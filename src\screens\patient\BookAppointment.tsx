import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  ProgressBar,
  List,
  Avatar,
  Chip,
  TextInput,
  Divider,
  RadioButton,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Calendar, DateData } from 'react-native-calendars';
import { format, addDays, parse } from 'date-fns';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../../contexts/AuthContext';
import { useAppointments } from '../../contexts/AppointmentContext';
import { useNavigation } from '@react-navigation/native';
import { supabase } from '../../../lib/supabase';
import {
  APPOINTMENT_TYPES,
  TIME_SLOTS,
  BOOKING_RULES,
  isClinicWorkingDay,
  calculateEndTime,
  formatTimeDisplay,
  APPOINTMENT_ERRORS,
  APPOINTMENT_SUCCESS,
} from '../../constants/appointmentConfig';

interface Dentist {
  id: string;
  name: string;
  email: string;
  specialization?: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
}

export const BookAppointment = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const { user } = useAuth();
  const { createAppointment, getAvailableSlots } = useAppointments();
  
  const [step, setStep] = useState(1);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [selectedDentist, setSelectedDentist] = useState<Dentist | null>(null);
  const [dentists, setDentists] = useState<Dentist[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [appointmentType, setAppointmentType] = useState('checkup');

  // Use appointment types from configuration
  const appointmentTypes = APPOINTMENT_TYPES.map(type => ({
    label: type.label,
    value: type.value,
    icon: type.value === 'checkup' ? 'tooth' : 
          type.value === 'cleaning' ? 'toothbrush' :
          type.value === 'filling' ? 'medical-bag' :
          type.value === 'emergency' ? 'ambulance' :
          type.value === 'consultation' ? 'comment-question' : 'tooth',
    color: type.color,
    duration: type.duration,
  }));

  useEffect(() => {
    fetchDentists();
  }, []);

  useEffect(() => {
    if (selectedDate && selectedDentist) {
      fetchTimeSlots();
    }
  }, [selectedDate, selectedDentist]);

  const fetchDentists = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email')
        .eq('role', 'dentist');

      if (error) throw error;

      // Map the data to use 'name' field for consistency
      const mappedDentists = (data || []).map((d: any) => ({
        id: d.id,
        name: d.name || 'Unknown',
        email: d.email
      }));

      setDentists(mappedDentists);
    } catch (error) {
      console.error('Error fetching dentists:', error);
      Alert.alert('Error', 'Failed to load dentists. Please try again.');
    }
  };

  const fetchTimeSlots = async () => {
    if (!selectedDentist?.id || !selectedDate) {
      console.log('Missing required data:', { selectedDentist, selectedDate });
      return;
    }
    
    setLoading(true);
    try {
      const slots = await getAvailableSlots(selectedDate, selectedDentist.id);
      
      if (!slots || !Array.isArray(slots)) {
        console.error('Invalid slots data received:', slots);
        setTimeSlots([]);
        return;
      }
      
      const formattedSlots = slots.map(slot => ({
        time: slot.startTime,
        available: slot.available,
      }));
      setTimeSlots(formattedSlots);
    } catch (error) {
      console.error('Error fetching time slots:', error);
      setTimeSlots([]);
      Alert.alert('Error', 'Failed to load available time slots. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDateSelect = (day: DateData) => {
    setSelectedDate(day.dateString);
    setSelectedTime('');
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
  };

  const handleDentistSelect = (dentist: Dentist) => {
    setSelectedDentist(dentist);
    setSelectedTime('');
  };

  const goToNextStep = async () => {
    if (step === 1 && !appointmentType) {
      Alert.alert('Please select appointment type');
      return;
    }
    if (step === 2 && !selectedDentist) {
      Alert.alert('Please select a dentist');
      return;
    }
    if (step === 3 && !selectedDate) {
      Alert.alert('Please select a date');
      return;
    }
    if (step === 4) {
      if (!selectedTime) {
        Alert.alert('Please select a time slot');
        return;
      }
      try {
        // Re-validate availability right before confirmation to avoid bad UX
        const latest = await getAvailableSlots(selectedDate, selectedDentist!.id);
        const slot = latest.find(s => s.startTime === selectedTime);
        if (!slot || !slot.available) {
          Alert.alert('Slot no longer available', 'Please choose another time.');
          // Refresh visible list
          await fetchTimeSlots();
          return; // stay on step 4
        }
      } catch (_) {
        // If we cannot validate, better to stop here than waste time on confirm
        Alert.alert('Unable to validate availability', 'Please try again.');
        return;
      }
    }
    setStep(step + 1);
  };

  const goToPreviousStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleBookAppointment = async () => {
    if (!selectedDate || !selectedTime || !selectedDentist) {
      Alert.alert('Error', 'Please complete all required fields');
      return;
    }

    setLoading(true);
    try {
      // Get appointment type configuration
      const selectedType = APPOINTMENT_TYPES.find(t => t.value === appointmentType);
      const duration = selectedType?.duration || 60;
      
      // Calculate end time using the configuration function
      const endTime = calculateEndTime(selectedTime, duration);

      await createAppointment({
        patient_id: user?.id,
        dentist_id: selectedDentist.id,
        date: selectedDate,
        start_time: selectedTime,
        end_time: endTime,
        notes: `Type: ${appointmentType}\n${notes}`,
      });

      Alert.alert(
        'Success',
        'Your appointment has been booked successfully!',
        [
          {
            text: 'View Appointments',
            onPress: () => navigation.navigate('MyAppointments'),
          },
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to book appointment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      <ProgressBar
        progress={step / 5}
        color={theme.colors.primary}
        style={styles.progressBar}
      />
      <Text style={styles.stepText}>Step {step} of 5</Text>
    </View>
  );

  const renderAppointmentType = () => (
    <View>
      <Title style={styles.stepTitle}>Select Appointment Type</Title>
      <RadioButton.Group
        onValueChange={setAppointmentType}
        value={appointmentType}
      >
        {appointmentTypes.map((type) => (
          <Card key={type.value} style={styles.typeCard}>
            <TouchableOpacity
              onPress={() => setAppointmentType(type.value)}
              style={styles.typeCardContent}
            >
              <View style={styles.typeCardLeft}>
                <MaterialCommunityIcons
                  name={type.icon as any}
                  size={24}
                  color={appointmentType === type.value ? theme.colors.primary : '#666'}
                />
                <Text style={styles.typeLabel}>{type.label}</Text>
              </View>
              <RadioButton value={type.value} />
            </TouchableOpacity>
          </Card>
        ))}
      </RadioButton.Group>
    </View>
  );

  const renderDentistSelection = () => (
    <View>
      <Title style={styles.stepTitle}>Select a Dentist</Title>
      {dentists.map((dentist) => (
        <Card
          key={dentist.id}
          style={[
            styles.dentistCard,
            selectedDentist?.id === dentist.id && styles.selectedCard,
          ]}
        >
          <TouchableOpacity onPress={() => handleDentistSelect(dentist)}>
            <Card.Content style={styles.dentistCardContent}>
              <Avatar.Text
                size={48}
                label={dentist.name.substring(0, 2).toUpperCase()}
              />
              <View style={styles.dentistInfo}>
                <Text style={styles.dentistName}>Dr. {dentist.name}</Text>
                <Text style={styles.dentistEmail}>{dentist.email}</Text>
              </View>
              {selectedDentist?.id === dentist.id && (
                <MaterialCommunityIcons
                  name="check-circle"
                  size={24}
                  color={theme.colors.primary}
                />
              )}
            </Card.Content>
          </TouchableOpacity>
        </Card>
      ))}
    </View>
  );

  const renderDateSelection = () => {
    const today = new Date().toISOString().split('T')[0];
    const maxDate = addDays(new Date(), BOOKING_RULES.MAX_ADVANCE_DAYS).toISOString().split('T')[0];

    return (
      <View>
        <Title style={styles.stepTitle}>Select a Date</Title>
        <Card style={styles.calendarCard}>
          <Calendar
            minDate={today}
            maxDate={maxDate}
            onDayPress={handleDateSelect}
            dayComponent={({ date, state }: any) => {
              const isWorkingDay = isClinicWorkingDay(new Date(date.dateString));
              return (
                <TouchableOpacity
                  onPress={() => isWorkingDay && handleDateSelect(date)}
                  disabled={!isWorkingDay || state === 'disabled'}
                >
                  <View style={{
                    width: 32,
                    height: 32,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: selectedDate === date.dateString ? theme.colors.primary : 'transparent',
                    borderRadius: 16,
                    opacity: isWorkingDay ? 1 : 0.3,
                  }}>
                    <Text style={{
                      color: selectedDate === date.dateString ? '#fff' : 
                             state === 'today' ? theme.colors.primary : 
                             isWorkingDay ? '#000' : '#999',
                      fontWeight: state === 'today' ? 'bold' : 'normal',
                    }}>
                      {date.day}
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            }}
            markedDates={{
              [selectedDate]: {
                selected: true,
                selectedColor: theme.colors.primary,
              },
            }}
            theme={{
              selectedDayBackgroundColor: theme.colors.primary,
              todayTextColor: theme.colors.primary,
              arrowColor: theme.colors.primary,
            }}
          />
        </Card>
        {selectedDate && (
          <Text style={styles.selectedDateText}>
            Selected: {format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')}
          </Text>
        )}
      </View>
    );
  };

  const renderTimeSelection = () => (
    <View>
      <Title style={styles.stepTitle}>Select a Time Slot</Title>
      {loading ? (
        <Text style={styles.loadingText}>Loading available slots...</Text>
      ) : (
        <View style={styles.timeSlotsContainer}>
          {timeSlots.map((slot) => (
            <Chip
              key={slot.time}
              selected={selectedTime === slot.time}
              onPress={() => slot.available && handleTimeSelect(slot.time)}
              disabled={!slot.available}
              style={[
                styles.timeSlot,
                !slot.available && styles.unavailableSlot,
                selectedTime === slot.time && styles.selectedSlot,
              ]}
            >
              {slot.time}
            </Chip>
          ))}
        </View>
      )}
      {selectedTime && (
        <Text style={styles.selectedTimeText}>Selected Time: {selectedTime}</Text>
      )}
    </View>
  );

  const renderConfirmation = () => (
    <View>
      <Title style={styles.stepTitle}>Confirm Your Appointment</Title>
      <Card style={styles.summaryCard}>
        <Card.Content>
          <List.Item
            title="Type"
            description={appointmentTypes.find(t => t.value === appointmentType)?.label}
            left={(props) => <List.Icon {...props} icon="medical-bag" />}
          />
          <Divider />
          <List.Item
            title="Dentist"
            description={`Dr. ${selectedDentist?.name}`}
            left={(props) => <List.Icon {...props} icon="doctor" />}
          />
          <Divider />
          <List.Item
            title="Date"
            description={selectedDate && format(new Date(selectedDate), 'EEEE, MMMM d, yyyy')}
            left={(props) => <List.Icon {...props} icon="calendar" />}
          />
          <Divider />
          <List.Item
            title="Time"
            description={selectedTime}
            left={(props) => <List.Icon {...props} icon="clock" />}
          />
        </Card.Content>
      </Card>

      <TextInput
        label="Additional Notes (Optional)"
        value={notes}
        onChangeText={setNotes}
        multiline
        numberOfLines={3}
        mode="outlined"
        style={styles.notesInput}
      />
    </View>
  );

  const renderStep = () => {
    switch (step) {
      case 1:
        return renderAppointmentType();
      case 2:
        return renderDentistSelection();
      case 3:
        return renderDateSelection();
      case 4:
        return renderTimeSelection();
      case 5:
        return renderConfirmation();
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {renderStepIndicator()}
        <View style={styles.content}>
          {renderStep()}
        </View>
      </ScrollView>
      
      <View style={styles.buttonContainer}>
        {step > 1 && (
          <Button
            mode="outlined"
            onPress={goToPreviousStep}
            style={styles.button}
          >
            Previous
          </Button>
        )}
        {step < 5 ? (
          <Button
            mode="contained"
            onPress={goToNextStep}
            style={styles.button}
            disabled={
              (step === 1 && !appointmentType) ||
              (step === 2 && !selectedDentist) ||
              (step === 3 && !selectedDate) ||
              (step === 4 && !selectedTime)
            }
          >
            Next
          </Button>
        ) : (
          <Button
            mode="contained"
            onPress={handleBookAppointment}
            style={styles.button}
            loading={loading}
            disabled={loading}
          >
            Book Appointment
          </Button>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    padding: 16,
  },
  stepIndicator: {
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  stepText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#666',
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  typeCard: {
    marginBottom: 12,
  },
  typeCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
  },
  typeCardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeLabel: {
    marginLeft: 12,
    fontSize: 16,
  },
  dentistCard: {
    marginBottom: 12,
  },
  selectedCard: {
    borderColor: '#2874ba',
    borderWidth: 2,
  },
  dentistCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dentistInfo: {
    marginLeft: 12,
    flex: 1,
  },
  dentistName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  dentistEmail: {
    fontSize: 14,
    color: '#666',
  },
  calendarCard: {
    marginBottom: 16,
  },
  selectedDateText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#2874ba',
    marginTop: 8,
  },
  timeSlotsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeSlot: {
    margin: 4,
    minWidth: '30%',
  },
  unavailableSlot: {
    opacity: 0.5,
  },
  selectedSlot: {
    backgroundColor: '#2874ba',
  },
  selectedTimeText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#2874ba',
    marginTop: 16,
  },
  loadingText: {
    textAlign: 'center',
    color: '#666',
    marginVertical: 20,
  },
  summaryCard: {
    marginBottom: 16,
  },
  notesInput: {
    marginTop: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 4,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});
