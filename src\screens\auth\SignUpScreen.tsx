import React, { useState } from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Image,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Title,
  Paragraph,
  RadioButton,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';

export const SignUpScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const theme = useTheme();
  const { signUp, loading } = useAuth();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [role, setRole] = useState<UserRole>(UserRole.PATIENT);

  const handleSignUp = async () => {
    if (!name || !email || !password) {
      alert('Please fill all required fields');
      return;
    }

    if (password !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      alert('Password must be at least 6 characters');
      return;
    }

    try {
      await signUp(email, password, name, role);
      navigation.goBack();
    } catch (error) {
      // Error is handled in the AuthContext
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Logo Header */}
          <View style={styles.logoSection}>
            <Image 
              source={require('../../../assets/drshilpas-logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
          
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.title}>Create New Account</Title>
              <Paragraph style={styles.subtitle}>
                Join our dental clinic community
              </Paragraph>

              <TextInput
                label="Full Name *"
                value={name}
                onChangeText={setName}
                mode="outlined"
                style={styles.input}
                left={<TextInput.Icon icon="account" />}
                disabled={loading}
              />

              <TextInput
                label="Email *"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                style={styles.input}
                left={<TextInput.Icon icon="email" />}
                disabled={loading}
              />

              <TextInput
                label="Phone Number"
                value={phone}
                onChangeText={setPhone}
                mode="outlined"
                keyboardType="phone-pad"
                style={styles.input}
                left={<TextInput.Icon icon="phone" />}
                disabled={loading}
              />

              <TextInput
                label="Password *"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                style={styles.input}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon
                    icon={showPassword ? 'eye-off' : 'eye'}
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                disabled={loading}
              />

              <TextInput
                label="Confirm Password *"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                mode="outlined"
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                style={styles.input}
                left={<TextInput.Icon icon="lock-check" />}
                disabled={loading}
              />

              <View style={styles.roleSection}>
                <Text style={styles.roleTitle}>Account Type:</Text>
                <RadioButton.Group
                  onValueChange={(value) => setRole(value as UserRole)}
                  value={role}
                >
                  <View style={styles.radioItem}>
                    <RadioButton value={UserRole.PATIENT} disabled={loading} />
                    <Text>Patient</Text>
                  </View>
                  <View style={styles.radioItem}>
                    <RadioButton value={UserRole.STAFF} disabled={loading} />
                    <Text>Staff</Text>
                  </View>
                  <View style={styles.radioItem}>
                    <RadioButton value={UserRole.DENTIST} disabled={loading} />
                    <Text>Dentist</Text>
                  </View>
                  <View style={styles.radioItem}>
                    <RadioButton value={UserRole.ADMIN} disabled={loading} />
                    <Text>Admin (Dev Only)</Text>
                  </View>
                </RadioButton.Group>
              </View>

              <Button
                mode="contained"
                onPress={handleSignUp}
                style={styles.signUpButton}
                contentStyle={styles.signUpButtonContent}
                disabled={loading}
                loading={loading}
              >
                {loading ? 'Creating Account...' : 'Sign Up'}
              </Button>

              <View style={styles.loginSection}>
                <Text style={styles.loginText}>Already have an account?</Text>
                <Button
                  mode="text"
                  onPress={() => navigation.goBack()}
                  disabled={loading}
                >
                  Sign In
                </Button>
              </View>
            </Card.Content>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logo: {
    width: 100,
    height: 100,
  },
  card: {
    marginTop: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 20,
  },
  input: {
    marginBottom: 16,
  },
  roleSection: {
    marginVertical: 16,
  },
  roleTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  signUpButton: {
    marginTop: 16,
    marginBottom: 16,
  },
  signUpButtonContent: {
    paddingVertical: 8,
  },
  loginSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    opacity: 0.7,
  },
});
