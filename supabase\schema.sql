-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('patient', 'staff', 'dentist', 'admin');
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
CREATE TYPE notification_type AS ENUM ('appointment_reminder', 'appointment_confirmation', 'appointment_cancellation', 'appointment_rescheduled', 'follow_up_reminder');
CREATE TYPE notification_status AS ENUM ('pending', 'sent', 'failed', 'delivered');
CREATE TYPE admin_action_type AS ENUM ('user_created', 'user_updated', 'user_deleted', 'role_changed', 'appointment_modified', 'appointment_cancelled', 'system_setting_changed', 'notification_triggered');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    phone TEXT,
    address TEXT,
    role user_role DEFAULT 'patient',
    status user_status DEFAULT 'active',
    avatar_url TEXT,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Appointments table
CREATE TABLE public.appointments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    staff_id UUID REFERENCES public.users(id),
    dentist_id UUID NOT NULL REFERENCES public.users(id),
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    notes TEXT,
    status appointment_status DEFAULT 'scheduled',
    followup_id UUID REFERENCES public.appointments(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT valid_time_range CHECK (start_time < end_time)
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES public.appointments(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    sent_at TIMESTAMPTZ,
    status notification_status DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Admin activity logs table
CREATE TABLE public.admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID NOT NULL REFERENCES public.users(id),
    action_type admin_action_type NOT NULL,
    target_id UUID,
    target_type TEXT,
    description TEXT NOT NULL,
    metadata JSONB,
    ip_address INET,
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- System settings table
CREATE TABLE public.system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clinic_name TEXT DEFAULT 'Dr. Shilpa''s Dental Clinic',
    clinic_address TEXT,
    clinic_phone TEXT,
    clinic_email TEXT,
    working_days TEXT[] DEFAULT ARRAY['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    working_hours JSONB DEFAULT '{"start": "10:00", "end": "18:00"}',
    slot_duration INTEGER DEFAULT 60, -- in minutes
    booking_advance_limit INTEGER DEFAULT 30, -- days in advance
    cancellation_advance_limit INTEGER DEFAULT 24, -- hours before appointment
    reminder_advance_time INTEGER DEFAULT 24, -- hours before appointment
    max_appointments_per_day INTEGER,
    notification_templates JSONB,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_appointments_patient_id ON public.appointments(patient_id);
CREATE INDEX idx_appointments_dentist_id ON public.appointments(dentist_id);
CREATE INDEX idx_appointments_date ON public.appointments(date);
CREATE INDEX idx_appointments_status ON public.appointments(status);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_appointment_id ON public.notifications(appointment_id);
CREATE INDEX idx_notifications_status ON public.notifications(status);
CREATE INDEX idx_admin_logs_admin_id ON public.admin_activity_logs(admin_id);
CREATE INDEX idx_admin_logs_timestamp ON public.admin_activity_logs(timestamp);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate appointment roles
CREATE OR REPLACE FUNCTION validate_appointment_roles()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if dentist_id has dentist role
  IF NOT EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = NEW.dentist_id AND role = 'dentist'
  ) THEN
    RAISE EXCEPTION 'dentist_id must reference a user with dentist role';
  END IF;
  
  -- Check if patient_id has patient role
  IF NOT EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = NEW.patient_id AND role = 'patient'
  ) THEN
    RAISE EXCEPTION 'patient_id must reference a user with patient role';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate admin activity logs
CREATE OR REPLACE FUNCTION validate_admin_activity()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if admin_id has admin role
  IF NOT EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = NEW.admin_id AND role = 'admin'
  ) THEN
    RAISE EXCEPTION 'admin_id must reference a user with admin role';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER set_timestamp_users
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_appointments
    BEFORE UPDATE ON public.appointments
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_notifications
    BEFORE UPDATE ON public.notifications
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_system_settings
    BEFORE UPDATE ON public.system_settings
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- Create validation triggers
CREATE TRIGGER validate_appointment_roles_trigger
    BEFORE INSERT OR UPDATE ON public.appointments
    FOR EACH ROW
    EXECUTE FUNCTION validate_appointment_roles();

CREATE TRIGGER validate_admin_activity_trigger
    BEFORE INSERT OR UPDATE ON public.admin_activity_logs
    FOR EACH ROW
    EXECUTE FUNCTION validate_admin_activity();

-- Row Level Security (RLS) Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Staff and admin can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role IN ('staff', 'admin')
        )
    );

CREATE POLICY "Admin can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Appointments policies
CREATE POLICY "Patients can view their own appointments" ON public.appointments
    FOR SELECT USING (patient_id = auth.uid());

CREATE POLICY "Patients can create their own appointments" ON public.appointments
    FOR INSERT WITH CHECK (patient_id = auth.uid());

CREATE POLICY "Patients can update their own appointments" ON public.appointments
    FOR UPDATE USING (patient_id = auth.uid());

CREATE POLICY "Dentists can view their appointments" ON public.appointments
    FOR SELECT USING (dentist_id = auth.uid());

CREATE POLICY "Staff can manage all appointments" ON public.appointments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role IN ('staff', 'admin')
        )
    );

-- Notifications policies
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Staff and admin can manage notifications" ON public.notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role IN ('staff', 'admin')
        )
    );

-- Admin activity logs policies
CREATE POLICY "Only admins can view activity logs" ON public.admin_activity_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

CREATE POLICY "Only admins can create activity logs" ON public.admin_activity_logs
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- System settings policies
CREATE POLICY "All authenticated users can view settings" ON public.system_settings
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Only admins can modify settings" ON public.system_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'New User'),
        COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'patient')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Insert default system settings
INSERT INTO public.system_settings (
    clinic_name,
    clinic_address,
    clinic_phone,
    clinic_email
) VALUES (
    'Dr. Shilpa''s Dental Clinic',
    '123 Main Street, City, State 12345',
    '+91 98765 43210',
    '<EMAIL>'
) ON CONFLICT DO NOTHING;
