import React from 'react'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { AuthProvider } from './contexts/AuthContext'
import { AppointmentProvider } from './contexts/AppointmentContext'
import { PatientProvider } from './contexts/PatientContext'
import AppRouter from './components/common/AppRouter'

const theme = createTheme({
  palette: {
    primary: {
      main: '#2874ba', // Brand blue from mobile app
    },
    secondary: {
      main: '#3dbc98', // Secondary color from mobile app
    },
    background: {
      default: '#f8f9fa',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
})

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        {/* Temporarily disabled other providers to test basic functionality */}
        {/* <AppointmentProvider>
          <PatientProvider> */}
            <AppRouter />
          {/* </PatientProvider>
        </AppointmentProvider> */}
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App