// Test Supabase connection
import { supabase } from './lib/supabase.js';

async function testConnection() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('system_settings')
      .select('clinic_name')
      .limit(1);
    
    if (error) {
      console.error('Connection error:', error);
    } else {
      console.log('✅ Supabase connected successfully');
      console.log('Data:', data);
    }
    
    // Test auth configuration
    const { data: authData, error: authError } = await supabase.auth.getSession();
    console.log('Auth session:', authData);
    
  } catch (err) {
    console.error('❌ Connection failed:', err);
  }
}

testConnection();
