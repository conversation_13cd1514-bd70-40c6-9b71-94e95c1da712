import React, { useState } from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Title,
  Paragraph,
  HelperText,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../contexts/AuthContext';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

export const ForgotPasswordScreen: React.FC<{ navigation: any }> = ({ navigation }) => {
  const theme = useTheme();
  const { resetPassword, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const validateEmail = () => {
    if (!email) {
      setError('Email is required');
      return false;
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }
    setError('');
    return true;
  };

  const handleResetPassword = async () => {
    if (validateEmail()) {
      try {
        await resetPassword(email);
        setSuccess(true);
      } catch (error) {
        // Error is handled in the AuthContext
        setSuccess(false);
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header Section */}
          <View style={styles.headerSection}>
            <View style={[styles.iconContainer, { backgroundColor: theme.colors.primaryContainer }]}>
              <MaterialCommunityIcons
                name="lock-reset"
                size={48}
                color={theme.colors.primary}
              />
            </View>
            <Title style={styles.title}>Reset Password</Title>
            <Paragraph style={styles.subtitle}>
              Enter your email address and we'll send you instructions to reset your password
            </Paragraph>
          </View>

          {/* Reset Form */}
          {!success ? (
            <Card style={styles.card}>
              <Card.Content>
                <TextInput
                  label="Email Address"
                  value={email}
                  onChangeText={(text) => {
                    setEmail(text);
                    setError('');
                  }}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  style={styles.input}
                  left={<TextInput.Icon icon="email" />}
                  disabled={loading}
                  error={!!error}
                />
                <HelperText type="error" visible={!!error}>
                  {error}
                </HelperText>

                <Button
                  mode="contained"
                  onPress={handleResetPassword}
                  style={styles.resetButton}
                  contentStyle={styles.resetButtonContent}
                  disabled={loading}
                  loading={loading}
                >
                  {loading ? 'Sending...' : 'Send Reset Instructions'}
                </Button>

                <Button
                  mode="text"
                  onPress={() => navigation.goBack()}
                  style={styles.backButton}
                  disabled={loading}
                  icon="arrow-left"
                >
                  Back to Login
                </Button>
              </Card.Content>
            </Card>
          ) : (
            <Card style={[styles.card, styles.successCard]}>
              <Card.Content>
                <View style={styles.successIconContainer}>
                  <MaterialCommunityIcons
                    name="check-circle"
                    size={64}
                    color={theme.colors.primary}
                  />
                </View>
                <Title style={styles.successTitle}>Check Your Email</Title>
                <Paragraph style={styles.successText}>
                  We've sent password reset instructions to:
                </Paragraph>
                <Text style={styles.emailText}>{email}</Text>
                <Paragraph style={styles.instructionText}>
                  Please check your email and follow the instructions to reset your password.
                  If you don't see the email, check your spam folder.
                </Paragraph>

                <Button
                  mode="contained"
                  onPress={() => navigation.goBack()}
                  style={styles.returnButton}
                  contentStyle={styles.returnButtonContent}
                >
                  Return to Login
                </Button>

                <Button
                  mode="text"
                  onPress={() => {
                    setSuccess(false);
                    setEmail('');
                  }}
                  style={styles.resendButton}
                >
                  Didn't receive email? Try again
                </Button>
              </Card.Content>
            </Card>
          )}

          {/* Help Section */}
          <Card style={styles.helpCard}>
            <Card.Content>
              <View style={styles.helpSection}>
                <MaterialCommunityIcons
                  name="help-circle-outline"
                  size={20}
                  color={theme.colors.primary}
                />
                <Text style={styles.helpTitle}>Need Help?</Text>
              </View>
              <Text style={styles.helpText}>
                If you're having trouble resetting your password, please contact our support team:
              </Text>
              <Text style={styles.contactText}>📧 <EMAIL></Text>
              <Text style={styles.contactText}>📞 +91 98765 43210</Text>
            </Card.Content>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 30,
    marginTop: 20,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    paddingHorizontal: 20,
  },
  card: {
    marginBottom: 20,
    elevation: 2,
  },
  successCard: {
    borderColor: '#4CAF50',
    borderWidth: 1,
  },
  input: {
    marginBottom: 8,
  },
  resetButton: {
    marginTop: 16,
    marginBottom: 8,
  },
  resetButtonContent: {
    paddingVertical: 8,
  },
  backButton: {
    marginTop: 8,
  },
  successIconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  successText: {
    textAlign: 'center',
    marginBottom: 8,
    opacity: 0.7,
  },
  emailText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#2874ba',
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 20,
  },
  returnButton: {
    marginBottom: 8,
  },
  returnButtonContent: {
    paddingVertical: 8,
  },
  resendButton: {
    marginTop: 8,
  },
  helpCard: {
    backgroundColor: 'rgba(46, 116, 186, 0.05)',
  },
  helpSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  helpTitle: {
    fontWeight: 'bold',
    marginLeft: 8,
    color: '#2874ba',
  },
  helpText: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 12,
  },
  contactText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#2874ba',
  },
});
