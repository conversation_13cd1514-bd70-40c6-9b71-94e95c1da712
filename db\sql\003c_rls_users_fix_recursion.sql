-- 003c_rls_users_fix_recursion.sql
-- Purpose: Replace recursive users policy with non-recursive version using helper function.
-- Requires: 004_fn_is_web_user.sql (creates public.is_web_user(uid uuid))

BEGIN;

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Drop previous policy if present
DO $$ BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname='public' AND tablename='users' AND policyname='users_select_for_receptionist'
  ) THEN
    DROP POLICY users_select_for_receptionist ON public.users;
  END IF;
  IF EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname='public' AND tablename='users' AND policyname='users_self_select'
  ) THEN
    DROP POLICY users_self_select ON public.users;
  END IF;
  IF EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname='public' AND tablename='users' AND policyname='users_select_doctors_patients_for_web_roles'
  ) THEN
    DROP POLICY users_select_doctors_patients_for_web_roles ON public.users;
  END IF;
END $$;

-- 1) Everyone can see their own row (needed for login to read role without recursion)
CREATE POLICY users_self_select
ON public.users
FOR SELECT
TO authenticated
USING (id = auth.uid());

-- 2) Web roles can see doctors/dentists/patients via helper function (no recursive select)
CREATE POLICY users_select_doctors_patients_for_web_roles
ON public.users
FOR SELECT
TO authenticated
USING (
  (role IN ('doctor','dentist','patient'))
  AND public.is_web_user(auth.uid())
);

COMMIT;

