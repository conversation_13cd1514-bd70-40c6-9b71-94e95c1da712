# Define screens to create
$screens = @(
    @{Path="src/screens/patient/PatientProfile.tsx"; Name="PatientProfile"; Title="Patient Profile"},
    @{Path="src/screens/staff/StaffDashboard.tsx"; Name="StaffDashboard"; Title="Staff Dashboard"},
    @{Path="src/screens/staff/ManageAppointments.tsx"; Name="ManageAppointments"; Title="Manage Appointments"},
    @{Path="src/screens/staff/PatientManagement.tsx"; Name="PatientManagement"; Title="Patient Management"},
    @{Path="src/screens/staff/StaffProfile.tsx"; Name="StaffProfile"; Title="Staff Profile"},
    @{Path="src/screens/dentist/DentistDashboard.tsx"; Name="DentistDashboard"; Title="Dentist Dashboard"},
    @{Path="src/screens/dentist/DentistSchedule.tsx"; Name="DentistSchedule"; Title="Dentist Schedule"},
    @{Path="src/screens/dentist/PatientRecords.tsx"; Name="PatientRecords"; Title="Patient Records"},
    @{Path="src/screens/dentist/DentistProfile.tsx"; Name="DentistProfile"; Title="Dentist Profile"},
    @{Path="src/screens/admin/AdminDashboard.tsx"; Name="AdminDashboard"; Title="Admin Dashboard"},
    @{Path="src/screens/admin/UserManagement.tsx"; Name="UserManagement"; Title="User Management"},
    @{Path="src/screens/admin/SystemSettings.tsx"; Name="SystemSettings"; Title="System Settings"},
    @{Path="src/screens/admin/ActivityLogs.tsx"; Name="ActivityLogs"; Title="Activity Logs"},
    @{Path="src/screens/admin/AdminProfile.tsx"; Name="AdminProfile"; Title="Admin Profile"},
    @{Path="src/screens/common/NotificationsScreen.tsx"; Name="NotificationsScreen"; Title="Notifications"},
    @{Path="src/screens/common/AppointmentDetails.tsx"; Name="AppointmentDetails"; Title="Appointment Details"}
)

# Template for placeholder screens
function Get-ScreenTemplate {
    param($Name, $Title)
    
    return @"
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

export const $Name = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text>$Title Screen</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
"@
}

# Create each screen file
foreach ($screen in $screens) {
    $content = Get-ScreenTemplate -Name $screen.Name -Title $screen.Title
    $content | Out-File -FilePath $screen.Path -Encoding UTF8
    Write-Host "Created $($screen.Path)"
}
