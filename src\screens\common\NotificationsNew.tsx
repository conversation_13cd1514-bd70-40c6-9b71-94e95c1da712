import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Alert,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  IconButton,
  Chip,
  Searchbar,
  List,
  Badge,
  Portal,
  Modal,
  Divider,
  FAB,
  SegmentedButtons,
  ActivityIndicator,
  Snackbar,
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { format, isToday, isYesterday, parseISO } from 'date-fns';
import { useNavigation } from '@react-navigation/native';
import { supabase } from '../../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import {
  Notification,
  NotificationType,
  NotificationStatus,
} from '../../types';

const NotificationsScreen = () => {
  const { user } = useAuth();
  const navigation = useNavigation<any>();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<NotificationType | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<NotificationStatus | 'all'>('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('notifications')
        .select(`
          *,
          appointment:appointments (
            id,
            date,
            start_time,
            end_time,
            appointment_type,
            status,
            patient:users!appointments_patient_id_fkey (
              id,
              name,
              email
            ),
            dentist:users!appointments_dentist_id_fkey (
              id,
              name
            )
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setNotifications(data || []);
      filterNotifications(data || [], searchQuery, selectedType, selectedStatus);
      
      // Count unread notifications
      const unread = (data || []).filter(n => n.status === 'pending').length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      showSnackbar('Failed to load notifications');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user, searchQuery, selectedType, selectedStatus]);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Filter notifications
  const filterNotifications = (
    data: Notification[],
    search: string,
    type: NotificationType | 'all',
    status: NotificationStatus | 'all'
  ) => {
    let filtered = [...data];

    // Filter by search query
    if (search) {
      filtered = filtered.filter(
        n =>
          n.title.toLowerCase().includes(search.toLowerCase()) ||
          n.message.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Filter by type
    if (type !== 'all') {
      filtered = filtered.filter(n => n.type === type);
    }

    // Filter by status
    if (status !== 'all') {
      filtered = filtered.filter(n => n.status === status);
    }

    setFilteredNotifications(filtered);
  };

  useEffect(() => {
    filterNotifications(notifications, searchQuery, selectedType, selectedStatus);
  }, [searchQuery, selectedType, selectedStatus, notifications]);

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ status: 'delivered' as NotificationStatus })
        .eq('id', notificationId);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId
            ? { ...n, status: 'delivered' as NotificationStatus }
            : n
        )
      );
      showSnackbar('Marked as read');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      showSnackbar('Failed to mark as read');
    }
  };

  // Mark multiple as read
  const markMultipleAsRead = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ status: 'delivered' as NotificationStatus })
        .in('id', selectedNotifications);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n =>
          selectedNotifications.includes(n.id)
            ? { ...n, status: 'delivered' as NotificationStatus }
            : n
        )
      );
      setSelectedNotifications([]);
      setSelectionMode(false);
      showSnackbar(`${selectedNotifications.length} notifications marked as read`);
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      showSnackbar('Failed to mark as read');
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId: string) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('notifications')
                .delete()
                .eq('id', notificationId);

              if (error) throw error;

              setNotifications(prev => prev.filter(n => n.id !== notificationId));
              showSnackbar('Notification deleted');
            } catch (error) {
              console.error('Error deleting notification:', error);
              showSnackbar('Failed to delete notification');
            }
          },
        },
      ]
    );
  };

  // Delete multiple notifications
  const deleteMultiple = async () => {
    Alert.alert(
      'Delete Notifications',
      `Are you sure you want to delete ${selectedNotifications.length} notifications?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('notifications')
                .delete()
                .in('id', selectedNotifications);

              if (error) throw error;

              setNotifications(prev =>
                prev.filter(n => !selectedNotifications.includes(n.id))
              );
              setSelectedNotifications([]);
              setSelectionMode(false);
              showSnackbar(`${selectedNotifications.length} notifications deleted`);
            } catch (error) {
              console.error('Error deleting notifications:', error);
              showSnackbar('Failed to delete notifications');
            }
          },
        },
      ]
    );
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      const unreadIds = notifications
        .filter(n => n.status === 'pending')
        .map(n => n.id);

      if (unreadIds.length === 0) {
        showSnackbar('No unread notifications');
        return;
      }

      const { error } = await supabase
        .from('notifications')
        .update({ status: 'delivered' as NotificationStatus })
        .in('id', unreadIds);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(n =>
          unreadIds.includes(n.id)
            ? { ...n, status: 'delivered' as NotificationStatus }
            : n
        )
      );
      showSnackbar('All notifications marked as read');
    } catch (error) {
      console.error('Error marking all as read:', error);
      showSnackbar('Failed to mark all as read');
    }
  };

  // Toggle notification selection
  const toggleSelection = (notificationId: string) => {
    setSelectedNotifications(prev =>
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  // Navigate to appointment
  const navigateToAppointment = (appointmentId: string) => {
    navigation.navigate('AppointmentDetails', { appointmentId });
  };

  // Show snackbar
  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = parseISO(dateString);
    if (isToday(date)) return 'Today';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM dd, yyyy');
  };

  // Get notification icon
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'appointment_reminder':
        return 'bell-ring';
      case 'appointment_confirmation':
        return 'check-circle';
      case 'appointment_cancellation':
        return 'cancel';
      case 'appointment_rescheduled':
        return 'calendar-refresh';
      case 'follow_up_reminder':
        return 'message-alert';
      default:
        return 'bell';
    }
  };

  // Get notification color
  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case 'appointment_reminder':
        return '#2196F3';
      case 'appointment_confirmation':
        return '#4CAF50';
      case 'appointment_cancellation':
        return '#F44336';
      case 'appointment_rescheduled':
        return '#FF9800';
      case 'follow_up_reminder':
        return '#9C27B0';
      default:
        return '#757575';
    }
  };

  // Get status color
  const getStatusColor = (status: NotificationStatus) => {
    switch (status) {
      case 'pending':
        return '#FF9800';
      case 'sent':
        return '#2196F3';
      case 'delivered':
        return '#4CAF50';
      case 'failed':
        return '#F44336';
      default:
        return '#757575';
    }
  };

  // Render notification item
  const renderNotificationItem = ({ item }: { item: Notification }) => {
    const isSelected = selectedNotifications.includes(item.id);
    const isUnread = item.status === 'pending';

    return (
      <TouchableOpacity
        onPress={() => {
          if (selectionMode) {
            toggleSelection(item.id);
          } else {
            if (isUnread) markAsRead(item.id);
            if (item.appointment_id) {
              navigateToAppointment(item.appointment_id);
            }
          }
        }}
        onLongPress={() => {
          setSelectionMode(true);
          toggleSelection(item.id);
        }}
      >
        <Card
          style={[
            styles.notificationCard,
            isSelected && styles.selectedCard,
            isUnread && styles.unreadCard,
          ]}
        >
          <Card.Content>
            <View style={styles.notificationHeader}>
              <View style={styles.notificationLeft}>
                {selectionMode ? (
                  <IconButton
                    icon={isSelected ? 'checkbox-marked' : 'checkbox-blank-outline'}
                    size={24}
                    onPress={() => toggleSelection(item.id)}
                  />
                ) : (
                  <View
                    style={[
                      styles.iconContainer,
                      { backgroundColor: getNotificationColor(item.type) + '20' },
                    ]}
                  >
                    <MaterialCommunityIcons
                      name={getNotificationIcon(item.type)}
                      size={24}
                      color={getNotificationColor(item.type)}
                    />
                  </View>
                )}
                <View style={styles.notificationContent}>
                  <View style={styles.titleRow}>
                    <Text style={[styles.notificationTitle, isUnread && styles.unreadText]}>
                      {item.title}
                    </Text>
                    {isUnread && <Badge size={8} style={styles.unreadBadge} />}
                  </View>
                  <Text style={styles.notificationMessage} numberOfLines={2}>
                    {item.message}
                  </Text>
                  <View style={styles.metaRow}>
                    <Chip
                      mode="flat"
                      compact
                      style={[
                        styles.typeChip,
                        { backgroundColor: getNotificationColor(item.type) + '20' },
                      ]}
                      textStyle={{ fontSize: 10 }}
                    >
                      {item.type.replace(/_/g, ' ')}
                    </Chip>
                    <Text style={styles.dateText}>
                      {formatDate(item.created_at)} • {format(parseISO(item.created_at), 'HH:mm')}
                    </Text>
                  </View>
                  {item.appointment && (
                    <TouchableOpacity
                      style={styles.appointmentInfo}
                      onPress={() => navigateToAppointment(item.appointment_id!)}
                    >
                      <MaterialCommunityIcons name="calendar" size={14} color="#666" />
                      <Text style={styles.appointmentText}>
                        {format(parseISO(item.appointment.date), 'MMM dd')} at{' '}
                        {item.appointment.start_time}
                      </Text>
                      <MaterialCommunityIcons name="chevron-right" size={14} color="#666" />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              {!selectionMode && (
                <IconButton
                  icon="delete"
                  size={20}
                  onPress={() => deleteNotification(item.id)}
                />
              )}
            </View>
          </Card.Content>
        </Card>
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MaterialCommunityIcons name="bell-off" size={64} color="#ccc" />
      <Text style={styles.emptyStateText}>No notifications</Text>
      <Text style={styles.emptyStateSubtext}>
        {searchQuery || selectedType !== 'all' || selectedStatus !== 'all'
          ? 'Try adjusting your filters'
          : "You're all caught up!"}
      </Text>
    </View>
  );

  // Render filter modal
  const renderFilterModal = () => (
    <Portal>
      <Modal
        visible={filterModalVisible}
        onDismiss={() => setFilterModalVisible(false)}
        contentContainerStyle={styles.modalContent}
      >
        <Title style={styles.modalTitle}>Filter Notifications</Title>
        
        <Text style={styles.filterLabel}>Type</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipRow}>
          <Chip
            selected={selectedType === 'all'}
            onPress={() => setSelectedType('all')}
            style={styles.filterChip}
          >
            All Types
          </Chip>
          <Chip
            selected={selectedType === NotificationType.APPOINTMENT_REMINDER}
            onPress={() => setSelectedType(NotificationType.APPOINTMENT_REMINDER)}
            style={styles.filterChip}
          >
            Reminders
          </Chip>
          <Chip
            selected={selectedType === NotificationType.APPOINTMENT_CONFIRMATION}
            onPress={() => setSelectedType(NotificationType.APPOINTMENT_CONFIRMATION)}
            style={styles.filterChip}
          >
            Confirmations
          </Chip>
          <Chip
            selected={selectedType === NotificationType.APPOINTMENT_CANCELLATION}
            onPress={() => setSelectedType(NotificationType.APPOINTMENT_CANCELLATION)}
            style={styles.filterChip}
          >
            Cancellations
          </Chip>
          <Chip
            selected={selectedType === NotificationType.APPOINTMENT_RESCHEDULED}
            onPress={() => setSelectedType(NotificationType.APPOINTMENT_RESCHEDULED)}
            style={styles.filterChip}
          >
            Rescheduled
          </Chip>
          <Chip
            selected={selectedType === NotificationType.FOLLOW_UP_REMINDER}
            onPress={() => setSelectedType(NotificationType.FOLLOW_UP_REMINDER)}
            style={styles.filterChip}
          >
            Follow-ups
          </Chip>
        </ScrollView>

        <Text style={styles.filterLabel}>Status</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipRow}>
          <Chip
            selected={selectedStatus === 'all'}
            onPress={() => setSelectedStatus('all')}
            style={styles.filterChip}
          >
            All Status
          </Chip>
          <Chip
            selected={selectedStatus === NotificationStatus.PENDING}
            onPress={() => setSelectedStatus(NotificationStatus.PENDING)}
            style={styles.filterChip}
          >
            Unread
          </Chip>
          <Chip
            selected={selectedStatus === NotificationStatus.DELIVERED}
            onPress={() => setSelectedStatus(NotificationStatus.DELIVERED)}
            style={styles.filterChip}
          >
            Read
          </Chip>
          <Chip
            selected={selectedStatus === NotificationStatus.SENT}
            onPress={() => setSelectedStatus(NotificationStatus.SENT)}
            style={styles.filterChip}
          >
            Sent
          </Chip>
          <Chip
            selected={selectedStatus === NotificationStatus.FAILED}
            onPress={() => setSelectedStatus(NotificationStatus.FAILED)}
            style={styles.filterChip}
          >
            Failed
          </Chip>
        </ScrollView>

        <View style={styles.modalActions}>
          <Button
            mode="outlined"
            onPress={() => {
              setSelectedType('all');
              setSelectedStatus('all');
              setFilterModalVisible(false);
            }}
          >
            Reset
          </Button>
          <Button mode="contained" onPress={() => setFilterModalVisible(false)}>
            Apply
          </Button>
        </View>
      </Modal>
    </Portal>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header with search and actions */}
      <View style={styles.header}>
        <Searchbar
          placeholder="Search notifications..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
        <View style={styles.headerActions}>
          {unreadCount > 0 && (
            <Badge style={styles.unreadCountBadge}>{unreadCount}</Badge>
          )}
          <IconButton
            icon="filter"
            size={24}
            onPress={() => setFilterModalVisible(true)}
          />
          <IconButton
            icon="check-all"
            size={24}
            onPress={markAllAsRead}
            disabled={unreadCount === 0}
          />
          {selectionMode && (
            <IconButton
              icon="close"
              size={24}
              onPress={() => {
                setSelectionMode(false);
                setSelectedNotifications([]);
              }}
            />
          )}
        </View>
      </View>

      {/* Active filters */}
      {(selectedType !== 'all' || selectedStatus !== 'all') && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.activeFilters}
        >
          {selectedType !== 'all' && (
            <Chip
              onClose={() => setSelectedType('all')}
              style={styles.activeFilterChip}
            >
              Type: {selectedType.replace(/_/g, ' ')}
            </Chip>
          )}
          {selectedStatus !== 'all' && (
            <Chip
              onClose={() => setSelectedStatus('all')}
              style={styles.activeFilterChip}
            >
              Status: {selectedStatus}
            </Chip>
          )}
        </ScrollView>
      )}

      {/* Selection actions */}
      {selectionMode && selectedNotifications.length > 0 && (
        <View style={styles.selectionActions}>
          <Text style={styles.selectionText}>
            {selectedNotifications.length} selected
          </Text>
          <View style={styles.selectionButtons}>
            <Button
              mode="outlined"
              onPress={markMultipleAsRead}
              style={styles.selectionButton}
            >
              Mark as Read
            </Button>
            <Button
              mode="outlined"
              onPress={deleteMultiple}
              style={styles.selectionButton}
              textColor="#F44336"
            >
              Delete
            </Button>
          </View>
        </View>
      )}

      {/* Notifications list */}
      <FlatList
        data={filteredNotifications}
        renderItem={renderNotificationItem}
        keyExtractor={item => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={() => {
            setRefreshing(true);
            fetchNotifications();
          }} />
        }
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={filteredNotifications.length === 0 ? styles.emptyListContent : undefined}
      />

      {/* Filter modal */}
      {renderFilterModal()}

      {/* Snackbar */}
      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  searchBar: {
    flex: 1,
    marginRight: 8,
    elevation: 0,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  unreadCountBadge: {
    position: 'absolute',
    top: 8,
    right: 45,
    backgroundColor: '#F44336',
    zIndex: 1,
  },
  activeFilters: {
    padding: 8,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  activeFilterChip: {
    marginHorizontal: 4,
  },
  selectionActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  selectionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  selectionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  selectionButton: {
    marginHorizontal: 4,
  },
  notificationCard: {
    margin: 8,
    marginHorizontal: 16,
    elevation: 1,
  },
  selectedCard: {
    backgroundColor: '#E3F2FD',
  },
  unreadCard: {
    backgroundColor: '#FFF9E6',
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  notificationLeft: {
    flexDirection: 'row',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  unreadText: {
    fontWeight: '600',
  },
  unreadBadge: {
    backgroundColor: '#FF9800',
    marginLeft: 8,
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  typeChip: {
    height: 20,
  },
  dateText: {
    fontSize: 12,
    color: '#999',
  },
  appointmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  appointmentText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
  },
  chipRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 24,
  },
});

export default NotificationsScreen;
