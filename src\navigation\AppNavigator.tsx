import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { useTheme, <PERSON>u, Divider, IconButton, Avatar } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { View, Alert } from 'react-native';

import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import { HeaderProfile } from '../components/common/HeaderProfile';
import { HeaderBrand } from '../components/common/HeaderBrand';
import { LoadingOverlay } from '../components/common/LoadingOverlay';

// Auth Screens
import { LoginScreen } from '../screens/auth/LoginScreen';
import { SignUpScreen } from '../screens/auth/SignUpScreen';
import { ForgotPasswordScreen } from '../screens/auth/ForgotPasswordScreen';

// Patient Screens
import { PatientDashboard } from '../screens/patient/PatientDashboard';
import { BookAppointment } from '../screens/patient/BookAppointment';
import { MyAppointments } from '../screens/patient/MyAppointments';
import { PatientProfile } from '../screens/patient/PatientProfile';

// Staff Screens
import { StaffDashboard } from '../screens/staff/StaffDashboard';
import { ManageAppointments } from '../screens/staff/ManageAppointments';
import { PatientManagement } from '../screens/staff/PatientManagement';
import { StaffProfile } from '../screens/staff/StaffProfile';

// Dentist Screens
import { DentistDashboard } from '../screens/dentist/DentistDashboard';
import { DentistSchedule } from '../screens/dentist/DentistSchedule';
import { PatientRecords } from '../screens/dentist/PatientRecords';
import { DentistProfile } from '../screens/dentist/DentistProfile';

// Admin Screens
import { AdminDashboard } from '../screens/admin/AdminDashboard';
import { UserManagement } from '../screens/admin/UserManagementNew';
import { SystemSettings } from '../screens/admin/SystemSettingsNew';
import { ActivityLogs } from '../screens/admin/ActivityLogsNew';
import { AdminProfile } from '../screens/admin/AdminProfile';
import { ManageAppointments as AdminManageAppointments } from '../screens/admin/ManageAppointments';

// Common Screens
import { NotificationsScreen } from '../screens/common/NotificationsScreen';
import { AppointmentDetails } from '../screens/common/AppointmentDetails';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();
const Drawer = createDrawerNavigator();

// Auth Stack Navigator
const AuthNavigator = () => {
  const theme = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerLeft: () => <HeaderBrand />,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="SignUp"
        component={SignUpScreen}
        options={{ title: 'Create Account' }}
      />
      <Stack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen}
        options={{ title: 'Reset Password' }}
      />
    </Stack.Navigator>
  );
};

// Patient Tab Navigator
const PatientTabNavigator = () => {
  const theme = useTheme();
  const { user, signOut } = useAuth();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'home';
              break;
            case 'BookAppointment':
              iconName = 'calendar-plus';
              break;
            case 'MyAppointments':
              iconName = 'calendar-check';
              break;
            case 'Notifications':
              iconName = 'bell';
              break;
            case 'Profile':
              iconName = 'account';
              break;
            default:
              iconName = 'circle';
          }

          return (
            <MaterialCommunityIcons
              name={iconName}
              size={size}
              color={color}
            />
          );
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerLeft: () => <HeaderBrand />,
        headerRight: () => <HeaderProfile />,
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={PatientDashboard}
        options={{ title: 'Home' }}
      />
      <Tab.Screen
        name="BookAppointment"
        component={BookAppointment}
        options={{ title: 'Book' }}
      />
      <Tab.Screen
        name="MyAppointments"
        component={MyAppointments}
        options={{ title: 'Appointments' }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{ title: 'Alerts' }}
      />
      <Tab.Screen
        name="Profile"
        component={PatientProfile}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

// Staff Tab Navigator
const StaffTabNavigator = () => {
  const theme = useTheme();
  const { user, signOut } = useAuth();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'view-dashboard';
              break;
            case 'Appointments':
              iconName = 'calendar-multiple';
              break;
            case 'Patients':
              iconName = 'account-group';
              break;
            case 'Notifications':
              iconName = 'bell';
              break;
            case 'Profile':
              iconName = 'account';
              break;
            default:
              iconName = 'circle';
          }

          return (
            <MaterialCommunityIcons
              name={iconName}
              size={size}
              color={color}
            />
          );
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerLeft: () => <HeaderBrand />,
        headerRight: () => <HeaderProfile />,
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={StaffDashboard}
        options={{ title: 'Dashboard' }}
      />
      <Tab.Screen
        name="Appointments"
        component={ManageAppointments}
        options={{ title: 'Appointments' }}
      />
      <Tab.Screen
        name="Patients"
        component={PatientManagement}
        options={{ title: 'Patients' }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{ title: 'Alerts' }}
      />
      <Tab.Screen
        name="Profile"
        component={StaffProfile}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

// Dentist Tab Navigator
const DentistTabNavigator = () => {
  const theme = useTheme();
  const { user, signOut } = useAuth();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'view-dashboard';
              break;
            case 'Schedule':
              iconName = 'calendar-clock';
              break;
            case 'Patients':
              iconName = 'folder-account';
              break;
            case 'Notifications':
              iconName = 'bell';
              break;
            case 'Profile':
              iconName = 'account';
              break;
            default:
              iconName = 'circle';
          }

          return (
            <MaterialCommunityIcons
              name={iconName}
              size={size}
              color={color}
            />
          );
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerLeft: () => <HeaderBrand />,
        headerRight: () => <HeaderProfile />,
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DentistDashboard}
        options={{ title: 'Dashboard' }}
      />
      <Tab.Screen
        name="Schedule"
        component={DentistSchedule}
        options={{ title: 'Schedule' }}
      />
      <Tab.Screen
        name="Patients"
        component={PatientRecords}
        options={{ title: 'Records' }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{ title: 'Alerts' }}
      />
      <Tab.Screen
        name="Profile"
        component={DentistProfile}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

// Admin Drawer Navigator
const AdminNavigator = () => {
  const theme = useTheme();
  const { user, signOut } = useAuth();

  return (
    <Drawer.Navigator
      screenOptions={{
        drawerActiveTintColor: theme.colors.primary,
        drawerInactiveTintColor: 'gray',
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#fff',
        headerLeft: () => <HeaderBrand />,
        headerRight: () => <HeaderProfile />,
      }}
    >
      <Drawer.Screen
        name="Dashboard"
        component={AdminDashboard}
        options={{
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="view-dashboard"
              color={color}
              size={size}
            />
          ),
        }}
      />
      <Drawer.Screen
        name="UserManagement"
        component={UserManagement}
        options={{
          title: 'User Management',
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="account-multiple-plus"
              color={color}
              size={size}
            />
          ),
        }}
      />
      <Drawer.Screen
        name="Appointments"
        component={AdminManageAppointments}
        options={{
          title: 'All Appointments',
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="calendar-multiple"
              color={color}
              size={size}
            />
          ),
        }}
      />
      <Drawer.Screen
        name="SystemSettings"
        component={SystemSettings}
        options={{
          title: 'System Settings',
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="cog"
              color={color}
              size={size}
            />
          ),
        }}
      />
      <Drawer.Screen
        name="ActivityLogs"
        component={ActivityLogs}
        options={{
          title: 'Activity Logs',
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="file-document"
              color={color}
              size={size}
            />
          ),
        }}
      />
      <Drawer.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          title: 'Notifications',
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="bell"
              color={color}
              size={size}
            />
          ),
        }}
      />
      <Drawer.Screen
        name="Profile"
        component={AdminProfile}
        options={{
          title: 'My Profile',
          drawerIcon: ({ color, size }) => (
            <MaterialCommunityIcons
              name="account"
              color={color}
              size={size}
            />
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

// Main App Navigator
export const AppNavigator = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingOverlay message="Signing you in..." fullscreen />;
  }

  return (
    <NavigationContainer>
      {!user ? (
        <AuthNavigator />
      ) : (
        <>
          {user.role === UserRole.PATIENT && <PatientTabNavigator />}
          {user.role === UserRole.STAFF && <StaffTabNavigator />}
          {user.role === UserRole.DENTIST && <DentistTabNavigator />}
          {user.role === UserRole.ADMIN && <AdminNavigator />}
        </>
      )}
    </NavigationContainer>
  );
};
