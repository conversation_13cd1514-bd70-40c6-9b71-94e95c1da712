import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { supabase } from '../../lib/supabase';
import { format, subHours } from 'date-fns';

// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

class NotificationService {
  private notificationListener: any;
  private responseListener: any;

  async initialize() {
    try {
      // Check if we're in development mode
      const isDevelopment = __DEV__;
      
      if (isDevelopment) {
        console.log('📱 Notification Service: Running in development mode');
        console.log('ℹ️ Push notifications are limited in Expo Go');
        console.log('✅ Local notifications will still work');
      }
      
      // Request permissions
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.log('Notification permissions not granted');
        return;
      }

      // Try to get push token (will return null in Expo Go)
      const token = await this.registerForPushNotifications();
      if (token) {
        await this.savePushToken(token);
        console.log('✅ Push notifications configured');
      } else {
        console.log('ℹ️ Using local notifications only');
      }

      // Set up listeners (works for both local and push notifications)
      this.setupListeners();
      console.log('✅ Notification listeners configured');
    } catch (error: any) {
      console.log('Notification initialization error (non-critical):', error.message);
      // Don't throw - notifications are not critical for app functionality
    }
  }

  async requestPermissions(): Promise<boolean> {
    if (!Device.isDevice) {
      console.log('Push notifications only work on physical devices');
      return false;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    return finalStatus === 'granted';
  }

  async registerForPushNotifications(): Promise<string | null> {
    try {
      // Check if we're in Expo Go (development) or a standalone build
      const isExpoGo = !Device.isDevice || __DEV__;
      
      if (isExpoGo) {
        console.log('Running in Expo Go - Push notifications limited');
        // In Expo Go, we can still use local notifications
        return null;
      }
      
      // Only try to get push token in production/standalone builds
      const projectId = 'your-project-id'; // Replace with actual project ID
      if (projectId === 'your-project-id') {
        console.log('Push notifications require a valid project ID');
        return null;
      }
      
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      });
      console.log('Push token:', token.data);
      return token.data;
    } catch (error: any) {
      console.log('Push token registration skipped:', error.message);
      // Don't throw error, just return null
      return null;
    }
  }

  async savePushToken(token: string) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('users')
          .update({ push_token: token })
          .eq('id', user.id);
      }
    } catch (error) {
      console.error('Error saving push token:', error);
    }
  }

  setupListeners() {
    // Handle notifications when app is in foreground
    this.notificationListener = Notifications.addNotificationReceivedListener(
      notification => {
        console.log('Notification received:', notification);
      }
    );

    // Handle notification taps
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      response => {
        console.log('Notification tapped:', response);
        this.handleNotificationResponse(response);
      }
    );
  }

  handleNotificationResponse(response: Notifications.NotificationResponse) {
    const data = response.notification.request.content.data;
    
    if (data.type === 'appointment_reminder') {
      // Navigate to appointment details
      // You'll need to pass navigation prop or use a navigation service
      console.log('Navigate to appointment:', data.appointmentId);
    }
  }

  // Schedule local notification for appointment reminder
  async scheduleAppointmentReminder(appointment: any) {
    const appointmentDate = new Date(`${appointment.date}T${appointment.start_time}`);
    const reminderDate = subHours(appointmentDate, 24); // 24 hours before

    if (reminderDate > new Date()) {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🦷 Appointment Reminder',
          body: `You have an appointment tomorrow at ${appointment.start_time} with Dr. ${appointment.dentist?.name}`,
          data: {
            type: 'appointment_reminder',
            appointmentId: appointment.id,
          },
        },
        trigger: {
          type: Notifications.SchedulableTriggerInputTypes.DATE,
          date: reminderDate,
        },
      });

      // Save notification record in database
      await this.saveNotificationRecord(appointment.id, 'appointment_reminder', reminderDate);
    }
  }

  // Send immediate notification
  async sendImmediateNotification(title: string, body: string, data?: any) {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: null, // Immediate
    });
  }

  // Cancel scheduled notification
  async cancelScheduledNotification(identifier: string) {
    await Notifications.cancelScheduledNotificationAsync(identifier);
  }

  // Get all scheduled notifications
  async getScheduledNotifications() {
    return await Notifications.getAllScheduledNotificationsAsync();
  }

  // Save notification record to database
  async saveNotificationRecord(appointmentId: string, type: string, scheduledFor: Date) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase.from('notifications').insert({
          user_id: user.id,
          appointment_id: appointmentId,
          type,
          title: 'Appointment Reminder',
          message: 'You have an upcoming appointment',
          status: 'pending',
        });
      }
    } catch (error) {
      console.error('Error saving notification record:', error);
    }
  }

  // Send notification to user (server-side function)
  async sendPushNotification(expoPushToken: string, title: string, body: string, data?: any) {
    const message = {
      to: expoPushToken,
      sound: 'default',
      title,
      body,
      data,
    };

    await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
  }

  // Clean up listeners
  cleanup() {
    try {
      this.notificationListener?.remove?.();
    } catch {}
    try {
      this.responseListener?.remove?.();
    } catch {}
  }
}

export default new NotificationService();
