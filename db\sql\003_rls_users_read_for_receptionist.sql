-- 003_rls_users_read_for_receptionist.sql
-- Purpose: Allow receptionist (and existing admin/staff/doctor) to read minimal user info
--          for doctors and patients, and always their own user row.
-- Safe to run multiple times; uses IF NOT EXISTS checks for policies.

BEGIN;

-- Ensure RLS is enabled (idempotent)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Receptionist read policy (doctors, patients, and own row)
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='users' AND policyname='users_select_for_receptionist'
  ) THEN
    CREATE POLICY users_select_for_receptionist
    ON public.users
    FOR SELECT
    TO authenticated
    USING (
      -- Caller must be one of these roles
      EXISTS (
        SELECT 1 FROM public.users me
        WHERE me.id = auth.uid()
          AND me.role IN ('admin','staff','doctor','receptionist')
      )
      AND (
        -- Receptionist can see doctors and patients generally
        role IN ('doctor','patient')
        -- and everyone can always see their own row
        OR id = auth.uid()
      )
    );
  END IF;
END $$;

COMMIT;

