import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { supabase } from '../services/supabase'
import { User, AuthContextType, UserRole, UserStatus } from '../types'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  
  // Removed debug mode - using proper authentication

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    // Check active sessions and sets the user
    const checkUser = async () => {
      try {
        setLoading(true)

        // Set a timeout to prevent infinite loading
        timeoutId = setTimeout(() => {
          console.log('Authentication timeout - falling back to login')
          setLoading(false)
          setUser(null)
          setSession(null)
        }, 10000) // 10 second timeout

        // Debug mode removed - using proper authentication only

        console.log('Checking user session...')
        console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL)

        // Test Supabase connection first
        const { data: testData, error: testError } = await supabase
          .from('users')
          .select('count')
          .limit(1)

        if (testError) {
          console.error('Supabase connection test failed:', testError)
          throw new Error('Cannot connect to database. Please check your connection.')
        }

        console.log('Supabase connection successful')

        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Session error:', error)
          throw error
        }

        console.log('Session data:', session)

        if (session) {
          setSession(session)
          console.log('Fetching user profile for ID:', session.user.id)

          // Fetch user profile from users table
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()

          if (userError) {
            console.error('User fetch error:', userError)
            // If user doesn't exist in users table, create it
            if (userError.code === 'PGRST116') { // No rows returned
              console.log('User not found in users table, creating...')
              const { data: newUser, error: createError } = await supabase
                .from('users')
                .insert({
                  id: session.user.id,
                  email: session.user.email,
                  name: session.user.user_metadata?.name || 'Unknown User',
                  role: session.user.user_metadata?.role || 'staff',
                  status: 'active',
                })
                .select()
                .single()

              if (createError) {
                console.error('User creation error:', createError)
                throw createError
              }

              console.log('User created:', newUser)
              setUser(newUser)
            } else {
              throw userError
            }
          } else {
            console.log('User data found:', userData)
            setUser(userData)
          }
        } else {
          console.log('No session found')
          setUser(null)
          setSession(null)
        }

        clearTimeout(timeoutId)
      } catch (error) {
        console.error('Error checking user:', error)
        clearTimeout(timeoutId)
        setUser(null)
        setSession(null)
      } finally {
        setLoading(false)
      }
    }

    checkUser()

    // Listen for changes on auth state (logged in, signed out, etc.)
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session)
      if (session) {
        setSession(session)
        // Fetch user profile
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single()

        if (!error && userData) {
          setUser(userData)
        }
      } else {
        setUser(null)
        setSession(null)
      }
      setLoading(false)
    })

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      
      if (error) throw error
      
      if (data.user) {
        // Fetch user profile
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .single()
        
        if (userError) throw userError
        
        // Check if user has staff or admin role
        if (userData.role !== UserRole.STAFF && userData.role !== UserRole.ADMIN) {
          await supabase.auth.signOut()
          throw new Error('Access denied. Staff or Admin access required.')
        }
        
        setUser(userData)
        setSession(data.session)
      }
    } catch (error: any) {
      console.error('Sign in error:', error)
      throw error
    }
  }

  const signUp = async (email: string, password: string, name: string, role: UserRole = UserRole.PATIENT) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            role,
          },
        },
      })
      
      if (error) throw error
      
      if (data.user) {
        setUser({
          id: data.user.id,
          email: data.user.email!,
          name,
          role,
          status: UserStatus.ACTIVE,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        setSession(data.session)
      }
    } catch (error: any) {
      console.error('Sign up error:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      
      setUser(null)
      setSession(null)
    } catch (error: any) {
      console.error('Sign out error:', error)
      throw error
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })
      
      if (error) throw error
    } catch (error: any) {
      console.error('Reset password error:', error)
      throw error
    }
  }

  const updateProfile = async (updates: Partial<User>) => {
    try {
      if (!user) throw new Error('No user logged in')
      
      const { error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)
      
      if (error) throw error
      
      setUser({ ...user, ...updates })
    } catch (error: any) {
      console.error('Update profile error:', error)
      throw error
    }
  }

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}