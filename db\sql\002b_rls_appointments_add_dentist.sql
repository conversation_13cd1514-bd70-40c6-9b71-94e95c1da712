-- 002b_rls_appointments_add_dentist.sql
-- Purpose: Update appointments RLS policies to include 'dentist' alongside 'doctor'.
-- Idempotent: Drops old policies if present and recreates with expanded role set.

BEGIN;

-- Ensure RLS is enabled (idempotent)
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies created in 002 if present
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='appointments' AND policyname='app_select_admin_staff_doctor_receptionist') THEN
    DROP POLICY app_select_admin_staff_doctor_receptionist ON public.appointments;
  END IF;
  IF EXISTS (SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='appointments' AND policyname='app_insert_admin_staff_doctor_receptionist') THEN
    DROP POLICY app_insert_admin_staff_doctor_receptionist ON public.appointments;
  END IF;
  IF EXISTS (SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='appointments' AND policyname='app_update_admin_staff_doctor_receptionist') THEN
    DROP POLICY app_update_admin_staff_doctor_receptionist ON public.appointments;
  END IF;
END $$;

-- Recreate with dentist included
CREATE POLICY app_select_admin_staff_doctor_dentist_receptionist
ON public.appointments
FOR SELECT
TO authenticated
USING (
  auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
      AND u.role IN ('admin','staff','doctor','dentist','receptionist')
  )
);

CREATE POLICY app_insert_admin_staff_doctor_dentist_receptionist
ON public.appointments
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
      AND u.role IN ('admin','staff','doctor','dentist','receptionist')
  )
);

CREATE POLICY app_update_admin_staff_doctor_dentist_receptionist
ON public.appointments
FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
      AND u.role IN ('admin','staff','doctor','dentist','receptionist')
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
      AND u.role IN ('admin','staff','doctor','dentist','receptionist')
  )
);

COMMIT;

