-- 005_appointments_standardize_schema.sql
-- Purpose: Standardize appointments schema and add generated helper columns
-- - Keep canonical columns: date (DATE), start_time (TIME), end_time (TIME)
-- - Ensure appointment_date and appointment_time are generated from canonical columns
-- - Ensure duration_minutes exists with default 60
-- - Add generated timestamps and range columns for overlap checks

BEGIN;

-- Ensure duration_minutes exists
ALTER TABLE public.appointments
  ADD COLUMN IF NOT EXISTS duration_minutes INTEGER DEFAULT 60;

-- appointment_date: generated from date
DO $$
DECLARE gen text;
BEGIN
  SELECT is_generated FROM information_schema.columns
  WHERE table_schema='public' AND table_name='appointments' AND column_name='appointment_date'
  INTO gen;

  IF gen IS DISTINCT FROM 'ALWAYS' THEN
    -- Drop and recreate as generated stored (safe: value derives from date)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_schema='public' AND table_name='appointments' AND column_name='appointment_date'
    ) THEN
      ALTER TABLE public.appointments DROP COLUMN appointment_date;
    END IF;
    ALTER TABLE public.appointments
      ADD COLUMN appointment_date DATE GENERATED ALWAYS AS (date) STORED;
  END IF;
END $$;

-- appointment_time: generated from start_time
DO $$
DECLARE gen2 text;
BEGIN
  SELECT is_generated FROM information_schema.columns
  WHERE table_schema='public' AND table_name='appointments' AND column_name='appointment_time'
  INTO gen2;

  IF gen2 IS DISTINCT FROM 'ALWAYS' THEN
    IF EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_schema='public' AND table_name='appointments' AND column_name='appointment_time'
    ) THEN
      ALTER TABLE public.appointments DROP COLUMN appointment_time;
    END IF;
    ALTER TABLE public.appointments
      ADD COLUMN appointment_time TIME GENERATED ALWAYS AS (start_time) STORED;
  END IF;
END $$;

-- Helper timestamps composed from date + time
ALTER TABLE public.appointments
  ADD COLUMN IF NOT EXISTS start_at TIMESTAMP GENERATED ALWAYS AS (date::timestamp + start_time) STORED,
  ADD COLUMN IF NOT EXISTS end_at   TIMESTAMP GENERATED ALWAYS AS (date::timestamp + end_time) STORED;

-- Effective range (NULL for cancelled/no_show so they don't block)
ALTER TABLE public.appointments
  ADD COLUMN IF NOT EXISTS effective_appt_range TSRANGE
  GENERATED ALWAYS AS (
    CASE WHEN status NOT IN ('cancelled','no_show')
      THEN tsrange((date::timestamp + start_time), (date::timestamp + end_time), '[)')
      ELSE NULL
    END
  ) STORED;

COMMIT;

