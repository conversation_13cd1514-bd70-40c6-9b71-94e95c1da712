import React from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { useTheme } from 'react-native-paper';

export const HeaderBrand = () => {
  const theme = useTheme();
  return (
    <View style={styles.container}>
      <View style={[styles.logoWrap, { backgroundColor: '#fff', borderColor: 'rgba(0,0,0,0.06)' }]}>
        <Image
          source={require('../../../assets/drshilpas-logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginLeft: 12,
    marginRight: 10,
  },
  logoWrap: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    borderWidth: 1,
  },
  logo: {
    width: 32,
    height: 32,
  },
});

export default HeaderBrand;

