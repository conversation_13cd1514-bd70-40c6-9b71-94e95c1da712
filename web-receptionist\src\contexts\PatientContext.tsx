import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { supabase } from '../services/supabase'
import { User, UserRole, UserStatus } from '../types'

interface PatientContextType {
  patients: User[]
  loading: boolean
  error: string | null
  fetchPatients: () => Promise<void>
  searchPatients: (query: string) => Promise<User[]>
  createPatient: (patientData: Partial<User>) => Promise<void>
  updatePatient: (id: string, updates: Partial<User>) => Promise<void>
  deletePatient: (id: string) => Promise<void>
}

const PatientContext = createContext<PatientContextType | undefined>(undefined)

export const usePatients = () => {
  const context = useContext(PatientContext)
  if (context === undefined) {
    throw new Error('usePatients must be used within a PatientProvider')
  }
  return context
}

interface PatientProviderProps {
  children: ReactNode
}

export const PatientProvider: React.FC<PatientProviderProps> = ({ children }) => {
  const [patients, setPatients] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchPatients()

    // Subscribe to patient changes
    const subscription = supabase
      .channel('patients-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'users' },
        (payload) => {
          handleRealtimeUpdate(payload)
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const handleRealtimeUpdate = (payload: any) => {
    const updatedPatient = payload.new
    const oldPatient = payload.old

    switch (payload.eventType) {
      case 'INSERT':
        if (updatedPatient.role === UserRole.PATIENT) {
          setPatients(prev => [...prev, updatedPatient])
        }
        break
      case 'UPDATE':
        if (updatedPatient.role === UserRole.PATIENT) {
          setPatients(prev =>
            prev.map(patient => patient.id === updatedPatient.id ? updatedPatient : patient)
          )
        } else {
          // If role changed from patient to something else, remove from list
          setPatients(prev => prev.filter(patient => patient.id !== updatedPatient.id))
        }
        break
      case 'DELETE':
        setPatients(prev => prev.filter(patient => patient.id !== oldPatient.id))
        break
    }
  }

  const fetchPatients = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', UserRole.PATIENT)
        .order('name', { ascending: true })

      if (error) throw error

      setPatients(data || [])
    } catch (error: any) {
      console.error('Error fetching patients:', error)
      setError(error.message || 'Failed to fetch patients')
    } finally {
      setLoading(false)
    }
  }

  const searchPatients = async (query: string): Promise<User[]> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', UserRole.PATIENT)
        .or(`name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)
        .order('name', { ascending: true })
        .limit(20)

      if (error) throw error

      return data || []
    } catch (error: any) {
      console.error('Error searching patients:', error)
      throw error
    }
  }

  const createPatient = async (patientData: Partial<User>) => {
    setLoading(true)
    setError(null)
    
    try {
      // Create auth user first
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: patientData.email!,
        password: 'TempPassword123!', // Temporary password
        options: {
          data: {
            name: patientData.name,
            role: UserRole.PATIENT,
          },
        },
      })

      if (authError) throw authError

      // Create user profile
      const { data, error } = await supabase
        .from('users')
        .insert({
          id: authData.user!.id,
          email: patientData.email!,
          name: patientData.name!,
          phone: patientData.phone,
          address: patientData.address,
          date_of_birth: patientData.date_of_birth,
          gender: patientData.gender,
          blood_group: patientData.blood_group,
          allergies: patientData.allergies,
          medical_conditions: patientData.medical_conditions,
          emergency_contact: patientData.emergency_contact,
          insurance_provider: patientData.insurance_provider,
          insurance_number: patientData.insurance_number,
          role: UserRole.PATIENT,
          status: UserStatus.ACTIVE,
        })
        .select()
        .single()

      if (error) throw error

      setPatients(prev => [...prev, data])
    } catch (error: any) {
      console.error('Error creating patient:', error)
      setError(error.message || 'Failed to create patient')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updatePatient = async (id: string, updates: Partial<User>) => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      setPatients(prev =>
        prev.map(patient => patient.id === id ? { ...patient, ...data } : patient)
      )
    } catch (error: any) {
      console.error('Error updating patient:', error)
      setError(error.message || 'Failed to update patient')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const deletePatient = async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      // First delete related appointments
      const { error: appointmentsError } = await supabase
        .from('appointments')
        .delete()
        .eq('patient_id', id)

      if (appointmentsError) throw appointmentsError

      // Then delete the user
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id)

      if (error) throw error

      setPatients(prev => prev.filter(patient => patient.id !== id))
    } catch (error: any) {
      console.error('Error deleting patient:', error)
      setError(error.message || 'Failed to delete patient')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const value: PatientContextType = {
    patients,
    loading,
    error,
    fetchPatients,
    searchPatients,
    createPatient,
    updatePatient,
    deletePatient,
  }

  return (
    <PatientContext.Provider value={value}>
      {children}
    </PatientContext.Provider>
  )
}