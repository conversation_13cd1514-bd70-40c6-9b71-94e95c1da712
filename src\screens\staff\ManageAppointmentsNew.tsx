import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  FlatList,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  Searchbar,
  Chip,
  IconButton,
  List,
  Divider,
  Portal,
  Modal,
  FAB,
  SegmentedButtons,
  DataTable,
  Badge,
  Surface,
  Avatar,
  Menu,
  TextInput,
  RadioButton,
  Switch,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRoute } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';
import { supabase } from '../../../lib/supabase';
import { format, parseISO, isToday, isTomorrow, isPast, addDays, startOfDay, endOfDay } from 'date-fns';

interface Appointment {
  id: string;
  patient_id: string;
  patient_name?: string;
  patient_phone?: string;
  patient_email?: string;
  dentist_id: string;
  dentist_name?: string;
  appointment_date: string;
  appointment_time: string;
  appointment_type: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  created_at: string;
  updated_at?: string;
  payment_status?: 'pending' | 'paid' | 'partial';
  amount?: number;
}

interface Patient {
  id: string;
  name: string;
  email: string;
  phone?: string;
}

interface Dentist {
  id: string;
  name: string;
  specialization?: string;
}

const APPOINTMENT_TYPES = [
  { value: 'checkup', label: 'Regular Checkup', duration: 30, color: '#4CAF50' },
  { value: 'cleaning', label: 'Teeth Cleaning', duration: 45, color: '#2196F3' },
  { value: 'filling', label: 'Filling', duration: 60, color: '#FF9800' },
  { value: 'extraction', label: 'Tooth Extraction', duration: 45, color: '#F44336' },
  { value: 'root_canal', label: 'Root Canal', duration: 90, color: '#9C27B0' },
  { value: 'crown', label: 'Crown', duration: 60, color: '#E91E63' },
  { value: 'orthodontics', label: 'Orthodontics', duration: 30, color: '#00BCD4' },
  { value: 'consultation', label: 'Consultation', duration: 20, color: '#795548' },
  { value: 'emergency', label: 'Emergency', duration: 60, color: '#FF5722' },
];

const APPOINTMENT_STATUS = {
  scheduled: { label: 'Scheduled', color: '#2196F3', icon: 'calendar-clock' },
  confirmed: { label: 'Confirmed', color: '#4CAF50', icon: 'check-circle' },
  completed: { label: 'Completed', color: '#9E9E9E', icon: 'check-all' },
  cancelled: { label: 'Cancelled', color: '#F44336', icon: 'cancel' },
  'no-show': { label: 'No Show', color: '#FF9800', icon: 'account-off' },
};

export const ManageAppointments = () => {
  const theme = useTheme();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'agenda'>('list');
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  
  const [patients, setPatients] = useState<Patient[]>([]);
  const [dentists, setDentists] = useState<Dentist[]>([]);
  
  const [formData, setFormData] = useState({
    patient_id: '',
    dentist_id: '',
    appointment_date: '',
    appointment_time: '',
    appointment_type: 'checkup',
    status: 'scheduled' as Appointment['status'],
    notes: '',
    send_notification: true,
  });

  const [stats, setStats] = useState({
    todayTotal: 0,
    todayCompleted: 0,
    tomorrowTotal: 0,
    weekTotal: 0,
    pendingPayments: 0,
  });

  useEffect(() => {
    fetchAppointments();
    fetchPatients();
    fetchDentists();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [appointments, searchQuery, statusFilter, dateFilter, selectedDate]);

  useEffect(() => {
    calculateStats();
  }, [appointments]);

  // Open create modal if navigated with openCreate param
  const route = useRoute<any>();
  useEffect(() => {
    if ((route as any)?.params?.openCreate) {
      setCreateModalVisible(true);
    }
  }, [route]);

  const fetchAppointments = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          patient:patient_id (
            id,
            name,
            email,
            phone
          ),
          dentist:dentist_id (
            id,
            name,
            specialization
          )
        `)
        .order('appointment_date', { ascending: true })
        .order('appointment_time', { ascending: true });

      if (error) throw error;

      const mappedData = data?.map(apt => ({
        ...apt,
        patient_name: apt.patient?.name,
        patient_phone: apt.patient?.phone,
        patient_email: apt.patient?.email,
        dentist_name: apt.dentist?.name,
      })) || [];

      setAppointments(mappedData);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      Alert.alert('Error', 'Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, phone')
        .eq('role', 'patient')
        .order('name');

      if (error) throw error;
      setPatients(data || []);
    } catch (error) {
      console.error('Error fetching patients:', error);
    }
  };

  const fetchDentists = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, specialization')
        .eq('role', 'dentist')
        .order('name');

      if (error) throw error;
      setDentists(data || []);
    } catch (error) {
      console.error('Error fetching dentists:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchAppointments();
    setRefreshing(false);
  };

  const applyFilters = () => {
    let filtered = [...appointments];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(apt =>
        apt.patient_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        apt.dentist_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        apt.appointment_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        apt.patient_phone?.includes(searchQuery)
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(apt => apt.status === statusFilter);
    }

    // Date filter
    const today = startOfDay(new Date());
    switch (dateFilter) {
      case 'today':
        filtered = filtered.filter(apt => 
          isToday(parseISO(apt.appointment_date))
        );
        break;
      case 'tomorrow':
        filtered = filtered.filter(apt => 
          isTomorrow(parseISO(apt.appointment_date))
        );
        break;
      case 'week':
        const weekEnd = addDays(today, 7);
        filtered = filtered.filter(apt => {
          const aptDate = parseISO(apt.appointment_date);
          return aptDate >= today && aptDate <= weekEnd;
        });
        break;
      case 'past':
        filtered = filtered.filter(apt => 
          isPast(parseISO(apt.appointment_date))
        );
        break;
    }

    // Selected date filter (for calendar view)
    if (viewMode === 'calendar' && selectedDate) {
      filtered = filtered.filter(apt => 
        apt.appointment_date === selectedDate
      );
    }

    setFilteredAppointments(filtered);
  };

  const calculateStats = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const tomorrow = format(addDays(new Date(), 1), 'yyyy-MM-dd');
    
    const todayApts = appointments.filter(apt => apt.appointment_date === today);
    const tomorrowApts = appointments.filter(apt => apt.appointment_date === tomorrow);
    const weekApts = appointments.filter(apt => {
      const aptDate = parseISO(apt.appointment_date);
      return aptDate >= new Date() && aptDate <= addDays(new Date(), 7);
    });
    
    setStats({
      todayTotal: todayApts.length,
      todayCompleted: todayApts.filter(apt => apt.status === 'completed').length,
      tomorrowTotal: tomorrowApts.length,
      weekTotal: weekApts.length,
      pendingPayments: appointments.filter(apt => apt.payment_status === 'pending').length,
    });
  };

  const handleStatusChange = async (appointment: Appointment, newStatus: Appointment['status']) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', appointment.id);

      if (error) throw error;

      // Send notification if needed
      if (newStatus === 'confirmed') {
        await sendNotification(appointment, 'Your appointment has been confirmed');
      } else if (newStatus === 'cancelled') {
        await sendNotification(appointment, 'Your appointment has been cancelled');
      }

      Alert.alert('Success', 'Appointment status updated');
      fetchAppointments();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update status');
    }
  };

  const handleCreateAppointment = async () => {
    if (!formData.patient_id || !formData.dentist_id || !formData.appointment_date || !formData.appointment_time) {
      Alert.alert('Error', 'Please fill all required fields');
      return;
    }

    try {
      const { error } = await supabase
        .from('appointments')
        .insert({
          ...formData,
          created_at: new Date().toISOString(),
        });

      if (error) throw error;

      if (formData.send_notification) {
        // Send notification to patient
        const patient = patients.find(p => p.id === formData.patient_id);
        if (patient) {
          await sendNotification(
            { ...formData, patient_email: patient.email } as any,
            'New appointment scheduled'
          );
        }
      }

      Alert.alert('Success', 'Appointment created successfully');
      setCreateModalVisible(false);
      resetForm();
      fetchAppointments();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to create appointment');
    }
  };

  const handleUpdateAppointment = async () => {
    if (!selectedAppointment) return;

    try {
      const { error } = await supabase
        .from('appointments')
        .update({
          ...formData,
          updated_at: new Date().toISOString(),
        })
        .eq('id', selectedAppointment.id);

      if (error) throw error;

      Alert.alert('Success', 'Appointment updated successfully');
      setEditModalVisible(false);
      resetForm();
      fetchAppointments();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update appointment');
    }
  };

  const handleDeleteAppointment = (appointment: Appointment) => {
    Alert.alert(
      'Delete Appointment',
      'Are you sure you want to delete this appointment?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('appointments')
                .delete()
                .eq('id', appointment.id);

              if (error) throw error;

              Alert.alert('Success', 'Appointment deleted successfully');
              fetchAppointments();
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete appointment');
            }
          },
        },
      ]
    );
  };

  const sendNotification = async (appointment: Appointment, message: string) => {
    try {
      await supabase.from('notifications').insert({
        user_id: appointment.patient_id,
        title: 'Appointment Update',
        message,
        type: 'appointment',
        related_id: appointment.id,
      });
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      patient_id: '',
      dentist_id: '',
      appointment_date: '',
      appointment_time: '',
      appointment_type: 'checkup',
      status: 'scheduled',
      notes: '',
      send_notification: true,
    });
  };

  const getAppointmentTypeInfo = (type: string) => {
    return APPOINTMENT_TYPES.find(t => t.value === type) || APPOINTMENT_TYPES[0];
  };

  const getStatusInfo = (status: string) => {
    return APPOINTMENT_STATUS[status as keyof typeof APPOINTMENT_STATUS] || APPOINTMENT_STATUS.scheduled;
  };

  const getMarkedDates = () => {
    const marked: any = {};
    
    appointments.forEach(apt => {
      const date = apt.appointment_date;
      if (!marked[date]) {
        marked[date] = { dots: [] };
      }
      
      const statusInfo = getStatusInfo(apt.status);
      marked[date].dots.push({
        color: statusInfo.color,
        selectedDotColor: statusInfo.color,
      });
    });

    // Mark selected date
    if (selectedDate) {
      marked[selectedDate] = {
        ...marked[selectedDate],
        selected: true,
        selectedColor: theme.colors.primary,
      };
    }

    return marked;
  };

  const renderAppointmentCard = ({ item }: { item: Appointment }) => {
    const typeInfo = getAppointmentTypeInfo(item.appointment_type);
    const statusInfo = getStatusInfo(item.status);
    const appointmentTime = parseISO(`${item.appointment_date}T${item.appointment_time}`);
    const isPastAppointment = isPast(appointmentTime);
    
    return (
      <Card 
        style={[styles.appointmentCard, isPastAppointment && styles.pastAppointment]}
        onPress={() => {
          setSelectedAppointment(item);
          setDetailsModalVisible(true);
        }}
      >
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.timeContainer}>
              <MaterialCommunityIcons 
                name="clock-outline" 
                size={16} 
                color={theme.colors.primary} 
              />
              <Text style={styles.timeText}>{item.appointment_time}</Text>
            </View>
            <Chip
              style={[styles.statusChip, { backgroundColor: statusInfo.color }]}
              textStyle={{ color: '#fff', fontSize: 11 }}
              icon={statusInfo.icon}
            >
              {statusInfo.label}
            </Chip>
          </View>

          <View style={styles.cardContent}>
            <View style={styles.patientInfo}>
              <Avatar.Text
                size={40}
                label={item.patient_name?.substring(0, 2).toUpperCase() || 'NA'}
                style={{ backgroundColor: typeInfo.color }}
              />
              <View style={styles.patientDetails}>
                <Text style={styles.patientName}>{item.patient_name || 'Unknown Patient'}</Text>
                <Text style={styles.appointmentType}>{typeInfo.label}</Text>
                {item.patient_phone && (
                  <Text style={styles.patientContact}>📞 {item.patient_phone}</Text>
                )}
              </View>
            </View>

            <View style={styles.dentistInfo}>
              <MaterialCommunityIcons name="doctor" size={16} color="#666" />
              <Text style={styles.dentistName}>Dr. {item.dentist_name}</Text>
            </View>
          </View>

          {item.status === 'scheduled' && !isPastAppointment && (
            <View style={styles.quickActions}>
              <Button
                mode="text"
                compact
                onPress={() => handleStatusChange(item, 'confirmed')}
                textColor="#4CAF50"
              >
                Confirm
              </Button>
              <Button
                mode="text"
                compact
                onPress={() => handleStatusChange(item, 'cancelled')}
                textColor="#F44336"
              >
                Cancel
              </Button>
              <IconButton
                icon="pencil"
                size={20}
                onPress={() => {
                  setSelectedAppointment(item);
                  setFormData({
                    patient_id: item.patient_id,
                    dentist_id: item.dentist_id,
                    appointment_date: item.appointment_date,
                    appointment_time: item.appointment_time,
                    appointment_type: item.appointment_type,
                    status: item.status,
                    notes: item.notes || '',
                    send_notification: false,
                  });
                  setEditModalVisible(true);
                }}
              />
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  const renderDetailsModal = () => {
    if (!selectedAppointment) return null;
    
    const typeInfo = getAppointmentTypeInfo(selectedAppointment.appointment_type);
    const statusInfo = getStatusInfo(selectedAppointment.status);
    
    return (
      <Portal>
        <Modal
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Appointment Details</Title>
            
            <Surface style={styles.detailsHeader} elevation={1}>
              <Avatar.Text
                size={60}
                label={selectedAppointment.patient_name?.substring(0, 2).toUpperCase() || 'NA'}
                style={{ backgroundColor: typeInfo.color }}
              />
              <View style={styles.detailsHeaderInfo}>
                <Text style={styles.detailsName}>{selectedAppointment.patient_name}</Text>
                <Chip
                  style={[styles.statusChip, { backgroundColor: statusInfo.color }]}
                  textStyle={{ color: '#fff' }}
                  icon={statusInfo.icon}
                >
                  {statusInfo.label}
                </Chip>
              </View>
            </Surface>
            
            <List.Section>
              <List.Item
                title="Date & Time"
                description={`${format(parseISO(selectedAppointment.appointment_date), 'EEEE, MMMM d, yyyy')}\n${selectedAppointment.appointment_time}`}
                left={props => <List.Icon {...props} icon="calendar-clock" />}
              />
              
              <List.Item
                title="Type"
                description={typeInfo.label}
                left={props => <List.Icon {...props} icon="tooth" color={typeInfo.color} />}
              />
              
              <List.Item
                title="Dentist"
                description={`Dr. ${selectedAppointment.dentist_name}`}
                left={props => <List.Icon {...props} icon="doctor" />}
              />
              
              {selectedAppointment.patient_phone && (
                <List.Item
                  title="Phone"
                  description={selectedAppointment.patient_phone}
                  left={props => <List.Icon {...props} icon="phone" />}
                  onPress={() => Alert.alert('Call', `Call ${selectedAppointment.patient_phone}?`)}
                />
              )}
              
              {selectedAppointment.patient_email && (
                <List.Item
                  title="Email"
                  description={selectedAppointment.patient_email}
                  left={props => <List.Icon {...props} icon="email" />}
                />
              )}
              
              {selectedAppointment.notes && (
                <List.Item
                  title="Notes"
                  description={selectedAppointment.notes}
                  left={props => <List.Icon {...props} icon="note-text" />}
                />
              )}
            </List.Section>

            <View style={styles.modalActions}>
              {selectedAppointment.status === 'scheduled' && (
                <>
                  <Button
                    mode="contained"
                    onPress={() => {
                      handleStatusChange(selectedAppointment, 'confirmed');
                      setDetailsModalVisible(false);
                    }}
                    style={styles.actionButton}
                    buttonColor="#4CAF50"
                  >
                    Confirm
                  </Button>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      setFormData({
                        patient_id: selectedAppointment.patient_id,
                        dentist_id: selectedAppointment.dentist_id,
                        appointment_date: selectedAppointment.appointment_date,
                        appointment_time: selectedAppointment.appointment_time,
                        appointment_type: selectedAppointment.appointment_type,
                        status: selectedAppointment.status,
                        notes: selectedAppointment.notes || '',
                        send_notification: false,
                      });
                      setDetailsModalVisible(false);
                      setEditModalVisible(true);
                    }}
                    style={styles.actionButton}
                  >
                    Edit
                  </Button>
                </>
              )}
              <Button
                mode="text"
                onPress={() => {
                  handleDeleteAppointment(selectedAppointment);
                  setDetailsModalVisible(false);
                }}
                textColor={theme.colors.error}
              >
                Delete
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading appointments...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={styles.title}>Manage Appointments</Title>
        <SegmentedButtons
          value={viewMode}
          onValueChange={(value) => setViewMode(value as any)}
          buttons={[
            { value: 'list', label: 'List', icon: 'format-list-bulleted' },
            { value: 'calendar', label: 'Calendar', icon: 'calendar' },
            { value: 'agenda', label: 'Agenda', icon: 'calendar-text' },
          ]}
          style={styles.viewToggle}
        />
      </View>

      {/* Stats */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.statsContainer}
      >
        <Surface style={[styles.statCard, { backgroundColor: '#E3F2FD' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.todayTotal}</Text>
          <Text style={styles.statLabel}>Today</Text>
          <Text style={styles.statSubtext}>{stats.todayCompleted} completed</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#E8F5E9' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.tomorrowTotal}</Text>
          <Text style={styles.statLabel}>Tomorrow</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#FFF3E0' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.weekTotal}</Text>
          <Text style={styles.statLabel}>This Week</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#FFEBEE' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.pendingPayments}</Text>
          <Text style={styles.statLabel}>Pending Payment</Text>
        </Surface>
      </ScrollView>

      {/* Filters */}
      <View style={styles.filters}>
        <Searchbar
          placeholder="Search patients, dentists..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          <Chip
            selected={dateFilter === 'all'}
            onPress={() => setDateFilter('all')}
            style={styles.filterChip}
          >
            All Dates
          </Chip>
          <Chip
            selected={dateFilter === 'today'}
            onPress={() => setDateFilter('today')}
            style={styles.filterChip}
            icon="calendar-today"
          >
            Today
          </Chip>
          <Chip
            selected={dateFilter === 'tomorrow'}
            onPress={() => setDateFilter('tomorrow')}
            style={styles.filterChip}
          >
            Tomorrow
          </Chip>
          <Chip
            selected={dateFilter === 'week'}
            onPress={() => setDateFilter('week')}
            style={styles.filterChip}
          >
            This Week
          </Chip>
          <Chip
            selected={dateFilter === 'past'}
            onPress={() => setDateFilter('past')}
            style={styles.filterChip}
          >
            Past
          </Chip>
        </ScrollView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          <Chip
            selected={statusFilter === 'all'}
            onPress={() => setStatusFilter('all')}
            style={styles.filterChip}
          >
            All Status
          </Chip>
          {Object.entries(APPOINTMENT_STATUS).map(([key, value]) => (
            <Chip
              key={key}
              selected={statusFilter === key}
              onPress={() => setStatusFilter(key)}
              style={styles.filterChip}
              icon={value.icon}
            >
              {value.label}
            </Chip>
          ))}
        </ScrollView>
      </View>

      {/* Content based on view mode */}
      {viewMode === 'list' && (
        <FlatList
          data={filteredAppointments}
          renderItem={renderAppointmentCard}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}

      {viewMode === 'calendar' && (
        <ScrollView showsVerticalScrollIndicator={false}>
          <Calendar
            current={selectedDate}
            onDayPress={(day) => setSelectedDate(day.dateString)}
            markedDates={getMarkedDates()}
            markingType="multi-dot"
            theme={{
              selectedDayBackgroundColor: theme.colors.primary,
              todayTextColor: theme.colors.primary,
              arrowColor: theme.colors.primary,
            }}
          />
          
          <View style={styles.calendarAppointments}>
            <Text style={styles.calendarDate}>
              {format(parseISO(selectedDate), 'EEEE, MMMM d, yyyy')}
            </Text>
            {filteredAppointments.length > 0 ? (
              filteredAppointments.map(apt => (
                <TouchableOpacity
                  key={apt.id}
                  onPress={() => {
                    setSelectedAppointment(apt);
                    setDetailsModalVisible(true);
                  }}
                >
                  <Surface style={styles.calendarAppointmentCard} elevation={1}>
                    <Text style={styles.calendarTime}>{apt.appointment_time}</Text>
                    <View style={styles.calendarInfo}>
                      <Text style={styles.calendarPatient}>{apt.patient_name}</Text>
                      <Text style={styles.calendarType}>
                        {getAppointmentTypeInfo(apt.appointment_type).label}
                      </Text>
                    </View>
                    <Chip
                      style={[
                        styles.calendarStatusChip,
                        { backgroundColor: getStatusInfo(apt.status).color }
                      ]}
                      textStyle={{ color: '#fff', fontSize: 10 }}
                    >
                      {getStatusInfo(apt.status).label}
                    </Chip>
                  </Surface>
                </TouchableOpacity>
              ))
            ) : (
              <Text style={styles.noAppointments}>No appointments for this date</Text>
            )}
          </View>
        </ScrollView>
      )}

      {viewMode === 'agenda' && (
        <Agenda
          items={{}}
          selected={selectedDate}
          renderItem={(item) => <View />}
          renderEmptyData={() => (
            <View style={styles.emptyAgenda}>
              <Text>No appointments</Text>
            </View>
          )}
        />
      )}

      {/* FAB */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => {
          resetForm();
          setCreateModalVisible(true);
        }}
        label="New Appointment"
      />

      {/* Modals */}
      {renderDetailsModal()}

      <View style={{ height: 100 }} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  viewToggle: {
    marginTop: 8,
  },
  statsContainer: {
    padding: 16,
    maxHeight: 120,
  },
  statCard: {
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    minWidth: 120,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  statSubtext: {
    fontSize: 11,
    color: '#999',
    marginTop: 2,
  },
  filters: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    elevation: 1,
  },
  searchBar: {
    marginBottom: 8,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  filterRow: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  filterChip: {
    marginRight: 8,
  },
  listContainer: {
    padding: 16,
  },
  appointmentCard: {
    marginBottom: 12,
    elevation: 2,
  },
  pastAppointment: {
    opacity: 0.7,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: 'bold',
  },
  statusChip: {
    height: 24,
  },
  cardContent: {
    marginBottom: 8,
  },
  patientInfo: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  patientDetails: {
    marginLeft: 12,
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  appointmentType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  patientContact: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  dentistInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dentistName: {
    marginLeft: 4,
    fontSize: 13,
    color: '#666',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 8,
    marginTop: 8,
  },
  calendarAppointments: {
    padding: 16,
  },
  calendarDate: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  calendarAppointmentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  calendarTime: {
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 12,
  },
  calendarInfo: {
    flex: 1,
  },
  calendarPatient: {
    fontSize: 14,
    fontWeight: '500',
  },
  calendarType: {
    fontSize: 12,
    color: '#666',
  },
  calendarStatusChip: {
    height: 20,
  },
  noAppointments: {
    textAlign: 'center',
    color: '#999',
    marginTop: 20,
  },
  emptyAgenda: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  detailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
  },
  detailsHeaderInfo: {
    marginLeft: 16,
    flex: 1,
  },
  detailsName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
});
