import React, { useEffect, useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Avatar,
  Text,
  useTheme,
  IconButton,
  Chip,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { format, startOfToday } from 'date-fns';
import { useAppointments } from '../../contexts/AppointmentContext';
import { AppointmentStatus } from '../../types';

export const PatientDashboard = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<any>();
  const { appointments, fetchAppointments } = useAppointments();
  const [refreshing, setRefreshing] = useState(false);
  const [upcomingAppointment, setUpcomingAppointment] = useState<any>(null);
  const [stats, setStats] = useState({
    totalAppointments: 0,
    upcomingCount: 0,
    completedCount: 0,
  });

  useEffect(() => {
    // Ensure we have fresh data when opening dashboard
    fetchAppointments().catch(() => {});
  }, []);

  useEffect(() => {
    const today = startOfToday();
    const upcoming = appointments
      .filter(a => a.status === AppointmentStatus.SCHEDULED && new Date(a.date) >= today)
      .sort((a, b) => {
        const dc = new Date(a.date).getTime() - new Date(b.date).getTime();
        if (dc !== 0) return dc;
        return a.start_time.localeCompare(b.start_time);
      });
    setUpcomingAppointment(upcoming[0] || null);

    setStats({
      totalAppointments: appointments.length,
      upcomingCount: upcoming.length,
      completedCount: appointments.filter(a => a.status === AppointmentStatus.COMPLETED).length,
    });
  }, [appointments]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await fetchAppointments().catch(() => {});
    setRefreshing(false);
  }, [fetchAppointments]);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getInitials = () => {
    const name = user?.name?.trim() || '';
    if (!name) return 'PT';
    const parts = name.split(/\s+/);
    const first = parts[0]?.[0] || '';
    const last = parts.length > 1 ? parts[parts.length - 1]?.[0] || '' : '';
    const initials = (first + last).toUpperCase();
    return initials || 'PT';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Welcome Section */}
        <Card style={[styles.welcomeCard, { backgroundColor: theme.colors.primary }]}>
          <Card.Content>
            <View style={styles.welcomeHeader}>
              <View style={styles.welcomeTextContainer}>
                <Text style={styles.greetingText}>{getGreeting()},</Text>
                <Title style={styles.userName}>{user?.name || 'Patient'}</Title>
              </View>
              <Avatar.Text
                size={60}
                label={getInitials()}
                style={styles.avatar}
              />
            </View>
          </Card.Content>
        </Card>

        {/* Upcoming Appointment */}
        {upcomingAppointment ? (
          <Card style={styles.appointmentCard}>
            <Card.Content>
              <View style={styles.appointmentHeader}>
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={20}
                  color={theme.colors.primary}
                />
                <Text style={[styles.appointmentTitle, { color: theme.colors.primary }]}>
                  Upcoming Appointment
                </Text>
              </View>
              <Divider style={styles.divider} />
              <View style={styles.appointmentDetails}>
                <View style={styles.appointmentRow}>
                  <MaterialCommunityIcons name="calendar" size={16} color="#666" />
                  <Text style={styles.appointmentText}>
                    {format(new Date(upcomingAppointment.date), 'EEEE, MMMM d, yyyy')}
                  </Text>
                </View>
                <View style={styles.appointmentRow}>
                  <MaterialCommunityIcons name="clock" size={16} color="#666" />
                  <Text style={styles.appointmentText}>
                    {upcomingAppointment.start_time} - {upcomingAppointment.end_time}
                  </Text>
                </View>
                <View style={styles.appointmentRow}>
                  <MaterialCommunityIcons name="doctor" size={16} color="#666" />
                  <Text style={styles.appointmentText}>
                    Dr. {upcomingAppointment.dentist?.name || 'Assigned Dentist'}
                  </Text>
                </View>
              </View>
              <View style={styles.appointmentActions}>
                <Button
                  mode="outlined"
                  onPress={() => {}}
                  style={styles.appointmentButton}
                  compact
                >
                  Reschedule
                </Button>
                <Button
                  mode="contained"
                  onPress={() => {}}
                  style={styles.appointmentButton}
                  compact
                >
                  View Details
                </Button>
              </View>
            </Card.Content>
          </Card>
        ) : (
          <Card style={styles.noAppointmentCard}>
            <Card.Content>
              <View style={styles.noAppointmentContent}>
                <MaterialCommunityIcons
                  name="calendar-blank"
                  size={48}
                  color="#ccc"
                />
                <Text style={styles.noAppointmentText}>No upcoming appointments</Text>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('BookAppointment')}
          >
            <View style={[styles.actionIcon, { backgroundColor: theme.colors.primaryContainer }]}>
              <MaterialCommunityIcons
                name="calendar-plus"
                size={28}
                color={theme.colors.primary}
              />
            </View>
            <Text style={styles.actionText}>Book</Text>
            <Text style={styles.actionSubtext}>Appointment</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('MyAppointments')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#e8f5e9' }]}>
              <MaterialCommunityIcons
                name="calendar-check"
                size={28}
                color="#4CAF50"
              />
            </View>
            <Text style={styles.actionText}>View</Text>
            <Text style={styles.actionSubtext}>Appointments</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Profile')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#fff3e0' }]}>
              <MaterialCommunityIcons
                name="account"
                size={28}
                color="#FF9800"
              />
            </View>
            <Text style={styles.actionText}>My</Text>
            <Text style={styles.actionSubtext}>Profile</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Notifications')}
          >
            <View style={[styles.actionIcon, { backgroundColor: '#fce4ec' }]}>
              <MaterialCommunityIcons
                name="bell"
                size={28}
                color="#E91E63"
              />
            </View>
            <Text style={styles.actionText}>View</Text>
            <Text style={styles.actionSubtext}>Notifications</Text>
          </TouchableOpacity>
        </View>

        {/* Statistics */}
        <Card style={styles.statsCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Your Statistics</Title>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
                  {stats.totalAppointments}
                </Text>
                <Text style={styles.statLabel}>Total</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: '#4CAF50' }]}>
                  {stats.upcomingCount}
                </Text>
                <Text style={styles.statLabel}>Upcoming</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, { color: '#FF9800' }]}>
                  {stats.completedCount}
                </Text>
                <Text style={styles.statLabel}>Completed</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Health Tips */}
        <Card style={styles.tipsCard}>
          <Card.Content>
            <View style={styles.tipsHeader}>
              <MaterialCommunityIcons
                name="lightbulb-outline"
                size={24}
                color={theme.colors.primary}
              />
              <Title style={styles.tipsTitle}>Dental Health Tips</Title>
            </View>
            <View style={styles.tipItem}>
              <MaterialCommunityIcons name="check-circle" size={16} color="#4CAF50" />
              <Text style={styles.tipText}>Brush your teeth twice daily for 2 minutes</Text>
            </View>
            <View style={styles.tipItem}>
              <MaterialCommunityIcons name="check-circle" size={16} color="#4CAF50" />
              <Text style={styles.tipText}>Floss daily to remove plaque between teeth</Text>
            </View>
            <View style={styles.tipItem}>
              <MaterialCommunityIcons name="check-circle" size={16} color="#4CAF50" />
              <Text style={styles.tipText}>Visit your dentist every 6 months</Text>
            </View>
            <View style={styles.tipItem}>
              <MaterialCommunityIcons name="check-circle" size={16} color="#4CAF50" />
              <Text style={styles.tipText}>Limit sugary foods and drinks</Text>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  welcomeCard: {
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 12,
    elevation: 4,
  },
  welcomeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeTextContainer: {
    flex: 1,
  },
  greetingText: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginVertical: 4,
  },
  welcomeSubtext: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  avatar: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginTop: 8,
  },
  actionButton: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    elevation: 2,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  actionSubtext: {
    fontSize: 12,
    color: '#666',
  },
  appointmentCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
  },
  appointmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  appointmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  divider: {
    marginVertical: 8,
  },
  appointmentDetails: {
    marginVertical: 8,
  },
  appointmentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  appointmentText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
  },
  appointmentActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  appointmentButton: {
    flex: 0.48,
  },
  noAppointmentCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
  },
  noAppointmentContent: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noAppointmentText: {
    fontSize: 14,
    color: '#666',
    marginVertical: 12,
  },
  statsCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#e0e0e0',
  },
  tipsCard: {
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
    backgroundColor: '#f0f8ff',
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
});
