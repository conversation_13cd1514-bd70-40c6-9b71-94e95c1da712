import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { supabase } from '../services/supabase'
import { Appointment, AppointmentContextType, AppointmentFilters, AppointmentStatus } from '../types'

const AppointmentContext = createContext<AppointmentContextType | undefined>(undefined)

export const useAppointments = () => {
  const context = useContext(AppointmentContext)
  if (context === undefined) {
    throw new Error('useAppointments must be used within an AppointmentProvider')
  }
  return context
}

interface AppointmentProviderProps {
  children: ReactNode
}

export const AppointmentProvider: React.FC<AppointmentProviderProps> = ({ children }) => {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAppointments()

    // Subscribe to appointment changes
    const subscription = supabase
      .channel('appointments-channel')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'appointments' },
        (payload) => {
          handleRealtimeUpdate(payload)
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const handleRealtimeUpdate = (payload: any) => {
    const updatedAppointment = payload.new
    const oldAppointment = payload.old

    switch (payload.eventType) {
      case 'INSERT':
        setAppointments(prev => [...prev, updatedAppointment])
        break
      case 'UPDATE':
        setAppointments(prev =>
          prev.map(apt => apt.id === updatedAppointment.id ? updatedAppointment : apt)
        )
        break
      case 'DELETE':
        setAppointments(prev => prev.filter(apt => apt.id !== oldAppointment.id))
        break
    }
  }

  const fetchAppointments = async (filters?: AppointmentFilters) => {
    setLoading(true)
    setError(null)
    
    try {
      let query = supabase
        .from('appointments')
        .select(`
          *,
          patient:patient_id (
            id,
            name,
            email,
            phone
          ),
          dentist:dentist_id (
            id,
            name,
            specialization
          ),
          staff:staff_id (
            id,
            name
          )
        `)
        .order('date', { ascending: true })
        .order('start_time', { ascending: true })

      // Apply filters
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }
      if (filters?.dentistId) {
        query = query.eq('dentist_id', filters.dentistId)
      }
      if (filters?.patientId) {
        query = query.eq('patient_id', filters.patientId)
      }
      if (filters?.dateFrom) {
        query = query.gte('date', filters.dateFrom)
      }
      if (filters?.dateTo) {
        query = query.lte('date', filters.dateTo)
      }

      const { data, error } = await query

      if (error) throw error

      setAppointments(data || [])
    } catch (error: any) {
      console.error('Error fetching appointments:', error)
      setError(error.message || 'Failed to fetch appointments')
    } finally {
      setLoading(false)
    }
  }

  const createAppointment = async (appointment: Partial<Appointment>) => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('appointments')
        .insert([appointment])
        .select()
        .single()

      if (error) throw error

      setAppointments(prev => [...prev, data])
      
      // Send notification to patient
      if (appointment.patient_id) {
        await sendNotification(
          appointment.patient_id,
          'New Appointment',
          `Your appointment has been scheduled for ${appointment.date} at ${appointment.start_time}`
        )
      }
    } catch (error: any) {
      console.error('Error creating appointment:', error)
      setError(error.message || 'Failed to create appointment')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateAppointment = async (id: string, updates: Partial<Appointment>) => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      setAppointments(prev =>
        prev.map(apt => apt.id === id ? { ...apt, ...data } : apt)
      )

      // Send notification if status changed
      if (updates.status && data.patient_id) {
        let message = ''
        switch (updates.status) {
          case AppointmentStatus.CONFIRMED:
            message = `Your appointment for ${data.date} at ${data.start_time} has been confirmed`
            break
          case AppointmentStatus.CANCELLED:
            message = `Your appointment for ${data.date} at ${data.start_time} has been cancelled`
            break
          case AppointmentStatus.COMPLETED:
            message = `Your appointment for ${data.date} has been completed`
            break
        }
        
        if (message) {
          await sendNotification(data.patient_id, 'Appointment Update', message)
        }
      }
    } catch (error: any) {
      console.error('Error updating appointment:', error)
      setError(error.message || 'Failed to update appointment')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const cancelAppointment = async (id: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await supabase
        .from('appointments')
        .update({ status: AppointmentStatus.CANCELLED })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      setAppointments(prev =>
        prev.map(apt => apt.id === id ? { ...apt, status: AppointmentStatus.CANCELLED } : apt)
      )

      // Send cancellation notification
      if (data.patient_id) {
        await sendNotification(
          data.patient_id,
          'Appointment Cancelled',
          `Your appointment for ${data.date} at ${data.start_time} has been cancelled`
        )
      }
    } catch (error: any) {
      console.error('Error cancelling appointment:', error)
      setError(error.message || 'Failed to cancel appointment')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getAvailableSlots = async (date: string, dentistId?: string) => {
    try {
      // Get clinic settings
      const { data: settings } = await supabase
        .from('system_settings')
        .select('working_hours, slot_duration')
        .single()

      if (!settings) throw new Error('Clinic settings not found')

      const workingHours = settings.working_hours
      const slotDuration = settings.slot_duration || 60

      // Get existing appointments for the date
      let appointmentsQuery = supabase
        .from('appointments')
        .select('start_time, end_time, dentist_id')
        .eq('date', date)
        .in('status', ['scheduled', 'confirmed'])

      if (dentistId) {
        appointmentsQuery = appointmentsQuery.eq('dentist_id', dentistId)
      }

      const { data: existingAppointments } = await appointmentsQuery

      // Generate time slots
      const slots = []
      const startTime = new Date(`${date}T${workingHours.start}`)
      const endTime = new Date(`${date}T${workingHours.end}`)

      for (let time = startTime; time < endTime; time.setMinutes(time.getMinutes() + slotDuration)) {
        const slotStart = time.toTimeString().slice(0, 5)
        const slotEnd = new Date(time.getTime() + slotDuration * 60000).toTimeString().slice(0, 5)

        // Check if slot is available
        const isAvailable = !existingAppointments?.some(apt => {
          const aptStart = apt.start_time
          const aptEnd = apt.end_time
          return slotStart >= aptStart && slotStart < aptEnd
        })

        slots.push({
          date,
          startTime: slotStart,
          endTime: slotEnd,
          available: isAvailable,
          dentistId,
        })
      }

      return slots
    } catch (error: any) {
      console.error('Error getting available slots:', error)
      throw error
    }
  }

  const sendNotification = async (userId: string, title: string, message: string) => {
    try {
      await supabase.from('notifications').insert({
        user_id: userId,
        title,
        message,
        type: 'appointment_confirmation',
        status: 'pending',
      })
    } catch (error) {
      console.error('Error sending notification:', error)
    }
  }

  const value: AppointmentContextType = {
    appointments,
    loading,
    error,
    fetchAppointments,
    createAppointment,
    updateAppointment,
    cancelAppointment,
    getAvailableSlots,
  }

  return (
    <AppointmentContext.Provider value={value}>
      {children}
    </AppointmentContext.Provider>
  )
}