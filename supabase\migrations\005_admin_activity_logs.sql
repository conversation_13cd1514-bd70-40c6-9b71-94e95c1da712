-- Create admin_activity_logs table
CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id VARCHAR(255),
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX idx_admin_activity_logs_action_type ON admin_activity_logs(action_type);
CREATE INDEX idx_admin_activity_logs_target_type ON admin_activity_logs(target_type);
CREATE INDEX idx_admin_activity_logs_created_at ON admin_activity_logs(created_at DESC);
CREATE INDEX idx_admin_activity_logs_target_id ON admin_activity_logs(target_id);

-- Enable RLS
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Only admins can view activity logs
CREATE POLICY "Admins can view activity logs"
ON admin_activity_logs FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Policy: System can insert activity logs (for all authenticated users to log their actions)
CREATE POLICY "Authenticated users can create activity logs"
ON admin_activity_logs FOR INSERT
TO authenticated
WITH CHECK (
    admin_id = auth.uid()
);

-- Policy: Only admins can delete old logs
CREATE POLICY "Admins can delete activity logs"
ON admin_activity_logs FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Function to automatically log user login
CREATE OR REPLACE FUNCTION log_user_login()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log if user is admin
    IF EXISTS (SELECT 1 FROM users WHERE id = NEW.id AND role = 'admin') THEN
        INSERT INTO admin_activity_logs (
            admin_id,
            action_type,
            target_type,
            description
        ) VALUES (
            NEW.id,
            'login',
            'system',
            'Admin logged into the system'
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old logs (older than 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_activity_logs()
RETURNS void AS $$
BEGIN
    DELETE FROM admin_activity_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up old logs (requires pg_cron extension)
-- Note: pg_cron needs to be enabled in Supabase dashboard
-- SELECT cron.schedule('cleanup-activity-logs', '0 2 * * *', 'SELECT cleanup_old_activity_logs();');

-- Insert some sample activity logs for testing
INSERT INTO admin_activity_logs (admin_id, action_type, target_type, target_id, description, metadata)
SELECT 
    u.id,
    action_types.action,
    target_types.target,
    gen_random_uuid()::text,
    action_types.description,
    jsonb_build_object('test', true, 'sample_data', true)
FROM users u
CROSS JOIN (
    VALUES 
    ('login', 'Admin logged into the system'),
    ('user_created', 'Created new user account'),
    ('user_updated', 'Updated user information'),
    ('settings_updated', 'Modified system settings'),
    ('appointment_created', 'Created new appointment'),
    ('backup_created', 'System backup completed')
) AS action_types(action, description)
CROSS JOIN (
    VALUES 
    ('system'),
    ('user'),
    ('appointment')
) AS target_types(target)
WHERE u.role = 'admin'
LIMIT 20;
