import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { TextField, Button, Box, Typography, Paper } from '@mui/material'

const TestLogin: React.FC = () => {
  const { signIn } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleTestLogin = async () => {
    setLoading(true)
    setError('')
    
    try {
      await signIn(email || '<EMAIL>', password || 'password123')
    } catch (error: any) {
      console.error('Test login error:', error)
      setError(error.message || 'Login failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
      <Paper elevation={3} sx={{ p: 4, maxWidth: 400, width: '100%' }}>
        <Typography variant="h5" gutterBottom align="center">
          Test Login
        </Typography>
        
        <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
          Use your existing Supabase credentials
        </Typography>

        {error && (
          <Typography color="error" variant="body2" sx={{ mb: 2 }}>
            {error}
          </Typography>
        )}

        <TextField
          fullWidth
          label="Email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          sx={{ mb: 2 }}
          placeholder="<EMAIL>"
        />
        
        <TextField
          fullWidth
          label="Password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          sx={{ mb: 3 }}
          placeholder="password123"
        />
        
        <Button
          fullWidth
          variant="contained"
          onClick={handleTestLogin}
          disabled={loading}
          sx={{ mb: 2 }}
        >
          {loading ? 'Logging in...' : 'Test Login'}
        </Button>

        <Button
          fullWidth
          variant="outlined"
          onClick={() => {
            setEmail('<EMAIL>')
            setPassword('password123')
          }}
        >
          Use Test Credentials
        </Button>

        <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
          Or try: http://localhost:3000/login for the regular login page
        </Typography>
      </Paper>
    </Box>
  )
}

export default TestLogin