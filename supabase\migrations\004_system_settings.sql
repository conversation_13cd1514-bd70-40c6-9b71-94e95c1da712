-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id INTEGER PRIMARY KEY DEFAULT 1 CHECK (id = 1), -- Ensures only one row
    settings JSONB NOT NULL DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id)
);

-- Create RLS policies for system_settings
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Allow admins to read settings
CREATE POLICY "Ad<PERSON> can read system settings"
ON system_settings FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Allow admins to update settings
CREATE POLICY "Ad<PERSON> can update system settings"
ON system_settings FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Allow admins to insert settings (initial setup)
CREATE POLICY "Admins can insert system settings"
ON system_settings FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = auth.uid() 
        AND users.role = 'admin'
    )
);

-- Insert default settings if not exists
INSERT INTO system_settings (id, settings)
VALUES (1, '{
    "clinicName": "Dr. Shilpa''s Dental Clinic",
    "clinicAddress": "",
    "clinicPhone": "",
    "clinicEmail": "",
    "clinicWebsite": "",
    "operatingHours": {
        "monday": {"isOpen": true, "openTime": "09:00", "closeTime": "18:00", "breakStart": "13:00", "breakEnd": "14:00"},
        "tuesday": {"isOpen": true, "openTime": "09:00", "closeTime": "18:00", "breakStart": "13:00", "breakEnd": "14:00"},
        "wednesday": {"isOpen": true, "openTime": "09:00", "closeTime": "18:00", "breakStart": "13:00", "breakEnd": "14:00"},
        "thursday": {"isOpen": true, "openTime": "09:00", "closeTime": "18:00", "breakStart": "13:00", "breakEnd": "14:00"},
        "friday": {"isOpen": true, "openTime": "09:00", "closeTime": "18:00", "breakStart": "13:00", "breakEnd": "14:00"},
        "saturday": {"isOpen": true, "openTime": "09:00", "closeTime": "14:00"},
        "sunday": {"isOpen": false, "openTime": "09:00", "closeTime": "18:00"}
    },
    "appointmentDuration": 30,
    "bufferTime": 15,
    "maxAdvanceBooking": 30,
    "maxDailyAppointments": 20,
    "allowOnlineBooking": true,
    "requirePaymentConfirmation": false,
    "cancellationPolicy": "Appointments can be cancelled up to 24 hours in advance",
    "cancellationTimeLimit": 24,
    "enableEmailNotifications": true,
    "enableSMSNotifications": false,
    "enablePushNotifications": true,
    "appointmentReminder": 24,
    "followUpReminder": 7,
    "acceptedPaymentMethods": ["cash", "card", "upi"],
    "taxRate": 18,
    "currency": "INR",
    "maintenanceMode": false,
    "autoBackup": true,
    "backupFrequency": "daily",
    "dataRetentionPeriod": 365,
    "sessionTimeout": 30,
    "enableReviews": true,
    "enableReferrals": true,
    "enableLoyaltyProgram": false,
    "enableTeleconsultation": false
}')
ON CONFLICT (id) DO NOTHING;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_system_settings_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.updated_by = auth.uid();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update timestamp
CREATE TRIGGER update_system_settings_timestamp_trigger
BEFORE UPDATE ON system_settings
FOR EACH ROW
EXECUTE FUNCTION update_system_settings_timestamp();
