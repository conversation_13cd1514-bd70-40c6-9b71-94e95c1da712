-- 002_rls_appointments_receptionist.sql
-- Purpose: Allow receptionist (and existing admin/staff/doctor) to read/insert/update appointments via RLS.
-- Safe to run multiple times; uses IF NOT EXISTS checks for policies.

BEGIN;

-- Ensure RLS is enabled (idempotent)
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;

-- SELECT policy
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='appointments' AND policyname='app_select_admin_staff_doctor_receptionist'
  ) THEN
    CREATE POLICY app_select_admin_staff_doctor_receptionist
    ON public.appointments
    FOR SELECT
    TO authenticated
    USING (
      auth.uid() IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
          AND u.role IN ('admin','staff','doctor','receptionist')
      )
    );
  END IF;
END $$;

-- INSERT policy
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='appointments' AND policyname='app_insert_admin_staff_doctor_receptionist'
  ) THEN
    CREATE POLICY app_insert_admin_staff_doctor_receptionist
    ON public.appointments
    FOR INSERT
    TO authenticated
    WITH CHECK (
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
          AND u.role IN ('admin','staff','doctor','receptionist')
      )
    );
  END IF;
END $$;

-- UPDATE policy
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='appointments' AND policyname='app_update_admin_staff_doctor_receptionist'
  ) THEN
    CREATE POLICY app_update_admin_staff_doctor_receptionist
    ON public.appointments
    FOR UPDATE
    TO authenticated
    USING (
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
          AND u.role IN ('admin','staff','doctor','receptionist')
      )
    )
    WITH CHECK (
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
          AND u.role IN ('admin','staff','doctor','receptionist')
      )
    );
  END IF;
END $$;

COMMIT;

