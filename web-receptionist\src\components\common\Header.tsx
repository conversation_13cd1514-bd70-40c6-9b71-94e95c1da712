import React from 'react'
import { App<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, IconButton, Badge, Box, Button } from '@mui/material'
import { Notifications as NotificationsIcon, AccountCircle, Logout } from '@mui/icons-material'
import { useAuth } from '../../contexts/AuthContext'

const Header: React.FC = () => {
  const { user, signOut } = useAuth()

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <AppBar position="static" elevation={1}>
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Dr. Shilpa Receptionist Portal
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <IconButton color="inherit">
            <Badge badgeContent={0} color="error">
              <NotificationsIcon />
            </Badge>
          </IconButton>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AccountCircle />
            <Typography variant="body2">
              {user?.name || 'User'}
            </Typography>
          </Box>
          
          <Button
            color="inherit"
            startIcon={<Logout />}
            onClick={handleLogout}
            size="small"
          >
            Logout
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  )
}

export default Header