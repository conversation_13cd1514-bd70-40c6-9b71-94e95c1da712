﻿import React, { useEffect, useState } from 'react';
import { View, StyleSheet, RefreshControl, ScrollView } from 'react-native';
import { Text, useTheme, Card, Button, Surface, Title } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '../../../lib/supabase';
import { useNavigation } from '@react-navigation/native';
import { format } from 'date-fns';

export const StaffDashboard = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    bookedToday: 0,
    cancelledToday: 0,
  });

  const fetchStats = async () => {
    try {
      setLoading(true);
      const todayStr = format(new Date(), 'yyyy-MM-dd');

      const { data, error } = await supabase
        .from('appointments')
        .select('id, status, date, appointment_date');
      if (error) throw error;

      const getDate = (apt: any) => apt.date || apt.appointment_date;

      const total = data?.length || 0;
      const bookedToday = (data || []).filter(
        (a: any) => ['scheduled', 'confirmed'].includes((a.status || '').toLowerCase()) && getDate(a) === todayStr
      ).length;
      const cancelledToday = (data || []).filter(
        (a: any) => (a.status || '').toLowerCase() === 'cancelled' && getDate(a) === todayStr
      ).length;

      setStats({ total, bookedToday, cancelledToday });
    } catch (e) {
      console.error('Failed to load staff stats', e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchStats();
    setRefreshing(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scroll}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Stats Row */}
        <View style={styles.statsRow}>
          <Surface style={[styles.statCard, { backgroundColor: '#E3F2FD' }]}>
            <Title style={styles.statValue}>{stats.bookedToday}</Title>
            <Text style={styles.statLabel}>Booked Today</Text>
          </Surface>
          <Surface style={[styles.statCard, { backgroundColor: '#FFEBEE' }]}>
            <Title style={styles.statValue}>{stats.cancelledToday}</Title>
            <Text style={styles.statLabel}>Cancelled Today</Text>
          </Surface>
          <Surface style={[styles.statCard, { backgroundColor: '#E8F5E9' }]}>
            <Title style={styles.statValue}>{stats.total}</Title>
            <Text style={styles.statLabel}>Total Appointments</Text>
          </Surface>
        </View>

        {/* Primary Actions */}
        <Card style={styles.actionsCard}>
          <Card.Content>
            <Button
              mode="contained"
              icon="plus"
              onPress={() => navigation.navigate('Appointments', { openCreate: true })}
              style={{ marginBottom: 12 }}
            >
              Book Appointment
            </Button>
            <Button
              mode="outlined"
              icon="calendar"
              onPress={() => navigation.navigate('Appointments')}
            >
              View All Appointments
            </Button>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  scroll: { padding: 16 },
  statsRow: { flexDirection: 'row', gap: 12, marginBottom: 16 },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statValue: { fontSize: 28, fontWeight: 'bold' },
  statLabel: { fontSize: 12, color: '#555' },
  actionsCard: { borderRadius: 12 },
});
