import React, { useState, useEffect } from 'react';
import {
  View,
  <PERSON>rollView,
  StyleSheet,
  RefreshControl,
  FlatList,
  Alert,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  Searchbar,
  Chip,
  IconButton,
  List,
  Divider,
  Portal,
  Modal,
  FAB,
  SegmentedButtons,
  DataTable,
  Badge,
  Surface,
  Avatar,
  Menu,
  TextInput,
  RadioButton,
  Switch,
  ActivityIndicator,
  AnimatedFAB,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../../lib/supabase';
import { format, parseISO, differenceInYears, isValid } from 'date-fns';
import { UserRole, UserStatus } from '../../types';

interface Patient {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  date_of_birth?: string;
  age?: number;
  gender?: string;
  blood_group?: string;
  allergies?: string;
  medical_conditions?: string;
  emergency_contact?: string;
  insurance_provider?: string;
  insurance_number?: string;
  created_at: string;
  last_visit?: string;
  total_visits?: number;
  upcoming_appointments?: number;
  status: UserStatus;
  avatar_url?: string;
  notes?: string;
}

interface Appointment {
  id: string;
  appointment_date: string;
  appointment_time: string;
  appointment_type: string;
  status: string;
  dentist_name?: string;
}

interface Treatment {
  id: string;
  treatment_date: string;
  treatment_type: string;
  dentist_name?: string;
  cost?: number;
  status: string;
  notes?: string;
}

interface PatientStats {
  totalPatients: number;
  activePatients: number;
  newThisMonth: number;
  withAppointments: number;
}

export const PatientManagement = () => {
  const theme = useTheme();
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [patientAppointments, setPatientAppointments] = useState<Appointment[]>([]);
  const [patientTreatments, setPatientTreatments] = useState<Treatment[]>([]);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [notesModalVisible, setNotesModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'info' | 'appointments' | 'treatments' | 'medical'>('info');
  
  // Filters
  const [genderFilter, setGenderFilter] = useState('all');
  const [ageFilter, setAgeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'visits'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  const [stats, setStats] = useState<PatientStats>({
    totalPatients: 0,
    activePatients: 0,
    newThisMonth: 0,
    withAppointments: 0,
  });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    date_of_birth: '',
    gender: '',
    blood_group: '',
    allergies: '',
    medical_conditions: '',
    emergency_contact: '',
    insurance_provider: '',
    insurance_number: '',
    notes: '',
  });

  const [isExtended, setIsExtended] = useState(true);

  useEffect(() => {
    fetchPatients();
  }, []);

  useEffect(() => {
    applyFiltersAndSort();
  }, [patients, searchQuery, genderFilter, ageFilter, statusFilter, sortBy, sortOrder]);

  useEffect(() => {
    calculateStats();
  }, [patients]);

  const fetchPatients = async () => {
    setLoading(true);
    try {
      // Fetch patients
      const { data: patientsData, error: patientsError } = await supabase
        .from('users')
        .select('*')
        .eq('role', UserRole.PATIENT)
        .order('created_at', { ascending: false });

      if (patientsError) throw patientsError;

      // Fetch appointment counts for each patient
      const { data: appointmentsData, error: appointmentsError } = await supabase
        .from('appointments')
        .select('patient_id, id, appointment_date, status')
        .gte('appointment_date', new Date().toISOString().split('T')[0]);

      if (appointmentsError) throw appointmentsError;

      // Process patients with additional computed fields
      const processedPatients = patientsData?.map(patient => {
        const patientAppointments = appointmentsData?.filter(apt => apt.patient_id === patient.id) || [];
        const upcomingAppointments = patientAppointments.filter(apt => apt.status !== 'cancelled').length;
        
        let age = null;
        if (patient.date_of_birth) {
          const birthDate = parseISO(patient.date_of_birth);
          if (isValid(birthDate)) {
            age = differenceInYears(new Date(), birthDate);
          }
        }

        return {
          ...patient,
          age,
          upcoming_appointments: upcomingAppointments,
          total_visits: 0, // This would come from treatment history
        };
      }) || [];

      setPatients(processedPatients);
    } catch (error) {
      console.error('Error fetching patients:', error);
      Alert.alert('Error', 'Failed to fetch patients');
    } finally {
      setLoading(false);
    }
  };

  const fetchPatientDetails = async (patientId: string) => {
    try {
      // Fetch appointments
      const { data: appointments, error: appointmentsError } = await supabase
        .from('appointments')
        .select(`
          *,
          dentist:dentist_id (
            name
          )
        `)
        .eq('patient_id', patientId)
        .order('appointment_date', { ascending: false })
        .limit(10);

      if (appointmentsError) throw appointmentsError;

      const mappedAppointments = appointments?.map(apt => ({
        ...apt,
        dentist_name: apt.dentist?.name,
      })) || [];

      setPatientAppointments(mappedAppointments);

      // Fetch treatments (if you have a treatments table)
      // For now, we'll use completed appointments as treatments
      const treatments = appointments
        ?.filter(apt => apt.status === 'completed')
        .map(apt => ({
          id: apt.id,
          treatment_date: apt.appointment_date,
          treatment_type: apt.appointment_type,
          dentist_name: apt.dentist?.name,
          status: 'completed',
          notes: apt.notes,
        })) || [];

      setPatientTreatments(treatments);
    } catch (error) {
      console.error('Error fetching patient details:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchPatients();
    setRefreshing(false);
  };

  const applyFiltersAndSort = () => {
    let filtered = [...patients];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(patient =>
        patient.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        patient.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        patient.phone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        patient.id.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Gender filter
    if (genderFilter !== 'all') {
      filtered = filtered.filter(patient => patient.gender === genderFilter);
    }

    // Age filter
    if (ageFilter !== 'all') {
      filtered = filtered.filter(patient => {
        if (!patient.age) return false;
        switch (ageFilter) {
          case 'child':
            return patient.age < 18;
          case 'adult':
            return patient.age >= 18 && patient.age < 60;
          case 'senior':
            return patient.age >= 60;
          default:
            return true;
        }
      });
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(patient => patient.status === statusFilter);
    }

    // Sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          break;
        case 'visits':
          comparison = (b.total_visits || 0) - (a.total_visits || 0);
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredPatients(filtered);
  };

  const calculateStats = () => {
    const now = new Date();
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    const activePatients = patients.filter(p => p.status === UserStatus.ACTIVE).length;
    const newThisMonth = patients.filter(p => 
      new Date(p.created_at) >= monthStart
    ).length;
    const withAppointments = patients.filter(p => 
      (p.upcoming_appointments || 0) > 0
    ).length;

    setStats({
      totalPatients: patients.length,
      activePatients,
      newThisMonth,
      withAppointments,
    });
  };

  const handleCreatePatient = async () => {
    if (!formData.name || !formData.email) {
      Alert.alert('Error', 'Name and email are required');
      return;
    }

    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: 'TempPassword123!', // Temporary password
        options: {
          data: {
            name: formData.name,
            role: UserRole.PATIENT,
          },
        },
      });

      if (authError) throw authError;

      // Create user profile
      const { error: profileError } = await supabase.from('users').insert({
        id: authData.user!.id,
        email: formData.email,
        name: formData.name,
        phone: formData.phone,
        address: formData.address,
        date_of_birth: formData.date_of_birth || null,
        gender: formData.gender,
        blood_group: formData.blood_group,
        allergies: formData.allergies,
        medical_conditions: formData.medical_conditions,
        emergency_contact: formData.emergency_contact,
        insurance_provider: formData.insurance_provider,
        insurance_number: formData.insurance_number,
        role: UserRole.PATIENT,
        status: UserStatus.ACTIVE,
      });

      if (profileError) throw profileError;

      Alert.alert('Success', 'Patient created successfully');
      setCreateModalVisible(false);
      resetForm();
      fetchPatients();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to create patient');
    }
  };

  const handleUpdatePatient = async () => {
    if (!selectedPatient) return;

    try {
      const { error } = await supabase
        .from('users')
        .update({
          name: formData.name,
          phone: formData.phone,
          address: formData.address,
          date_of_birth: formData.date_of_birth || null,
          gender: formData.gender,
          blood_group: formData.blood_group,
          allergies: formData.allergies,
          medical_conditions: formData.medical_conditions,
          emergency_contact: formData.emergency_contact,
          insurance_provider: formData.insurance_provider,
          insurance_number: formData.insurance_number,
          updated_at: new Date().toISOString(),
        })
        .eq('id', selectedPatient.id);

      if (error) throw error;

      Alert.alert('Success', 'Patient updated successfully');
      setEditModalVisible(false);
      resetForm();
      fetchPatients();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update patient');
    }
  };

  const handleDeletePatient = (patient: Patient) => {
    Alert.alert(
      'Delete Patient',
      `Are you sure you want to delete ${patient.name}? This will also delete all related appointments and records.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('users')
                .delete()
                .eq('id', patient.id);

              if (error) throw error;

              Alert.alert('Success', 'Patient deleted successfully');
              fetchPatients();
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete patient');
            }
          },
        },
      ]
    );
  };

  const handleSaveNotes = async () => {
    if (!selectedPatient) return;

    try {
      const { error } = await supabase
        .from('users')
        .update({ 
          notes: formData.notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedPatient.id);

      if (error) throw error;

      Alert.alert('Success', 'Notes saved successfully');
      setNotesModalVisible(false);
      fetchPatients();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save notes');
    }
  };

  const openEditModal = (patient: Patient) => {
    setSelectedPatient(patient);
    setFormData({
      name: patient.name,
      email: patient.email,
      phone: patient.phone || '',
      address: patient.address || '',
      date_of_birth: patient.date_of_birth || '',
      gender: patient.gender || '',
      blood_group: patient.blood_group || '',
      allergies: patient.allergies || '',
      medical_conditions: patient.medical_conditions || '',
      emergency_contact: patient.emergency_contact || '',
      insurance_provider: patient.insurance_provider || '',
      insurance_number: patient.insurance_number || '',
      notes: patient.notes || '',
    });
    setEditModalVisible(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      address: '',
      date_of_birth: '',
      gender: '',
      blood_group: '',
      allergies: '',
      medical_conditions: '',
      emergency_contact: '',
      insurance_provider: '',
      insurance_number: '',
      notes: '',
    });
    setSelectedPatient(null);
  };

  const handleCall = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  const handleEmail = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const handleWhatsApp = (phone: string) => {
    Linking.openURL(`whatsapp://send?phone=${phone}`);
  };

  const renderPatientCard = ({ item }: { item: Patient }) => {
    const initials = item.name.split(' ').map(n => n[0]).join('').toUpperCase();
    
    return (
      <Card 
        style={styles.patientCard}
        onPress={() => {
          setSelectedPatient(item);
          setActiveTab('info');
          fetchPatientDetails(item.id);
          setDetailsModalVisible(true);
        }}
      >
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.patientHeader}>
              {item.avatar_url ? (
                <Avatar.Image size={48} source={{ uri: item.avatar_url }} />
              ) : (
                <Avatar.Text size={48} label={initials} />
              )}
              <View style={styles.patientInfo}>
                <Text style={styles.patientName}>{item.name}</Text>
                <Text style={styles.patientId}>ID: {item.id.substring(0, 8)}</Text>
                {item.age && (
                  <View style={styles.metaRow}>
                    <Chip style={styles.metaChip} textStyle={styles.metaChipText}>
                      {item.age} yrs
                    </Chip>
                    {item.gender && (
                      <Chip style={styles.metaChip} textStyle={styles.metaChipText}>
                        {item.gender}
                      </Chip>
                    )}
                    {item.blood_group && (
                      <Chip style={styles.metaChip} textStyle={styles.metaChipText}>
                        {item.blood_group}
                      </Chip>
                    )}
                  </View>
                )}
              </View>
            </View>
            
            <IconButton
              icon="dots-vertical"
              onPress={() => {
                setSelectedPatient(item);
                openEditModal(item);
              }}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.contactInfo}>
            {item.phone && (
              <TouchableOpacity 
                style={styles.contactItem}
                onPress={() => handleCall(item.phone!)}
              >
                <MaterialCommunityIcons name="phone" size={16} color="#4CAF50" />
                <Text style={styles.contactText}>{item.phone}</Text>
              </TouchableOpacity>
            )}
            
            {item.email && (
              <TouchableOpacity 
                style={styles.contactItem}
                onPress={() => handleEmail(item.email)}
              >
                <MaterialCommunityIcons name="email" size={16} color="#2196F3" />
                <Text style={styles.contactText}>{item.email}</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.cardFooter}>
            <View style={styles.statsRow}>
              {item.upcoming_appointments !== undefined && item.upcoming_appointments > 0 && (
                <Badge style={styles.statBadge}>{`${item.upcoming_appointments} upcoming`}</Badge>
              )}
              {item.total_visits !== undefined && item.total_visits > 0 && (
                <Badge style={[styles.statBadge, { backgroundColor: '#4CAF50' }]}>
                  {`${item.total_visits} visits`}
                </Badge>
              )}
            </View>
            
            <View style={styles.quickActions}>
              <IconButton
                icon="calendar-plus"
                size={20}
                onPress={() => {
                  // Navigate to book appointment with patient pre-selected
                  Alert.alert('Book Appointment', `Book appointment for ${item.name}`);
                }}
              />
              <IconButton
                icon="note-text"
                size={20}
                onPress={() => {
                  setSelectedPatient(item);
                  setFormData({ ...formData, notes: item.notes || '' });
                  setNotesModalVisible(true);
                }}
              />
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderDetailsModal = () => {
    if (!selectedPatient) return null;
    
    return (
      <Portal>
        <Modal
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            {/* Patient Header */}
            <Surface style={styles.modalHeader} elevation={1}>
              {selectedPatient.avatar_url ? (
                <Avatar.Image size={80} source={{ uri: selectedPatient.avatar_url }} />
              ) : (
                <Avatar.Text 
                  size={80} 
                  label={selectedPatient.name.split(' ').map(n => n[0]).join('').toUpperCase()} 
                />
              )}
              <View style={styles.modalHeaderInfo}>
                <Title>{selectedPatient.name}</Title>
                <Text style={styles.patientIdModal}>ID: {selectedPatient.id}</Text>
                <View style={styles.actionButtons}>
                  {selectedPatient.phone && (
                    <IconButton
                      icon="phone"
                      size={20}
                      onPress={() => handleCall(selectedPatient.phone!)}
                      style={styles.actionButton}
                    />
                  )}
                  {selectedPatient.email && (
                    <IconButton
                      icon="email"
                      size={20}
                      onPress={() => handleEmail(selectedPatient.email)}
                      style={styles.actionButton}
                    />
                  )}
                  {selectedPatient.phone && (
                    <IconButton
                      icon="whatsapp"
                      size={20}
                      onPress={() => handleWhatsApp(selectedPatient.phone!)}
                      style={styles.actionButton}
                    />
                  )}
                </View>
              </View>
            </Surface>

            {/* Tabs */}
            <SegmentedButtons
              value={activeTab}
              onValueChange={(value) => setActiveTab(value as any)}
              buttons={[
                { value: 'info', label: 'Info' },
                { value: 'appointments', label: 'Appointments' },
                { value: 'treatments', label: 'Treatments' },
                { value: 'medical', label: 'Medical' },
              ]}
              style={styles.tabs}
            />

            {/* Tab Content */}
            <View style={styles.tabContent}>
              {activeTab === 'info' && (
                <List.Section>
                  <List.Item
                    title="Phone"
                    description={selectedPatient.phone || 'Not provided'}
                    left={props => <List.Icon {...props} icon="phone" />}
                  />
                  <List.Item
                    title="Email"
                    description={selectedPatient.email}
                    left={props => <List.Icon {...props} icon="email" />}
                  />
                  <List.Item
                    title="Address"
                    description={selectedPatient.address || 'Not provided'}
                    left={props => <List.Icon {...props} icon="map-marker" />}
                  />
                  <List.Item
                    title="Date of Birth"
                    description={
                      selectedPatient.date_of_birth
                        ? `${format(parseISO(selectedPatient.date_of_birth), 'MMM d, yyyy')} (${selectedPatient.age} years)`
                        : 'Not provided'
                    }
                    left={props => <List.Icon {...props} icon="cake" />}
                  />
                  <List.Item
                    title="Gender"
                    description={selectedPatient.gender || 'Not specified'}
                    left={props => <List.Icon {...props} icon="gender-male-female" />}
                  />
                  <List.Item
                    title="Member Since"
                    description={format(parseISO(selectedPatient.created_at), 'MMMM d, yyyy')}
                    left={props => <List.Icon {...props} icon="calendar-account" />}
                  />
                </List.Section>
              )}

              {activeTab === 'appointments' && (
                <View>
                  {patientAppointments.length > 0 ? (
                    patientAppointments.map((apt, index) => (
                      <Surface key={apt.id} style={styles.appointmentItem} elevation={1}>
                        <View style={styles.appointmentHeader}>
                          <Text style={styles.appointmentDate}>
                            {format(parseISO(apt.appointment_date), 'MMM d, yyyy')}
                          </Text>
                          <Chip style={styles.statusChip} textStyle={styles.statusChipText}>
                            {apt.status}
                          </Chip>
                        </View>
                        <Text style={styles.appointmentTime}>{apt.appointment_time}</Text>
                        <Text style={styles.appointmentType}>{apt.appointment_type}</Text>
                        {apt.dentist_name && (
                          <Text style={styles.appointmentDentist}>Dr. {apt.dentist_name}</Text>
                        )}
                      </Surface>
                    ))
                  ) : (
                    <Text style={styles.emptyText}>No appointments found</Text>
                  )}
                </View>
              )}

              {activeTab === 'treatments' && (
                <View>
                  {patientTreatments.length > 0 ? (
                    patientTreatments.map((treatment, index) => (
                      <Surface key={treatment.id} style={styles.treatmentItem} elevation={1}>
                        <View style={styles.treatmentHeader}>
                          <Text style={styles.treatmentDate}>
                            {format(parseISO(treatment.treatment_date), 'MMM d, yyyy')}
                          </Text>
                          {treatment.cost && (
                            <Text style={styles.treatmentCost}>₹{treatment.cost}</Text>
                          )}
                        </View>
                        <Text style={styles.treatmentType}>{treatment.treatment_type}</Text>
                        {treatment.dentist_name && (
                          <Text style={styles.treatmentDentist}>Dr. {treatment.dentist_name}</Text>
                        )}
                        {treatment.notes && (
                          <Text style={styles.treatmentNotes}>{treatment.notes}</Text>
                        )}
                      </Surface>
                    ))
                  ) : (
                    <Text style={styles.emptyText}>No treatment history found</Text>
                  )}
                </View>
              )}

              {activeTab === 'medical' && (
                <List.Section>
                  <List.Item
                    title="Blood Group"
                    description={selectedPatient.blood_group || 'Not provided'}
                    left={props => <List.Icon {...props} icon="water" />}
                  />
                  <List.Item
                    title="Allergies"
                    description={selectedPatient.allergies || 'None reported'}
                    left={props => <List.Icon {...props} icon="alert-circle" />}
                  />
                  <List.Item
                    title="Medical Conditions"
                    description={selectedPatient.medical_conditions || 'None reported'}
                    left={props => <List.Icon {...props} icon="medical-bag" />}
                  />
                  <List.Item
                    title="Emergency Contact"
                    description={selectedPatient.emergency_contact || 'Not provided'}
                    left={props => <List.Icon {...props} icon="phone-alert" />}
                  />
                  <List.Item
                    title="Insurance Provider"
                    description={selectedPatient.insurance_provider || 'Not provided'}
                    left={props => <List.Icon {...props} icon="shield-check" />}
                  />
                  {selectedPatient.insurance_number && (
                    <List.Item
                      title="Insurance Number"
                      description={selectedPatient.insurance_number}
                      left={props => <List.Icon {...props} icon="numeric" />}
                    />
                  )}
                </List.Section>
              )}
            </View>

            {/* Modal Actions */}
            <View style={styles.modalActions}>
              <Button
                mode="outlined"
                onPress={() => openEditModal(selectedPatient)}
                icon="pencil"
              >
                Edit
              </Button>
              <Button
                mode="contained"
                onPress={() => {
                  setDetailsModalVisible(false);
                  // Navigate to book appointment
                  Alert.alert('Book Appointment', `Book appointment for ${selectedPatient.name}`);
                }}
                icon="calendar-plus"
              >
                Book Appointment
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading patients...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={styles.title}>Patient Management</Title>
        <View style={styles.headerActions}>
          <SegmentedButtons
            value={viewMode}
            onValueChange={(value) => setViewMode(value as any)}
            buttons={[
              { value: 'list', icon: 'format-list-bulleted' },
              { value: 'grid', icon: 'view-grid' },
            ]}
            style={styles.viewToggle}
          />
        </View>
      </View>

      {/* Stats */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.statsContainer}
      >
        <Surface style={[styles.statCard, { backgroundColor: '#E3F2FD' }]} elevation={1}>
          <MaterialCommunityIcons name="account-group" size={24} color="#2196F3" />
          <Text style={styles.statValue}>{stats.totalPatients}</Text>
          <Text style={styles.statLabel}>Total Patients</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#E8F5E9' }]} elevation={1}>
          <MaterialCommunityIcons name="account-check" size={24} color="#4CAF50" />
          <Text style={styles.statValue}>{stats.activePatients}</Text>
          <Text style={styles.statLabel}>Active</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#FFF3E0' }]} elevation={1}>
          <MaterialCommunityIcons name="account-plus" size={24} color="#FF9800" />
          <Text style={styles.statValue}>{stats.newThisMonth}</Text>
          <Text style={styles.statLabel}>New This Month</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#F3E5F5' }]} elevation={1}>
          <MaterialCommunityIcons name="calendar-account" size={24} color="#9C27B0" />
          <Text style={styles.statValue}>{stats.withAppointments}</Text>
          <Text style={styles.statLabel}>With Appointments</Text>
        </Surface>
      </ScrollView>

      {/* Search and Filters */}
      <View style={styles.filters}>
        <Searchbar
          placeholder="Search patients..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        {/* Filter Chips */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          <Chip
            selected={genderFilter === 'all'}
            onPress={() => setGenderFilter('all')}
            style={styles.filterChip}
          >
            All Genders
          </Chip>
          <Chip
            selected={genderFilter === 'male'}
            onPress={() => setGenderFilter('male')}
            style={styles.filterChip}
          >
            Male
          </Chip>
          <Chip
            selected={genderFilter === 'female'}
            onPress={() => setGenderFilter('female')}
            style={styles.filterChip}
          >
            Female
          </Chip>
          <Chip
            selected={genderFilter === 'other'}
            onPress={() => setGenderFilter('other')}
            style={styles.filterChip}
          >
            Other
          </Chip>
        </ScrollView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          <Chip
            selected={ageFilter === 'all'}
            onPress={() => setAgeFilter('all')}
            style={styles.filterChip}
          >
            All Ages
          </Chip>
          <Chip
            selected={ageFilter === 'child'}
            onPress={() => setAgeFilter('child')}
            style={styles.filterChip}
            icon="baby-face"
          >
            Children (0-17)
          </Chip>
          <Chip
            selected={ageFilter === 'adult'}
            onPress={() => setAgeFilter('adult')}
            style={styles.filterChip}
            icon="account"
          >
            Adults (18-59)
          </Chip>
          <Chip
            selected={ageFilter === 'senior'}
            onPress={() => setAgeFilter('senior')}
            style={styles.filterChip}
            icon="account-clock"
          >
            Seniors (60+)
          </Chip>
        </ScrollView>

        {/* Sort Options */}
        <View style={styles.sortRow}>
          <Text style={styles.sortLabel}>Sort by:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.sortOptions}>
            <Chip
              selected={sortBy === 'name'}
              onPress={() => setSortBy('name')}
              style={styles.sortChip}
            >
              Name
            </Chip>
            <Chip
              selected={sortBy === 'date'}
              onPress={() => setSortBy('date')}
              style={styles.sortChip}
            >
              Join Date
            </Chip>
            <Chip
              selected={sortBy === 'visits'}
              onPress={() => setSortBy('visits')}
              style={styles.sortChip}
            >
              Visits
            </Chip>
          </ScrollView>
          <IconButton
            icon={sortOrder === 'asc' ? 'sort-ascending' : 'sort-descending'}
            onPress={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          />
        </View>
      </View>

      {/* Patient List */}
      {filteredPatients.length > 0 ? (
        <FlatList
          data={filteredPatients}
          renderItem={renderPatientCard}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          numColumns={viewMode === 'grid' ? 2 : 1}
          key={viewMode} // Force re-render when view mode changes
        />
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="account-search" size={64} color="#ccc" />
          <Text style={styles.emptyTitle}>No patients found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery ? 'Try adjusting your search or filters' : 'Add your first patient'}
          </Text>
        </View>
      )}

      {/* FAB */}
      <AnimatedFAB
        icon="plus"
        label="Add Patient"
        extended={isExtended}
        onPress={() => {
          resetForm();
          setCreateModalVisible(true);
        }}
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}

      />

      {/* Modals */}
      {renderDetailsModal()}

      {/* Create/Edit Modal */}
      <Portal>
        <Modal
          visible={createModalVisible || editModalVisible}
          onDismiss={() => {
            setCreateModalVisible(false);
            setEditModalVisible(false);
          }}
          contentContainerStyle={styles.formModal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>
              {createModalVisible ? 'Add New Patient' : 'Edit Patient'}
            </Title>

            <TextInput
              label="Full Name *"
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              disabled={editModalVisible}
              style={styles.input}
            />

            <TextInput
              label="Phone"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              mode="outlined"
              keyboardType="phone-pad"
              style={styles.input}
            />

            <TextInput
              label="Address"
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.input}
            />

            <TextInput
              label="Date of Birth (YYYY-MM-DD)"
              value={formData.date_of_birth}
              onChangeText={(text) => setFormData({ ...formData, date_of_birth: text })}
              mode="outlined"
              placeholder="1990-01-01"
              style={styles.input}
            />

            <Text style={styles.fieldLabel}>Gender</Text>
            <RadioButton.Group
              onValueChange={(value) => setFormData({ ...formData, gender: value })}
              value={formData.gender}
            >
              <View style={styles.radioRow}>
                <RadioButton.Item label="Male" value="male" />
                <RadioButton.Item label="Female" value="female" />
                <RadioButton.Item label="Other" value="other" />
              </View>
            </RadioButton.Group>

            <TextInput
              label="Blood Group"
              value={formData.blood_group}
              onChangeText={(text) => setFormData({ ...formData, blood_group: text })}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Allergies"
              value={formData.allergies}
              onChangeText={(text) => setFormData({ ...formData, allergies: text })}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.input}
            />

            <TextInput
              label="Medical Conditions"
              value={formData.medical_conditions}
              onChangeText={(text) => setFormData({ ...formData, medical_conditions: text })}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.input}
            />

            <TextInput
              label="Emergency Contact"
              value={formData.emergency_contact}
              onChangeText={(text) => setFormData({ ...formData, emergency_contact: text })}
              mode="outlined"
              keyboardType="phone-pad"
              style={styles.input}
            />

            <TextInput
              label="Insurance Provider"
              value={formData.insurance_provider}
              onChangeText={(text) => setFormData({ ...formData, insurance_provider: text })}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Insurance Number"
              value={formData.insurance_number}
              onChangeText={(text) => setFormData({ ...formData, insurance_number: text })}
              mode="outlined"
              style={styles.input}
            />

            <View style={styles.modalButtons}>
              <Button
                mode="outlined"
                onPress={() => {
                  setCreateModalVisible(false);
                  setEditModalVisible(false);
                }}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={createModalVisible ? handleCreatePatient : handleUpdatePatient}
              >
                {createModalVisible ? 'Create' : 'Update'}
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Notes Modal */}
      <Portal>
        <Modal
          visible={notesModalVisible}
          onDismiss={() => setNotesModalVisible(false)}
          contentContainerStyle={styles.notesModal}
        >
          <Title style={styles.modalTitle}>Patient Notes</Title>
          <TextInput
            label="Notes"
            value={formData.notes}
            onChangeText={(text) => setFormData({ ...formData, notes: text })}
            mode="outlined"
            multiline
            numberOfLines={6}
            style={styles.notesInput}
          />
          <View style={styles.modalButtons}>
            <Button
              mode="outlined"
              onPress={() => setNotesModalVisible(false)}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveNotes}
            >
              Save Notes
            </Button>
          </View>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewToggle: {
    width: 100,
  },
  statsContainer: {
    padding: 16,
    maxHeight: 120,
  },
  statCard: {
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 120,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  filters: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    elevation: 1,
  },
  searchBar: {
    marginBottom: 8,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  filterRow: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  filterChip: {
    marginRight: 8,
  },
  sortRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  sortLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  sortOptions: {
    flex: 1,
    flexDirection: 'row',
  },
  sortChip: {
    marginRight: 8,
  },
  listContainer: {
    padding: 16,
  },
  patientCard: {
    marginBottom: 12,
    elevation: 2,
  },
  cardHeader: {
    marginBottom: 8,
  },
  patientHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  patientInfo: {
    marginLeft: 12,
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  patientId: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  metaRow: {
    flexDirection: 'row',
    marginTop: 6,
    gap: 4,
  },
  metaChip: {
    height: 22,
    backgroundColor: '#E3F2FD',
  },
  metaChipText: {
    fontSize: 10,
    color: '#2196F3',
  },
  divider: {
    marginVertical: 8,
  },
  contactInfo: {
    marginBottom: 8,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  contactText: {
    marginLeft: 6,
    fontSize: 13,
    color: '#666',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 8,
  },
  statBadge: {
    backgroundColor: '#2196F3',
  },
  quickActions: {
    flexDirection: 'row',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#aaa',
    marginTop: 8,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    backgroundColor: '#fff',
    margin: 20,
    borderRadius: 8,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 8,
  },
  modalHeaderInfo: {
    marginLeft: 16,
    flex: 1,
  },
  patientIdModal: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 8,
  },
  actionButton: {
    margin: 0,
  },
  tabs: {
    marginHorizontal: 20,
    marginTop: 16,
  },
  tabContent: {
    padding: 20,
  },
  appointmentItem: {
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  appointmentDate: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  statusChip: {
    height: 20,
  },
  statusChipText: {
    fontSize: 10,
  },
  appointmentTime: {
    fontSize: 13,
    color: '#666',
  },
  appointmentType: {
    fontSize: 13,
    fontWeight: '500',
    marginTop: 2,
  },
  appointmentDentist: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  treatmentItem: {
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
  },
  treatmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  treatmentDate: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  treatmentCost: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  treatmentType: {
    fontSize: 13,
    fontWeight: '500',
  },
  treatmentDentist: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  treatmentNotes: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
    fontStyle: 'italic',
  },
  emptyText: {
    textAlign: 'center',
    color: '#999',
    marginTop: 20,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    gap: 12,
  },
  formModal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#666',
  },
  radioRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 12,
  },
  notesModal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  notesInput: {
    marginVertical: 16,
  },
});
