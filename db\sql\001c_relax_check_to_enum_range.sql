-- 001c_relax_check_to_enum_range.sql
-- Purpose: Replace users.role CHECK with one that accepts any value of enum public.user_role.
-- This does not further restrict beyond the enum and will never fail due to existing rows.

BEGIN;
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE public.users
  ADD CONSTRAINT users_role_check
  CHECK (role = ANY(enum_range(NULL::public.user_role)));
COMMIT;
