import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Avatar, Menu, Divider, Text, useTheme } from 'react-native-paper';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

export const HeaderProfile = () => {
  const theme = useTheme();
  const { user, signOut } = useAuth();
  const navigation = useNavigation<any>();
  const [visible, setVisible] = useState(false);

  const openMenu = () => setVisible(true);
  const closeMenu = () => setVisible(false);

  const handleLogout = () => {
    closeMenu();
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
            } catch (error) {
              console.error('Logout error:', error);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleProfile = () => {
    closeMenu();
    navigation.navigate('Profile');
  };

  const getInitials = () => {
    if (user?.name) {
      const names = user.name.split(' ');
      if (names.length > 1) {
        return `${names[0][0]}${names[1][0]}`.toUpperCase();
      }
      return user.name.substring(0, 2).toUpperCase();
    }
    return 'U';
  };

  return (
    <View style={styles.container}>
      <Menu
        visible={visible}
        onDismiss={closeMenu}
        anchor={
          <TouchableOpacity onPress={openMenu} style={styles.avatarButton}>
            <Avatar.Text
              size={36}
              label={getInitials()}
              style={{ backgroundColor: theme.colors.primary }}
            />
          </TouchableOpacity>
        }
        contentStyle={styles.menuContent}
      >
        <View style={styles.menuHeader}>
          <Avatar.Text
            size={48}
            label={getInitials()}
            style={{ backgroundColor: theme.colors.primary }}
          />
          <Text style={styles.userName}>{user?.name || 'User'}</Text>
          <Text style={styles.userEmail}>{user?.email}</Text>
          <Text style={styles.userRole}>{user?.role?.toUpperCase()}</Text>
        </View>
        
        <Divider />
        
        <Menu.Item
          onPress={handleProfile}
          title="My Profile"
          leadingIcon={() => (
            <MaterialCommunityIcons name="account" size={20} color={theme.colors.onSurface} />
          )}
        />
        
        <Menu.Item
          onPress={() => {
            closeMenu();
            navigation.navigate('Notifications');
          }}
          title="Notifications"
          leadingIcon={() => (
            <MaterialCommunityIcons name="bell" size={20} color={theme.colors.onSurface} />
          )}
        />
        
        <Divider />
        
        <Menu.Item
          onPress={handleLogout}
          title="Logout"
          titleStyle={{ color: theme.colors.error }}
          leadingIcon={() => (
            <MaterialCommunityIcons name="logout" size={20} color={theme.colors.error} />
          )}
        />
      </Menu>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 8,
  },
  avatarButton: {
    padding: 4,
  },
  menuContent: {
    marginTop: 40,
    minWidth: 200,
  },
  menuHeader: {
    alignItems: 'center',
    padding: 16,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
  },
  userEmail: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  userRole: {
    fontSize: 10,
    color: '#999',
    marginTop: 4,
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
});
