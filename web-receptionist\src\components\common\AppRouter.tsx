import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import LoginPage from '../../pages/LoginPage'
import Dashboard from '../../pages/Dashboard'
import Appointments from '../../pages/Appointments'
import Patients from '../../pages/Patients'
import Calendar from '../../pages/Calendar'
import Settings from '../../pages/Settings'
import Layout from './Layout'
import TestLogin from './TestLogin'

const AppRouter: React.FC = () => {
  const { user, loading } = useAuth()

  // Add debugging
  useEffect(() => {
    console.log('AppRouter - user:', user)
    console.log('AppRouter - loading:', loading)
  }, [user, loading])

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px',
        color: '#666',
        flexDirection: 'column'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div>Loading Receptionist Portal...</div>
          <div style={{ fontSize: '14px', marginTop: '10px', color: '#999' }}>
            Checking authentication status...
          </div>
          <div style={{ fontSize: '12px', marginTop: '20px', color: '#ccc' }}>
            If this takes too long, try:
          </div>
          <div style={{ marginTop: '10px' }}>
            <a href="/test" style={{ color: '#2874ba', textDecoration: 'none', margin: '0 10px' }}>
              Test Login
            </a>
            <span style={{ color: '#ccc' }}>|</span>
            <a href="/?debug=true" style={{ color: '#2874ba', textDecoration: 'none', margin: '0 10px' }}>
              Debug Mode
            </a>
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/test" element={<TestLogin />} />
          <Route path="/bypass" element={
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <h2>Quick Access</h2>
              <p>Choose how to access the receptionist portal:</p>
              <div style={{ margin: '20px 0' }}>
                <a
                  href="/?debug=true"
                  style={{
                    display: 'inline-block',
                    padding: '12px 24px',
                    margin: '10px',
                    backgroundColor: '#2874ba',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '8px'
                  }}
                >
                  🚀 Quick Start (Debug Mode)
                </a>
              </div>
              <div style={{ margin: '20px 0' }}>
                <a
                  href="/test"
                  style={{
                    display: 'inline-block',
                    padding: '12px 24px',
                    margin: '10px',
                    backgroundColor: '#3dbc98',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '8px'
                  }}
                >
                  🔐 Test Login
                </a>
              </div>
              <div style={{ margin: '20px 0' }}>
                <a
                  href="/login"
                  style={{
                    display: 'inline-block',
                    padding: '12px 24px',
                    margin: '10px',
                    backgroundColor: '#666',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '8px'
                  }}
                >
                  🏥 Regular Login
                </a>
              </div>
            </div>
          } />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    )
  }

  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/appointments" element={<Appointments />} />
          <Route path="/patients" element={<Patients />} />
          <Route path="/calendar" element={<Calendar />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default AppRouter