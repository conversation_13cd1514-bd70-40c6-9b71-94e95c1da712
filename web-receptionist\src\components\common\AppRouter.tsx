import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import LoginPage from '../../pages/LoginPage'
import Dashboard from '../../pages/Dashboard'
import Appointments from '../../pages/Appointments'
import Patients from '../../pages/Patients'
import Calendar from '../../pages/Calendar'
import Settings from '../../pages/Settings'
import Layout from './Layout'
import TestLogin from './TestLogin'

const AppRouter: React.FC = () => {
  const { user, loading } = useAuth()

  // Add debugging
  useEffect(() => {
    console.log('AppRouter - user:', user)
    console.log('AppRouter - loading:', loading)
  }, [user, loading])

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px',
        color: '#666'
      }}>
        <div>
          <div>Loading Receptionist Portal...</div>
          <div style={{ fontSize: '14px', marginTop: '10px', color: '#999' }}>
            Checking authentication status...
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/test" element={<TestLogin />} />
          <Route path="/bypass" element={
            <div style={{ padding: '20px' }}>
              <h2>Debug Mode</h2>
              <p>Authentication is currently disabled for testing.</p>
              <button
                onClick={() => {
                  // Create a mock user for testing
                  window.location.href = '/dashboard'
                }}
                style={{ padding: '10px', margin: '10px' }}
              >
                Continue to Dashboard (Debug Mode)
              </button>
              <p><a href="/login">Go to regular login</a></p>
            </div>
          } />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    )
  }

  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/appointments" element={<Appointments />} />
          <Route path="/patients" element={<Patients />} />
          <Route path="/calendar" element={<Calendar />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default AppRouter