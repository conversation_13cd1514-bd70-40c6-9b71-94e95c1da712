import React, { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import LoginPage from '../../pages/LoginPage'
import Dashboard from '../../pages/Dashboard'
import Appointments from '../../pages/Appointments'
import Patients from '../../pages/Patients'
import Calendar from '../../pages/Calendar'
import Settings from '../../pages/Settings'
import Layout from './Layout'


const AppRouter: React.FC = () => {
  const { user, loading } = useAuth()

  // Add debugging
  useEffect(() => {
    console.log('AppRouter - user:', user)
    console.log('AppRouter - loading:', loading)
  }, [user, loading])

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '18px',
        color: '#666',
        flexDirection: 'column'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div>Loading Receptionist Portal...</div>
          <div style={{ fontSize: '14px', marginTop: '10px', color: '#999' }}>
            Checking authentication status...
          </div>
          <div style={{ fontSize: '12px', marginTop: '20px', color: '#ccc' }}>
            Connecting to Dr. Shilpa's Dental Clinic database...
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    )
  }

  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/appointments" element={<Appointments />} />
          <Route path="/patients" element={<Patients />} />
          <Route path="/calendar" element={<Calendar />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Layout>
    </Router>
  )
}

export default AppRouter