import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  ViewStyle,
  Dimensions,
} from 'react-native';
import { Surface } from 'react-native-paper';
import { lightTheme } from '../../theme';

const { width: screenWidth } = Dimensions.get('window');

interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: any;
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
}

// Base Skeleton Component
export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius,
  style,
  variant = 'rectangular',
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const getBorderRadius = () => {
    if (borderRadius !== undefined) return borderRadius;
    switch (variant) {
      case 'circular':
        return 9999;
      case 'rounded':
        return 8;
      case 'text':
        return 4;
      default:
        return 0;
    }
  };

  const getHeight = () => {
    if (variant === 'circular' && typeof width === 'number') {
      return width;
    }
    return height;
  };

  return (
    <Animated.View
      style={[
        {
          width,
          height: getHeight(),
          borderRadius: getBorderRadius(),
          backgroundColor: '#E0E0E0',
          opacity,
        },
        style,
      ]}
    />
  );
};

// Card Skeleton
export const CardSkeleton: React.FC<{ style?: any }> = ({ style }) => {
  return (
    <Surface style={[styles.cardSkeleton, style]} elevation={1}>
      <View style={styles.cardHeader}>
        <Skeleton variant="circular" width={40} height={40} />
        <View style={styles.cardHeaderText}>
          <Skeleton width="60%" height={16} style={{ marginBottom: 8 }} />
          <Skeleton width="40%" height={12} />
        </View>
      </View>
      <Skeleton width="100%" height={100} style={{ marginVertical: 12 }} variant="rounded" />
      <Skeleton width="80%" height={14} style={{ marginBottom: 8 }} />
      <Skeleton width="60%" height={14} />
    </Surface>
  );
};

// List Item Skeleton
export const ListItemSkeleton: React.FC<{ style?: any }> = ({ style }) => {
  return (
    <View style={[styles.listItemSkeleton, style]}>
      <Skeleton variant="circular" width={48} height={48} />
      <View style={styles.listItemContent}>
        <Skeleton width="70%" height={16} style={{ marginBottom: 8 }} />
        <Skeleton width="50%" height={12} />
      </View>
      <Skeleton width={60} height={24} variant="rounded" />
    </View>
  );
};

// Appointment Card Skeleton
export const AppointmentCardSkeleton: React.FC<{ style?: any }> = ({ style }) => {
  return (
    <Surface style={[styles.appointmentCard, style]} elevation={2}>
      <View style={styles.appointmentHeader}>
        <Skeleton width="30%" height={14} />
        <Skeleton width={80} height={20} variant="rounded" />
      </View>
      <View style={styles.appointmentBody}>
        <View style={styles.appointmentRow}>
          <Skeleton variant="circular" width={24} height={24} />
          <Skeleton width="60%" height={14} style={{ marginLeft: 12 }} />
        </View>
        <View style={styles.appointmentRow}>
          <Skeleton variant="circular" width={24} height={24} />
          <Skeleton width="50%" height={14} style={{ marginLeft: 12 }} />
        </View>
        <View style={styles.appointmentRow}>
          <Skeleton variant="circular" width={24} height={24} />
          <Skeleton width="40%" height={14} style={{ marginLeft: 12 }} />
        </View>
      </View>
      <View style={styles.appointmentFooter}>
        <Skeleton width={100} height={36} variant="rounded" />
        <Skeleton width={100} height={36} variant="rounded" />
      </View>
    </Surface>
  );
};

// Patient Card Skeleton
export const PatientCardSkeleton: React.FC<{ style?: any }> = ({ style }) => {
  return (
    <Surface style={[styles.patientCard, style]} elevation={2}>
      <View style={styles.patientHeader}>
        <Skeleton variant="circular" width={60} height={60} />
        <View style={styles.patientInfo}>
          <Skeleton width="80%" height={18} style={{ marginBottom: 8 }} />
          <Skeleton width="60%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="70%" height={14} />
        </View>
      </View>
      <View style={styles.patientStats}>
        <View style={styles.statItem}>
          <Skeleton width={40} height={20} />
          <Skeleton width={60} height={12} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.statItem}>
          <Skeleton width={40} height={20} />
          <Skeleton width={60} height={12} style={{ marginTop: 4 }} />
        </View>
        <View style={styles.statItem}>
          <Skeleton width={40} height={20} />
          <Skeleton width={60} height={12} style={{ marginTop: 4 }} />
        </View>
      </View>
    </Surface>
  );
};

// Stats Card Skeleton
export const StatsCardSkeleton: React.FC<{ style?: any }> = ({ style }) => {
  return (
    <Surface style={[styles.statsCard, style]} elevation={1}>
      <Skeleton variant="circular" width={48} height={48} style={{ marginBottom: 12 }} />
      <Skeleton width="60%" height={24} style={{ marginBottom: 8 }} />
      <Skeleton width="80%" height={14} />
    </Surface>
  );
};

// Dashboard Skeleton
export const DashboardSkeleton: React.FC = () => {
  return (
    <View style={styles.dashboardContainer}>
      {/* Header */}
      <View style={styles.dashboardHeader}>
        <View>
          <Skeleton width={150} height={24} style={{ marginBottom: 8 }} />
          <Skeleton width={100} height={16} />
        </View>
        <Skeleton variant="circular" width={40} height={40} />
      </View>

      {/* Stats Cards */}
      <View style={styles.statsRow}>
        <StatsCardSkeleton style={{ flex: 1, marginRight: 8 }} />
        <StatsCardSkeleton style={{ flex: 1, marginHorizontal: 4 }} />
        <StatsCardSkeleton style={{ flex: 1, marginLeft: 8 }} />
      </View>

      {/* Recent Items */}
      <Surface style={styles.section} elevation={1}>
        <Skeleton width={150} height={20} style={{ marginBottom: 16 }} />
        <ListItemSkeleton />
        <ListItemSkeleton />
        <ListItemSkeleton />
      </Surface>

      {/* Chart */}
      <Surface style={styles.section} elevation={1}>
        <Skeleton width={120} height={20} style={{ marginBottom: 16 }} />
        <Skeleton width="100%" height={200} variant="rounded" />
      </Surface>
    </View>
  );
};

// Table Skeleton
export const TableSkeleton: React.FC<{ rows?: number }> = ({ rows = 5 }) => {
  return (
    <Surface style={styles.tableSkeleton} elevation={1}>
      {/* Header */}
      <View style={styles.tableHeader}>
        <Skeleton width="20%" height={16} />
        <Skeleton width="25%" height={16} />
        <Skeleton width="20%" height={16} />
        <Skeleton width="15%" height={16} />
        <Skeleton width="15%" height={16} />
      </View>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, index) => (
        <View key={index} style={styles.tableRow}>
          <Skeleton width="20%" height={14} />
          <Skeleton width="25%" height={14} />
          <Skeleton width="20%" height={14} />
          <Skeleton width="15%" height={14} />
          <Skeleton width="15%" height={14} variant="rounded" />
        </View>
      ))}
    </Surface>
  );
};

// Form Skeleton
export const FormSkeleton: React.FC = () => {
  return (
    <View style={styles.formSkeleton}>
      <View style={styles.formField}>
        <Skeleton width={80} height={14} style={{ marginBottom: 8 }} />
        <Skeleton width="100%" height={56} variant="rounded" />
      </View>
      <View style={styles.formField}>
        <Skeleton width={100} height={14} style={{ marginBottom: 8 }} />
        <Skeleton width="100%" height={56} variant="rounded" />
      </View>
      <View style={styles.formField}>
        <Skeleton width={90} height={14} style={{ marginBottom: 8 }} />
        <Skeleton width="100%" height={100} variant="rounded" />
      </View>
      <View style={styles.formActions}>
        <Skeleton width={100} height={48} variant="rounded" />
        <Skeleton width={100} height={48} variant="rounded" />
      </View>
    </View>
  );
};

// Calendar Skeleton
export const CalendarSkeleton: React.FC = () => {
  return (
    <Surface style={styles.calendarSkeleton} elevation={1}>
      {/* Month Header */}
      <View style={styles.calendarHeader}>
        <Skeleton width={40} height={40} variant="circular" />
        <Skeleton width={150} height={20} />
        <Skeleton width={40} height={40} variant="circular" />
      </View>
      
      {/* Week Days */}
      <View style={styles.weekDays}>
        {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((_, index) => (
          <Skeleton key={index} width={30} height={14} style={{ marginHorizontal: 8 }} />
        ))}
      </View>
      
      {/* Calendar Grid */}
      <View style={styles.calendarGrid}>
        {Array.from({ length: 35 }).map((_, index) => (
          <Skeleton
            key={index}
            width={40}
            height={40}
            variant="rounded"
            style={{ margin: 4 }}
          />
        ))}
      </View>
    </Surface>
  );
};

const styles = StyleSheet.create({
  // Card Skeleton
  cardSkeleton: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  cardHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  cardHeaderText: {
    flex: 1,
    marginLeft: 12,
  },

  // List Item
  listItemSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 8,
    borderRadius: 8,
  },
  listItemContent: {
    flex: 1,
    marginLeft: 12,
  },

  // Appointment Card
  appointmentCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  appointmentBody: {
    marginBottom: 16,
  },
  appointmentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  appointmentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },

  // Patient Card
  patientCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  patientHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  patientInfo: {
    flex: 1,
    marginLeft: 12,
  },
  patientStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },

  // Stats Card
  statsCard: {
    padding: 16,
    alignItems: 'center',
    borderRadius: 8,
    backgroundColor: '#fff',
  },

  // Dashboard
  dashboardContainer: {
    padding: 16,
  },
  dashboardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  section: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
  },

  // Table
  tableSkeleton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  tableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    marginBottom: 12,
  },
  tableRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },

  // Form
  formSkeleton: {
    padding: 16,
  },
  formField: {
    marginBottom: 20,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },

  // Calendar
  calendarSkeleton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  weekDays: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
});
