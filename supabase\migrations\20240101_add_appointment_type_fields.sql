-- Add appointment_type and duration_minutes columns to appointments table
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS appointment_type VARCHAR(50) DEFAULT 'checkup',
ADD COLUMN IF NOT EXISTS duration_minutes INTEGER DEFAULT 60;

-- Update existing appointments to have default values
UPDATE appointments 
SET appointment_type = 'checkup', duration_minutes = 60
WHERE appointment_type IS NULL OR duration_minutes IS NULL;

-- Create index for appointment_type for better query performance
CREATE INDEX IF NOT EXISTS idx_appointments_appointment_type ON appointments(appointment_type);

-- Add comment on columns
COMMENT ON COLUMN appointments.appointment_type IS 'Type of appointment (checkup, cleaning, filling, emergency, consultation)';
COMMENT ON COLUMN appointments.duration_minutes IS 'Duration of the appointment in minutes';
