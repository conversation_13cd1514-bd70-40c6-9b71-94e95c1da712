import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  TextInput,
  Avatar,
  List,
  Divider,
  Portal,
  Dialog,
  IconButton,
  Chip,
  Surface,
  FAB,
  Snackbar,
  Switch,
  RadioButton,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import * as ImagePicker from 'expo-image-picker';
import { supabase } from '../../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { UserRole } from '../../types';
import { format } from 'date-fns';

interface ProfileData {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: string;
  date_of_birth?: string;
  gender?: string;
  role: UserRole;
  avatar_url?: string;
  // Role-specific fields
  specialization?: string; // For dentist
  experience_years?: number; // For dentist/staff
  qualifications?: string; // For dentist
  department?: string; // For staff
  emergency_contact?: string; // For patient
  blood_group?: string; // For patient
  allergies?: string; // For patient
  medical_conditions?: string; // For patient
  insurance_provider?: string; // For patient
  insurance_number?: string; // For patient
  created_at: string;
  updated_at?: string;
}

export const ProfileScreen = () => {
  const theme = useTheme();
  const { user, signOut } = useAuth();
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [changePasswordVisible, setChangePasswordVisible] = useState(false);
  const [deleteAccountVisible, setDeleteAccountVisible] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  
  const [formData, setFormData] = useState<Partial<ProfileData>>({});
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    appointmentReminders: true,
    marketingEmails: false,
  });

  useEffect(() => {
    fetchProfile();
    fetchPreferences();
  }, []);

  const fetchProfile = async () => {
    setLoading(true);
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) throw new Error('No authenticated user');

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', authUser.id)
        .single();

      if (error) throw error;
      
      setProfileData(data);
      setFormData(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const fetchPreferences = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', authUser.id)
        .single();

      if (data) {
        setPreferences(data.preferences);
      }
    } catch (error) {
      console.error('Error fetching preferences:', error);
    }
  };

  const handleSaveProfile = async () => {
    setSaving(true);
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) throw new Error('No authenticated user');

      const { error } = await supabase
        .from('users')
        .update({
          name: formData.name,
          phone: formData.phone,
          address: formData.address,
          date_of_birth: formData.date_of_birth,
          gender: formData.gender,
          specialization: formData.specialization,
          experience_years: formData.experience_years,
          qualifications: formData.qualifications,
          department: formData.department,
          emergency_contact: formData.emergency_contact,
          blood_group: formData.blood_group,
          allergies: formData.allergies,
          medical_conditions: formData.medical_conditions,
          insurance_provider: formData.insurance_provider,
          insurance_number: formData.insurance_number,
          updated_at: new Date().toISOString(),
        })
        .eq('id', authUser.id);

      if (error) throw error;

      setProfileData(formData as ProfileData);
      setEditing(false);
      showSnackbar('Profile updated successfully');
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleSavePreferences = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: authUser.id,
          preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
      showSnackbar('Preferences saved successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save preferences');
    }
  };

  const handleChangePassword = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return;
    }

    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword,
      });

      if (error) throw error;

      Alert.alert('Success', 'Password changed successfully');
      setChangePasswordVisible(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to change password');
    }
  };

  const handleDeleteAccount = async () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { data: { user: authUser } } = await supabase.auth.getUser();
              if (!authUser) return;

              // Delete user data
              const { error: deleteError } = await supabase
                .from('users')
                .delete()
                .eq('id', authUser.id);

              if (deleteError) throw deleteError;

              // Delete auth account
              const { error: authError } = await supabase.auth.admin.deleteUser(
                authUser.id
              );

              if (authError) throw authError;

              Alert.alert('Success', 'Account deleted successfully');
              signOut();
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete account');
            }
          },
        },
      ]
    );
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.7,
    });

    if (!result.canceled && result.assets[0]) {
      uploadAvatar(result.assets[0].uri);
    }
  };

  const uploadAvatar = async (uri: string) => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) return;

      const fileName = `${authUser.id}-${Date.now()}.jpg`;
      const formData = new FormData();
      formData.append('file', {
        uri,
        name: fileName,
        type: 'image/jpeg',
      } as any);

      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(fileName, formData);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);

      // Update user profile with avatar URL
      await supabase
        .from('users')
        .update({ avatar_url: publicUrl })
        .eq('id', authUser.id);

      setFormData({ ...formData, avatar_url: publicUrl });
      setProfileData({ ...profileData!, avatar_url: publicUrl });
      showSnackbar('Avatar updated successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to upload avatar');
    }
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const getRoleSpecificFields = () => {
    if (!profileData) return null;

    switch (profileData.role) {
      case UserRole.PATIENT:
        return (
          <>
            <TextInput
              label="Emergency Contact"
              value={formData.emergency_contact || ''}
              onChangeText={(text) => setFormData({ ...formData, emergency_contact: text })}
              mode="outlined"
              editable={editing}
              style={styles.input}
              left={<TextInput.Icon icon="phone-alert" />}
            />
            
            <TextInput
              label="Blood Group"
              value={formData.blood_group || ''}
              onChangeText={(text) => setFormData({ ...formData, blood_group: text })}
              mode="outlined"
              editable={editing}
              style={styles.input}
              left={<TextInput.Icon icon="water" />}
            />
            
            <TextInput
              label="Allergies"
              value={formData.allergies || ''}
              onChangeText={(text) => setFormData({ ...formData, allergies: text })}
              mode="outlined"
              editable={editing}
              multiline
              numberOfLines={2}
              style={styles.input}
              left={<TextInput.Icon icon="alert-circle" />}
            />
            
            <TextInput
              label="Medical Conditions"
              value={formData.medical_conditions || ''}
              onChangeText={(text) => setFormData({ ...formData, medical_conditions: text })}
              mode="outlined"
              editable={editing}
              multiline
              numberOfLines={2}
              style={styles.input}
              left={<TextInput.Icon icon="medical-bag" />}
            />
            
            <TextInput
              label="Insurance Provider"
              value={formData.insurance_provider || ''}
              onChangeText={(text) => setFormData({ ...formData, insurance_provider: text })}
              mode="outlined"
              editable={editing}
              style={styles.input}
              left={<TextInput.Icon icon="shield-check" />}
            />
            
            <TextInput
              label="Insurance Number"
              value={formData.insurance_number || ''}
              onChangeText={(text) => setFormData({ ...formData, insurance_number: text })}
              mode="outlined"
              editable={editing}
              style={styles.input}
              left={<TextInput.Icon icon="numeric" />}
            />
          </>
        );
        
      case UserRole.DENTIST:
        return (
          <>
            <TextInput
              label="Specialization"
              value={formData.specialization || ''}
              onChangeText={(text) => setFormData({ ...formData, specialization: text })}
              mode="outlined"
              editable={editing}
              style={styles.input}
              left={<TextInput.Icon icon="certificate" />}
            />
            
            <TextInput
              label="Qualifications"
              value={formData.qualifications || ''}
              onChangeText={(text) => setFormData({ ...formData, qualifications: text })}
              mode="outlined"
              editable={editing}
              multiline
              numberOfLines={2}
              style={styles.input}
              left={<TextInput.Icon icon="school" />}
            />
            
            <TextInput
              label="Years of Experience"
              value={formData.experience_years?.toString() || ''}
              onChangeText={(text) => setFormData({ ...formData, experience_years: parseInt(text) || 0 })}
              mode="outlined"
              editable={editing}
              keyboardType="numeric"
              style={styles.input}
              left={<TextInput.Icon icon="briefcase-clock" />}
            />
          </>
        );
        
      case UserRole.STAFF:
        return (
          <>
            <TextInput
              label="Department"
              value={formData.department || ''}
              onChangeText={(text) => setFormData({ ...formData, department: text })}
              mode="outlined"
              editable={editing}
              style={styles.input}
              left={<TextInput.Icon icon="office-building" />}
            />
            
            <TextInput
              label="Years of Experience"
              value={formData.experience_years?.toString() || ''}
              onChangeText={(text) => setFormData({ ...formData, experience_years: parseInt(text) || 0 })}
              mode="outlined"
              editable={editing}
              keyboardType="numeric"
              style={styles.input}
              left={<TextInput.Icon icon="briefcase-clock" />}
            />
          </>
        );
        
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Profile Header */}
          <Surface style={styles.header} elevation={2}>
            <View style={styles.avatarContainer}>
              {formData.avatar_url ? (
                <Avatar.Image
                  size={100}
                  source={{ uri: formData.avatar_url }}
                />
              ) : (
                <Avatar.Text
                  size={100}
                  label={formData.name?.substring(0, 2).toUpperCase() || 'NA'}
                />
              )}
              {editing && (
                <IconButton
                  icon="camera"
                  size={24}
                  style={styles.cameraButton}
                  onPress={pickImage}
                />
              )}
            </View>
            
            <Title style={styles.name}>{profileData?.name}</Title>
            <Text style={styles.role}>{profileData?.role}</Text>
            <Text style={styles.email}>{profileData?.email}</Text>
            
            <View style={styles.headerActions}>
              {!editing ? (
                <Button
                  mode="contained"
                  onPress={() => setEditing(true)}
                  icon="pencil"
                >
                  Edit Profile
                </Button>
              ) : (
                <View style={styles.editActions}>
                  <Button
                    mode="outlined"
                    onPress={() => {
                      setFormData(profileData!);
                      setEditing(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    mode="contained"
                    onPress={handleSaveProfile}
                    loading={saving}
                  >
                    Save
                  </Button>
                </View>
              )}
            </View>
          </Surface>

          {/* Basic Information */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.sectionTitle}>Basic Information</Title>
              
              <TextInput
                label="Full Name"
                value={formData.name || ''}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                mode="outlined"
                editable={editing}
                style={styles.input}
                left={<TextInput.Icon icon="account" />}
              />
              
              <TextInput
                label="Phone"
                value={formData.phone || ''}
                onChangeText={(text) => setFormData({ ...formData, phone: text })}
                mode="outlined"
                editable={editing}
                keyboardType="phone-pad"
                style={styles.input}
                left={<TextInput.Icon icon="phone" />}
              />
              
              <TextInput
                label="Address"
                value={formData.address || ''}
                onChangeText={(text) => setFormData({ ...formData, address: text })}
                mode="outlined"
                editable={editing}
                multiline
                numberOfLines={2}
                style={styles.input}
                left={<TextInput.Icon icon="map-marker" />}
              />
              
              <TextInput
                label="Date of Birth"
                value={formData.date_of_birth || ''}
                onChangeText={(text) => setFormData({ ...formData, date_of_birth: text })}
                mode="outlined"
                editable={editing}
                style={styles.input}
                left={<TextInput.Icon icon="calendar" />}
                placeholder="YYYY-MM-DD"
              />
              
              <View style={styles.genderContainer}>
                <Text style={styles.fieldLabel}>Gender</Text>
                <RadioButton.Group
                  onValueChange={(value) => setFormData({ ...formData, gender: value })}
                  value={formData.gender || ''}
                >
                  <View style={styles.radioRow}>
                    <RadioButton.Item label="Male" value="male" disabled={!editing} />
                    <RadioButton.Item label="Female" value="female" disabled={!editing} />
                    <RadioButton.Item label="Other" value="other" disabled={!editing} />
                  </View>
                </RadioButton.Group>
              </View>
            </Card.Content>
          </Card>

          {/* Role-Specific Information */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.sectionTitle}>
                {profileData?.role === UserRole.PATIENT && 'Medical Information'}
                {profileData?.role === UserRole.DENTIST && 'Professional Information'}
                {profileData?.role === UserRole.STAFF && 'Work Information'}
                {profileData?.role === UserRole.ADMIN && 'Administrative Information'}
              </Title>
              
              {getRoleSpecificFields()}
            </Card.Content>
          </Card>

          {/* Notification Preferences */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.sectionTitle}>Notification Preferences</Title>
              
              <List.Item
                title="Email Notifications"
                description="Receive updates via email"
                left={props => <List.Icon {...props} icon="email" />}
                right={() => (
                  <Switch
                    value={preferences.emailNotifications}
                    onValueChange={(value) => 
                      setPreferences({ ...preferences, emailNotifications: value })
                    }
                  />
                )}
              />
              
              <List.Item
                title="SMS Notifications"
                description="Receive updates via SMS"
                left={props => <List.Icon {...props} icon="message-text" />}
                right={() => (
                  <Switch
                    value={preferences.smsNotifications}
                    onValueChange={(value) => 
                      setPreferences({ ...preferences, smsNotifications: value })
                    }
                  />
                )}
              />
              
              <List.Item
                title="Push Notifications"
                description="Receive in-app notifications"
                left={props => <List.Icon {...props} icon="bell" />}
                right={() => (
                  <Switch
                    value={preferences.pushNotifications}
                    onValueChange={(value) => 
                      setPreferences({ ...preferences, pushNotifications: value })
                    }
                  />
                )}
              />
              
              <List.Item
                title="Appointment Reminders"
                description="Get reminded about appointments"
                left={props => <List.Icon {...props} icon="calendar-alert" />}
                right={() => (
                  <Switch
                    value={preferences.appointmentReminders}
                    onValueChange={(value) => 
                      setPreferences({ ...preferences, appointmentReminders: value })
                    }
                  />
                )}
              />
              
              <Button
                mode="contained"
                onPress={handleSavePreferences}
                style={styles.preferenceButton}
              >
                Save Preferences
              </Button>
            </Card.Content>
          </Card>

          {/* Security Settings */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.sectionTitle}>Security</Title>
              
              <List.Item
                title="Change Password"
                description="Update your account password"
                left={props => <List.Icon {...props} icon="lock-reset" />}
                right={props => <List.Icon {...props} icon="chevron-right" />}
                onPress={() => setChangePasswordVisible(true)}
              />
              
              <Divider />
              
              <List.Item
                title="Sign Out"
                description="Sign out from all devices"
                left={props => <List.Icon {...props} icon="logout" />}
                right={props => <List.Icon {...props} icon="chevron-right" />}
                onPress={signOut}
              />
              
              <Divider />
              
              <List.Item
                title="Delete Account"
                description="Permanently delete your account"
                titleStyle={{ color: theme.colors.error }}
                descriptionStyle={{ color: theme.colors.error }}
                left={props => <List.Icon {...props} icon="delete" color={theme.colors.error} />}
                right={props => <List.Icon {...props} icon="chevron-right" color={theme.colors.error} />}
                onPress={() => setDeleteAccountVisible(true)}
              />
            </Card.Content>
          </Card>

          {/* Account Information */}
          <Card style={styles.card}>
            <Card.Content>
              <Title style={styles.sectionTitle}>Account Information</Title>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>User ID:</Text>
                <Text style={styles.infoValue}>{profileData?.id}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Account Created:</Text>
                <Text style={styles.infoValue}>
                  {profileData?.created_at && format(new Date(profileData.created_at), 'MMM d, yyyy')}
                </Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Last Updated:</Text>
                <Text style={styles.infoValue}>
                  {profileData?.updated_at 
                    ? format(new Date(profileData.updated_at), 'MMM d, yyyy h:mm a')
                    : 'Never'}
                </Text>
              </View>
            </Card.Content>
          </Card>

          <View style={{ height: 100 }} />
        </ScrollView>

        {/* Change Password Dialog */}
        <Portal>
          <Dialog visible={changePasswordVisible} onDismiss={() => setChangePasswordVisible(false)}>
            <Dialog.Title>Change Password</Dialog.Title>
            <Dialog.Content>
              <TextInput
                label="Current Password"
                value={passwordData.currentPassword}
                onChangeText={(text) => setPasswordData({ ...passwordData, currentPassword: text })}
                mode="outlined"
                secureTextEntry
                style={styles.dialogInput}
              />
              
              <TextInput
                label="New Password"
                value={passwordData.newPassword}
                onChangeText={(text) => setPasswordData({ ...passwordData, newPassword: text })}
                mode="outlined"
                secureTextEntry
                style={styles.dialogInput}
              />
              
              <TextInput
                label="Confirm New Password"
                value={passwordData.confirmPassword}
                onChangeText={(text) => setPasswordData({ ...passwordData, confirmPassword: text })}
                mode="outlined"
                secureTextEntry
                style={styles.dialogInput}
              />
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setChangePasswordVisible(false)}>Cancel</Button>
              <Button onPress={handleChangePassword}>Change</Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
        >
          {snackbarMessage}
        </Snackbar>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#fff',
    elevation: 2,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  role: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  email: {
    fontSize: 14,
    color: '#999',
    marginBottom: 16,
  },
  headerActions: {
    marginTop: 8,
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
  },
  card: {
    marginHorizontal: 16,
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#666',
  },
  genderContainer: {
    marginTop: 8,
  },
  radioRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  preferenceButton: {
    marginTop: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  dialogInput: {
    marginBottom: 12,
  },
});
