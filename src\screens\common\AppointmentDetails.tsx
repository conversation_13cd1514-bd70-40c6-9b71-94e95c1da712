import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Linking } from 'react-native';
import { 
  Text, 
  Card, 
  Title, 
  Button, 
  Chip, 
  Divider, 
  List, 
  Avatar,
  useTheme,
  IconButton,
  Dialog,
  Portal,
  TextInput,
  Paragraph
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRoute, useNavigation } from '@react-navigation/native';
import { format, parseISO } from 'date-fns';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAppointments } from '../../contexts/AppointmentContext';
import { useAuth } from '../../contexts/AuthContext';
import { Appointment, AppointmentStatus } from '../../types';
import { APPOINTMENT_TYPES } from '../../constants/appointmentConfig';

export const AppointmentDetails = () => {
  const theme = useTheme();
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const { user } = useAuth();
  const { updateAppointment, cancelAppointment } = useAppointments();
  
  const appointment: Appointment = route.params?.appointment;
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showRescheduleDialog, setShowRescheduleDialog] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  
  if (!appointment) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <Text>No appointment data available</Text>
        </View>
      </SafeAreaView>
    );
  }

  const appointmentDate = new Date(appointment.date);
  const isPast = appointmentDate < new Date();
  const canModify = appointment.status === AppointmentStatus.SCHEDULED && !isPast;

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return '#2196F3';
      case AppointmentStatus.CONFIRMED:
        return '#4CAF50';
      case AppointmentStatus.COMPLETED:
        return '#8BC34A';
      case AppointmentStatus.CANCELLED:
        return '#F44336';
      case AppointmentStatus.NO_SHOW:
        return '#FF9800';
      default:
        return '#757575';
    }
  };

  const getAppointmentTypeConfig = (type?: string) => {
    if (!type) return { label: 'General', color: '#757575', icon: 'tooth', duration: 60 };
    const config = APPOINTMENT_TYPES.find(t => t.value === type);
    return config || { label: type, color: '#757575', icon: 'tooth', duration: 60 };
  };

  const handleCancelAppointment = async () => {
    if (!cancellationReason.trim()) {
      Alert.alert('Error', 'Please provide a reason for cancellation');
      return;
    }
    
    try {
      await cancelAppointment(appointment.id);
      Alert.alert('Success', 'Appointment cancelled successfully');
      setShowCancelDialog(false);
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to cancel appointment');
    }
  };

  const handleReschedule = () => {
    setShowRescheduleDialog(false);
    navigation.navigate('BookAppointment', { reschedule: appointment });
  };

  const handleCall = (phone?: string) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  const handleEmail = (email?: string) => {
    if (email) {
      Linking.openURL(`mailto:${email}`);
    }
  };

  const typeConfig = getAppointmentTypeConfig(appointment.appointment_type);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header Card */}
        <Card style={styles.headerCard}>
          <Card.Content>
            <View style={styles.headerContent}>
              <View style={styles.dateTimeBox}>
                <Text style={styles.dayText}>
                  {format(appointmentDate, 'dd')}
                </Text>
                <Text style={styles.monthText}>
                  {format(appointmentDate, 'MMM')}
                </Text>
                <Text style={styles.yearText}>
                  {format(appointmentDate, 'yyyy')}
                </Text>
              </View>
              <View style={styles.headerInfo}>
                <Title style={styles.title}>Appointment Details</Title>
                <View style={styles.chipContainer}>
                  <Chip
                    icon={typeConfig.icon}
                    style={[styles.typeChip, { backgroundColor: typeConfig.color + '20' }]}
                    textStyle={{ color: typeConfig.color }}
                  >
                    {typeConfig.label}
                  </Chip>
                  <Chip
                    style={[styles.statusChip, { backgroundColor: getStatusColor(appointment.status) }]}
                    textStyle={{ color: '#fff' }}
                  >
                    {appointment.status}
                  </Chip>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Details Card */}
        <Card style={styles.card}>
          <Card.Content>
            <List.Item
              title="Date & Time"
              description={`${format(appointmentDate, 'EEEE, MMMM d, yyyy')}\n${appointment.start_time} - ${appointment.end_time}`}
              left={props => <List.Icon {...props} icon="calendar-clock" />}
            />
            <Divider />
            
            <List.Item
              title="Appointment Type"
              description={`${typeConfig.label} (${appointment.duration_minutes || typeConfig.duration} minutes)`}
              left={props => <List.Icon {...props} icon={typeConfig.icon} />}
            />
            <Divider />
            
            <List.Item
              title="Dentist"
              description={`Dr. ${appointment.dentist?.name}`}
              left={props => <List.Icon {...props} icon="doctor" />}
            />
            
            {appointment.notes && (
              <>
                <Divider />
                <List.Item
                  title="Notes"
                  description={appointment.notes}
                  left={props => <List.Icon {...props} icon="note-text" />}
                />
              </>
            )}
          </Card.Content>
        </Card>

        {/* Contact Card */}
        {(appointment.dentist?.phone || appointment.dentist?.email) && (
          <Card style={styles.card}>
            <Card.Title title="Contact Information" />
            <Card.Content>
              {appointment.dentist?.phone && (
                <List.Item
                  title="Phone"
                  description={appointment.dentist.phone}
                  left={props => <List.Icon {...props} icon="phone" />}
                  right={() => (
                    <IconButton
                      icon="phone"
                      onPress={() => handleCall(appointment.dentist?.phone)}
                      iconColor={theme.colors.primary}
                    />
                  )}
                />
              )}
              {appointment.dentist?.email && (
                <List.Item
                  title="Email"
                  description={appointment.dentist.email}
                  left={props => <List.Icon {...props} icon="email" />}
                  right={() => (
                    <IconButton
                      icon="email"
                      onPress={() => handleEmail(appointment.dentist?.email)}
                      iconColor={theme.colors.primary}
                    />
                  )}
                />
              )}
            </Card.Content>
          </Card>
        )}

        {/* Actions */}
        {canModify && (
          <View style={styles.actions}>
            <Button
              mode="contained"
              onPress={() => setShowRescheduleDialog(true)}
              icon="calendar-edit"
              style={styles.actionButton}
            >
              Reschedule
            </Button>
            <Button
              mode="outlined"
              onPress={() => setShowCancelDialog(true)}
              icon="cancel"
              style={styles.actionButton}
              textColor={theme.colors.error}
            >
              Cancel Appointment
            </Button>
          </View>
        )}

        {/* Additional Information */}
        <Card style={[styles.card, styles.infoCard]}>
          <Card.Content>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="information" size={20} color={theme.colors.primary} />
              <Text style={styles.infoText}>Important Information</Text>
            </View>
            <Paragraph style={styles.infoParagraph}>
              • Please arrive 10 minutes before your appointment time
            </Paragraph>
            <Paragraph style={styles.infoParagraph}>
              • Bring any previous medical records or X-rays
            </Paragraph>
            <Paragraph style={styles.infoParagraph}>
              • If you need to cancel, please do so at least 24 hours in advance
            </Paragraph>
            {typeConfig.label === 'Surgery' && (
              <Paragraph style={styles.infoParagraph}>
                • Follow pre-surgery instructions provided by your dentist
              </Paragraph>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Cancel Dialog */}
      <Portal>
        <Dialog visible={showCancelDialog} onDismiss={() => setShowCancelDialog(false)}>
          <Dialog.Title>Cancel Appointment</Dialog.Title>
          <Dialog.Content>
            <Paragraph>Are you sure you want to cancel this appointment?</Paragraph>
            <TextInput
              label="Reason for cancellation"
              value={cancellationReason}
              onChangeText={setCancellationReason}
              multiline
              numberOfLines={3}
              style={styles.reasonInput}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowCancelDialog(false)}>No</Button>
            <Button onPress={handleCancelAppointment} textColor={theme.colors.error}>
              Yes, Cancel
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Reschedule Dialog */}
      <Portal>
        <Dialog visible={showRescheduleDialog} onDismiss={() => setShowRescheduleDialog(false)}>
          <Dialog.Title>Reschedule Appointment</Dialog.Title>
          <Dialog.Content>
            <Paragraph>
              You will be redirected to the booking screen to select a new date and time for your {typeConfig.label} appointment.
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowRescheduleDialog(false)}>Cancel</Button>
            <Button onPress={handleReschedule}>Continue</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateTimeBox: {
    width: 80,
    height: 80,
    backgroundColor: '#2874ba',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  dayText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  monthText: {
    color: '#fff',
    fontSize: 14,
  },
  yearText: {
    color: '#fff',
    fontSize: 12,
    opacity: 0.8,
  },
  headerInfo: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  typeChip: {
    height: 28,
  },
  statusChip: {
    height: 28,
  },
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    elevation: 2,
  },
  actions: {
    padding: 16,
    gap: 12,
  },
  actionButton: {
    paddingVertical: 6,
  },
  infoCard: {
    backgroundColor: '#E3F2FD',
    marginBottom: 24,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoParagraph: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 28,
    marginBottom: 4,
  },
  reasonInput: {
    marginTop: 16,
    backgroundColor: '#fff',
  },
});
