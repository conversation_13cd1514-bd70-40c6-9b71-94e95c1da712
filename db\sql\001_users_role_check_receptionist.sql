-- 001_users_role_check_receptionist.sql
-- Purpose: Recreate the users.role CHECK constraint to allow 'receptionist' and 'dentist'.
-- Requires: db/sql/000_enum_user_role_add_values.sql to have run first (adds enum values).

BEGIN;
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE public.users
  ADD CONSTRAINT users_role_check
  CHECK (
    role = ANY (ARRAY[
      'admin'::public.user_role,
      'staff'::public.user_role,
      'doctor'::public.user_role,
      'dentist'::public.user_role,
      'receptionist'::public.user_role,
      'patient'::public.user_role
    ])
  );
COMMIT;
