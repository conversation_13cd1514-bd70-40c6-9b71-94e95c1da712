import React from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { Box, Typography, Paper } from '@mui/material'

const DebugInfo: React.FC = () => {
  const { user, loading, session } = useAuth()

  return (
    <Paper sx={{ p: 2, mb: 2, backgroundColor: '#f0f0f0' }}>
      <Typography variant="h6" gutterBottom>
        Debug Information
      </Typography>
      <Typography variant="body2">
        <strong>Loading:</strong> {loading ? 'true' : 'false'}
      </Typography>
      <Typography variant="body2">
        <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}
      </Typography>
      <Typography variant="body2">
        <strong>Session:</strong> {session ? 'exists' : 'null'}
      </Typography>
      <Typography variant="body2">
        <strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'not set'}
      </Typography>
    </Paper>
  )
}

export default DebugInfo