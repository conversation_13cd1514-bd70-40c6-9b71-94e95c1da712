import { MD3LightTheme, MD3DarkTheme, configureFonts } from 'react-native-paper';
import { Platform } from 'react-native';

// Color Palette
const colors = {
  // Primary Brand Colors
  primary: {
    main: '#2874ba',
    light: '#5a9fd4',
    dark: '#1a4a7a',
    contrast: '#ffffff',
  },
  secondary: {
    main: '#3dbc98',
    light: '#6dcdb3',
    dark: '#2a8569',
    contrast: '#ffffff',
  },
  accent: {
    main: '#f59e0b',
    light: '#fbbf24',
    dark: '#d97706',
    contrast: '#ffffff',
  },
  
  // Semantic Colors
  success: {
    main: '#4CAF50',
    light: '#81C784',
    dark: '#388E3C',
    background: '#E8F5E9',
  },
  warning: {
    main: '#FF9800',
    light: '#FFB74D',
    dark: '#F57C00',
    background: '#FFF3E0',
  },
  error: {
    main: '#F44336',
    light: '#EF5350',
    dark: '#C62828',
    background: '#FFEBEE',
  },
  info: {
    main: '#2196F3',
    light: '#64B5F6',
    dark: '#1976D2',
    background: '#E3F2FD',
  },
  
  // Neutral Colors
  neutral: {
    white: '#FFFFFF',
    background: '#f8f9fa',
    surface: '#FFFFFF',
    surfaceVariant: '#F5F5F5',
    border: '#E0E0E0',
    divider: '#E8E8E8',
    disabled: '#BDBDBD',
    placeholder: '#9E9E9E',
    text: {
      primary: '#212121',
      secondary: '#757575',
      disabled: '#BDBDBD',
      hint: '#9E9E9E',
    },
  },
  
  // Role-based Colors
  roles: {
    patient: '#2196F3',
    dentist: '#4CAF50',
    staff: '#FF9800',
    admin: '#9C27B0',
  },
  
  // Appointment Status Colors
  appointment: {
    scheduled: '#2196F3',
    confirmed: '#4CAF50',
    completed: '#9E9E9E',
    cancelled: '#F44336',
    noShow: '#FF9800',
  },
};

// Typography
const typography = {
  fontFamily: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),
  
  // Font Sizes
  sizes: {
    xs: 10,
    sm: 12,
    base: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 28,
    '4xl': 32,
    '5xl': 36,
  },
  
  // Font Weights
  weights: {
    thin: '100' as const,
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    black: '900' as const,
  },
  
  // Line Heights
  lineHeights: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },
};

// Spacing & Layout
const spacing = {
  none: 0,
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 56,
  '6xl': 64,
};

// Border Radius
const borderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  full: 9999,
};

// Shadows (Platform-specific)
const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 6,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.18,
    shadowRadius: 16,
    elevation: 12,
  },
};

// Animation Durations
const animations = {
  duration: {
    instant: 0,
    fast: 150,
    base: 300,
    slow: 500,
    slower: 700,
    slowest: 1000,
  },
  
  easing: {
    linear: 'linear',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// Component Specific Styles
const components = {
  card: {
    borderRadius: borderRadius.md,
    padding: spacing.base,
    ...shadows.sm,
  },
  
  button: {
    height: 48,
    borderRadius: borderRadius.base,
    paddingHorizontal: spacing.base,
  },
  
  input: {
    height: 56,
    borderRadius: borderRadius.base,
    fontSize: typography.sizes.base,
  },
  
  avatar: {
    sizes: {
      sm: 32,
      md: 40,
      lg: 56,
      xl: 72,
      '2xl': 96,
    },
  },
  
  chip: {
    height: 32,
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing.md,
  },
  
  badge: {
    height: 20,
    minWidth: 20,
    borderRadius: borderRadius.full,
    paddingHorizontal: spacing.xs,
  },
};

// Configure fonts for React Native Paper
const fontConfig = {
  displayLarge: {
    fontFamily: typography.fontFamily,
    fontSize: 57,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
    lineHeight: 64,
  },
  displayMedium: {
    fontFamily: typography.fontFamily,
    fontSize: 45,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
    lineHeight: 52,
  },
  displaySmall: {
    fontFamily: typography.fontFamily,
    fontSize: 36,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
    lineHeight: 44,
  },
  headlineLarge: {
    fontFamily: typography.fontFamily,
    fontSize: 32,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
    lineHeight: 40,
  },
  headlineMedium: {
    fontFamily: typography.fontFamily,
    fontSize: 28,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
    lineHeight: 36,
  },
  headlineSmall: {
    fontFamily: typography.fontFamily,
    fontSize: 24,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
    lineHeight: 32,
  },
  titleLarge: {
    fontFamily: typography.fontFamily,
    fontSize: 22,
    fontWeight: typography.weights.medium,
    letterSpacing: 0,
    lineHeight: 28,
  },
  titleMedium: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: typography.weights.medium,
    letterSpacing: 0.15,
    lineHeight: 24,
  },
  titleSmall: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: typography.weights.medium,
    letterSpacing: 0.1,
    lineHeight: 20,
  },
  bodyLarge: {
    fontFamily: typography.fontFamily,
    fontSize: 16,
    fontWeight: typography.weights.regular,
    letterSpacing: 0.15,
    lineHeight: 24,
  },
  bodyMedium: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: typography.weights.regular,
    letterSpacing: 0.25,
    lineHeight: 20,
  },
  bodySmall: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: typography.weights.regular,
    letterSpacing: 0.4,
    lineHeight: 16,
  },
  labelLarge: {
    fontFamily: typography.fontFamily,
    fontSize: 14,
    fontWeight: typography.weights.medium,
    letterSpacing: 0.1,
    lineHeight: 20,
  },
  labelMedium: {
    fontFamily: typography.fontFamily,
    fontSize: 12,
    fontWeight: typography.weights.medium,
    letterSpacing: 0.5,
    lineHeight: 16,
  },
  labelSmall: {
    fontFamily: typography.fontFamily,
    fontSize: 11,
    fontWeight: typography.weights.medium,
    letterSpacing: 0.5,
    lineHeight: 16,
  },
  default: {
    fontFamily: typography.fontFamily,
    fontWeight: typography.weights.regular,
    letterSpacing: 0,
  },
};

// Light Theme
export const lightTheme = {
  ...MD3LightTheme,
  fonts: configureFonts({ config: fontConfig }),
  colors: {
    ...MD3LightTheme.colors,
    primary: colors.primary.main,
    primaryContainer: colors.primary.light,
    secondary: colors.secondary.main,
    secondaryContainer: colors.secondary.light,
    tertiary: colors.accent.main,
    tertiaryContainer: colors.accent.light,
    surface: colors.neutral.surface,
    surfaceVariant: colors.neutral.surfaceVariant,
    background: colors.neutral.background,
    error: colors.error.main,
    errorContainer: colors.error.background,
    onPrimary: colors.primary.contrast,
    onPrimaryContainer: colors.primary.dark,
    onSecondary: colors.secondary.contrast,
    onSecondaryContainer: colors.secondary.dark,
    onTertiary: colors.accent.contrast,
    onTertiaryContainer: colors.accent.dark,
    onSurface: colors.neutral.text.primary,
    onSurfaceVariant: colors.neutral.text.secondary,
    onError: '#FFFFFF',
    onErrorContainer: colors.error.dark,
    onBackground: colors.neutral.text.primary,
    outline: colors.neutral.border,
    outlineVariant: colors.neutral.divider,
    inverseSurface: colors.neutral.text.primary,
    inverseOnSurface: colors.neutral.white,
    inversePrimary: colors.primary.light,
    shadow: '#000000',
    scrim: '#000000',
    backdrop: 'rgba(0, 0, 0, 0.4)',
    elevation: {
      level0: 'transparent',
      level1: colors.neutral.surface,
      level2: colors.neutral.surface,
      level3: colors.neutral.surface,
      level4: colors.neutral.surface,
      level5: colors.neutral.surface,
    },
  },
  
  // Custom theme properties
  custom: {
    colors,
    typography,
    spacing,
    borderRadius,
    shadows,
    animations,
    components,
  },
};

// Dark Theme (optional, for future use)
export const darkTheme = {
  ...MD3DarkTheme,
  fonts: configureFonts({ config: fontConfig }),
  colors: {
    ...MD3DarkTheme.colors,
    primary: colors.primary.light,
    primaryContainer: colors.primary.dark,
    secondary: colors.secondary.light,
    secondaryContainer: colors.secondary.dark,
    tertiary: colors.accent.light,
    tertiaryContainer: colors.accent.dark,
    surface: '#1E1E1E',
    surfaceVariant: '#2C2C2C',
    background: '#121212',
    error: colors.error.light,
    errorContainer: colors.error.dark,
    onPrimary: colors.primary.dark,
    onPrimaryContainer: colors.primary.light,
    onSecondary: colors.secondary.dark,
    onSecondaryContainer: colors.secondary.light,
    onTertiary: colors.accent.dark,
    onTertiaryContainer: colors.accent.light,
    onSurface: '#E0E0E0',
    onSurfaceVariant: '#BDBDBD',
    onError: '#000000',
    onErrorContainer: colors.error.light,
    onBackground: '#E0E0E0',
    outline: '#424242',
    outlineVariant: '#303030',
    inverseSurface: '#E0E0E0',
    inverseOnSurface: '#303030',
    inversePrimary: colors.primary.dark,
    shadow: '#000000',
    scrim: '#000000',
    backdrop: 'rgba(0, 0, 0, 0.6)',
    elevation: {
      level0: 'transparent',
      level1: '#1E1E1E',
      level2: '#232323',
      level3: '#252525',
      level4: '#272727',
      level5: '#2C2C2C',
    },
  },
  
  // Custom theme properties
  custom: {
    colors,
    typography,
    spacing,
    borderRadius,
    shadows,
    animations,
    components,
  },
};

// Export default theme (light)
export default lightTheme;

// Helper functions for using theme
export const getColor = (colorPath: string, theme: typeof lightTheme) => {
  const keys = colorPath.split('.');
  let value: any = theme.custom.colors;
  
  for (const key of keys) {
    value = value?.[key];
  }
  
  return value || theme.colors.primary;
};

export const getSpacing = (size: keyof typeof spacing) => spacing[size];

export const getBorderRadius = (size: keyof typeof borderRadius) => borderRadius[size];

export const getShadow = (size: keyof typeof shadows) => shadows[size];

export const getFontSize = (size: keyof typeof typography.sizes) => typography.sizes[size];

export const getFontWeight = (weight: keyof typeof typography.weights) => typography.weights[weight];
