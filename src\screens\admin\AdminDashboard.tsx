import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  DataTable,
  ProgressBar,
  Avatar,
  List,
  Divider,
  IconButton,
  Badge,
  Surface,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Line<PERSON><PERSON>, PieChart } from 'react-native-chart-kit';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { supabase } from '../../../lib/supabase';
import { format, startOfWeek, endOfWeek, subDays } from 'date-fns';

const screenWidth = Dimensions.get('window').width;

interface DashboardStats {
  totalUsers: number;
  totalAppointments: number;
  todayAppointments: number;
  weekAppointments: number;
  pendingAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  usersByRole: {
    patients: number;
    dentists: number;
    staff: number;
  };
}

export const AdminDashboard = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalAppointments: 0,
    todayAppointments: 0,
    weekAppointments: 0,
    pendingAppointments: 0,
    completedAppointments: 0,
    cancelledAppointments: 0,
    usersByRole: {
      patients: 0,
      dentists: 0,
      staff: 0,
    },
  });
  const [recentAppointments, setRecentAppointments] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);
  const [weeklyData, setWeeklyData] = useState<number[]>([0, 0, 0, 0, 0, 0, 0]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchStats(),
        fetchRecentAppointments(),
        fetchRecentActivities(),
        fetchWeeklyAppointments(),
      ]);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Fetch users count
      const { count: totalUsers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      // Fetch users by role
      const { data: userRoles } = await supabase
        .from('users')
        .select('role');

      const usersByRole = {
        patients: userRoles?.filter(u => u.role === 'patient').length || 0,
        dentists: userRoles?.filter(u => u.role === 'dentist').length || 0,
        staff: userRoles?.filter(u => u.role === 'staff').length || 0,
      };

      // Fetch appointments stats
      const { count: totalAppointments } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true });

      const today = new Date().toISOString().split('T')[0];
      const { count: todayAppointments } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .eq('date', today);

      const weekStart = startOfWeek(new Date()).toISOString().split('T')[0];
      const weekEnd = endOfWeek(new Date()).toISOString().split('T')[0];
      const { count: weekAppointments } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gte('date', weekStart)
        .lte('date', weekEnd);

      // Fetch appointments by status
      const { data: appointmentStatuses } = await supabase
        .from('appointments')
        .select('status');

      const pendingAppointments = appointmentStatuses?.filter(a => a.status === 'scheduled').length || 0;
      const completedAppointments = appointmentStatuses?.filter(a => a.status === 'completed').length || 0;
      const cancelledAppointments = appointmentStatuses?.filter(a => a.status === 'cancelled').length || 0;

      setStats({
        totalUsers: totalUsers || 0,
        totalAppointments: totalAppointments || 0,
        todayAppointments: todayAppointments || 0,
        weekAppointments: weekAppointments || 0,
        pendingAppointments,
        completedAppointments,
        cancelledAppointments,
        usersByRole,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchRecentAppointments = async () => {
    try {
      const today = new Date().toISOString().slice(0, 10);
      const { data } = await supabase
        .from('appointments')
        .select(`
          *,
          patient:patient_id(name, email),
          dentist:dentist_id(name)
        `)
        // Only show today and upcoming on dashboard
        .or(`date.gte.${today},appointment_date.gte.${today}`)
        .order('date', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(5);

      setRecentAppointments(data || []);
    } catch (error) {
      console.error('Error fetching recent appointments:', error);
    }
  };

  const fetchRecentActivities = async () => {
    try {
      const { data } = await supabase
        .from('admin_activity_logs')
        .select(`
          *,
          admin:admin_id(name)
        `)
        .order('timestamp', { ascending: false })
        .limit(5);

      setRecentActivities(data || []);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
    }
  };

  const fetchWeeklyAppointments = async () => {
    try {
      const data = [];
      for (let i = 6; i >= 0; i--) {
        const date = subDays(new Date(), i).toISOString().split('T')[0];
        const { count } = await supabase
          .from('appointments')
          .select('*', { count: 'exact', head: true })
          .eq('date', date);
        data.push(count || 0);
      }
      setWeeklyData(data);
    } catch (error) {
      console.error('Error fetching weekly data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  const renderStatCard = (icon: string, label: string, value: number | string, color: string) => (
    <Card style={styles.statCard}>
      <Card.Content style={styles.statCardContent}>
        <View style={[styles.statIconContainer, { backgroundColor: color + '20' }]}>
          <MaterialCommunityIcons name={icon} size={24} color={color} />
        </View>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
      </Card.Content>
    </Card>
  );

  const renderQuickAction = (icon: string, label: string, onPress: () => void, color: string = theme.colors.primary) => (
    <TouchableOpacity style={styles.quickAction} onPress={onPress}>
      <View style={[styles.quickActionIcon, { backgroundColor: color + '20' }]}>
        <MaterialCommunityIcons name={icon} size={28} color={color} />
      </View>
      <Text style={styles.quickActionLabel}>{label}</Text>
    </TouchableOpacity>
  );

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(40, 116, 186, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#2874ba',
    },
  };

  const pieData = [
    {
      name: 'Completed',
      population: stats.completedAppointments,
      color: '#4CAF50',
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: 'Pending',
      population: stats.pendingAppointments,
      color: '#2196F3',
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
    {
      name: 'Cancelled',
      population: stats.cancelledAppointments,
      color: '#F44336',
      legendFontColor: '#7F7F7F',
      legendFontSize: 12,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Title style={styles.title}>Admin Dashboard</Title>
            <Text style={styles.subtitle}>Welcome back, {user?.name}</Text>
          </View>
          <IconButton
            icon="cog"
            size={24}
            onPress={() => navigation.navigate('SystemSettings')}
          />
        </View>

        {/* Stats Overview */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.statsContainer}>
          {renderStatCard('account-group', 'Total Users', stats.totalUsers, '#2196F3')}
          {renderStatCard('calendar-check', 'Total Appointments', stats.totalAppointments, '#4CAF50')}
          {renderStatCard('calendar-today', "Today's Appointments", stats.todayAppointments, '#FF9800')}
          {renderStatCard('calendar-week', 'This Week', stats.weekAppointments, '#9C27B0')}
        </ScrollView>

        {/* Quick Actions */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Quick Actions</Title>
            <View style={styles.quickActionsGrid}>
              {renderQuickAction('account-plus', 'Add User', () => navigation.navigate('UserManagement'))}
              {renderQuickAction('calendar-plus', 'Book Appointment', () => navigation.navigate('Appointments'))}
              {renderQuickAction('file-document', 'View Reports', () => navigation.navigate('ActivityLogs'), '#FF9800')}
              {renderQuickAction('bell', 'Send Notification', () => navigation.navigate('Notifications'), '#9C27B0')}
            </View>
          </Card.Content>
        </Card>

        {/* Weekly Appointments Chart */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Weekly Appointments</Title>
            <LineChart
              data={{
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                  data: weeklyData,
                }],
              }}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              bezier
              style={styles.chart}
            />
          </Card.Content>
        </Card>

        {/* Appointment Status Distribution */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Appointment Status</Title>
            <PieChart
              data={pieData}
              width={screenWidth - 64}
              height={200}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          </Card.Content>
        </Card>

        {/* User Distribution */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>User Distribution</Title>
            <View style={styles.userStats}>
              <View style={styles.userStatItem}>
                <MaterialCommunityIcons name="account" size={32} color="#2196F3" />
                <Text style={styles.userStatValue}>{stats.usersByRole.patients}</Text>
                <Text style={styles.userStatLabel}>Patients</Text>
              </View>
              <View style={styles.userStatDivider} />
              <View style={styles.userStatItem}>
                <MaterialCommunityIcons name="doctor" size={32} color="#4CAF50" />
                <Text style={styles.userStatValue}>{stats.usersByRole.dentists}</Text>
                <Text style={styles.userStatLabel}>Dentists</Text>
              </View>
              <View style={styles.userStatDivider} />
              <View style={styles.userStatItem}>
                <MaterialCommunityIcons name="account-tie" size={32} color="#FF9800" />
                <Text style={styles.userStatValue}>{stats.usersByRole.staff}</Text>
                <Text style={styles.userStatLabel}>Staff</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Recent Appointments */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Title style={styles.sectionTitle}>Recent Appointments</Title>
              <Button mode="text" onPress={() => navigation.navigate('Appointments')}>
                View All
              </Button>
            </View>
            <Divider />
            {recentAppointments.map((appointment, index) => (
              <List.Item
                key={appointment.id}
                title={appointment.patient?.name}
                description={`${format(new Date(appointment.date), 'MMM d, yyyy')} at ${appointment.start_time} - Dr. ${appointment.dentist?.name}`}
                left={(props) => (
                  <Avatar.Text
                    {...props}
                    size={40}
                    label={appointment.patient?.name?.substring(0, 2).toUpperCase()}
                    color="#fff"
                    style={{ backgroundColor: theme.colors.primary }}
                  />
                )}
                right={(props) => (
                  <Badge
                    style={[
                      styles.statusBadge,
                      { backgroundColor: appointment.status === 'scheduled' ? '#2196F3' : '#4CAF50' }
                    ]}
                  >
                    {appointment.status}
                  </Badge>
                )}
              />
            ))}
          </Card.Content>
        </Card>

        {/* Recent Activities */}
        <Card style={[styles.sectionCard, styles.lastCard]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Title style={styles.sectionTitle}>Recent Activities</Title>
              <Button mode="text" onPress={() => navigation.navigate('ActivityLogs')}>
                View All
              </Button>
            </View>
            <Divider />
            {recentActivities.map((activity, index) => (
              <List.Item
                key={activity.id}
                title={activity.description}
                description={`${activity.admin?.name} - ${format(new Date(activity.timestamp), 'MMM d, yyyy h:mm a')}`}
                left={(props) => (
                  <MaterialCommunityIcons
                    name="history"
                    size={24}
                    color="#666"
                    style={{ marginLeft: 8, marginTop: 8 }}
                  />
                )}
              />
            ))}
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  statsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  statCard: {
    marginRight: 12,
    width: 140,
    elevation: 2,
  },
  statCardContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  sectionCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAction: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionLabel: {
    fontSize: 12,
    color: '#333',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  userStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 16,
  },
  userStatItem: {
    alignItems: 'center',
    flex: 1,
  },
  userStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
    marginBottom: 4,
  },
  userStatLabel: {
    fontSize: 12,
    color: '#666',
  },
  userStatDivider: {
    width: 1,
    height: 60,
    backgroundColor: '#e0e0e0',
  },
  statusBadge: {
    alignSelf: 'center',
  },
  lastCard: {
    marginBottom: 24,
  },
});
