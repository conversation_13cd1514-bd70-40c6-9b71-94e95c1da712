-- =====================================================
-- APPOINTMENT MANAGEMENT MIGRATION
-- This migration implements PRD-compliant appointment management:
-- 1. Clinic hours: Mon-Sat 10am-6pm
-- 2. 1-hour appointment slots
-- 3. Conflict prevention
-- 4. Follow-up appointments
-- 5. Dentist availability
-- =====================================================

-- =====================================================
-- STEP 1: Update appointments table structure
-- =====================================================

-- Add end_time column if it doesn't exist
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS end_time TIME;

-- Add appointment_type column if it doesn't exist
ALTER TABLE appointments
ADD COLUMN IF NOT EXISTS appointment_type VARCHAR(50) DEFAULT 'checkup';

-- Update existing appointments to have end_time (1 hour after start_time)
UPDATE appointments 
SET end_time = (appointment_time + INTERVAL '1 hour')::TIME
WHERE end_time IS NULL;

-- Make end_time NOT NULL after updating existing records
ALTER TABLE appointments 
ALTER COLUMN end_time SET NOT NULL;

-- Add column for follow-up appointments
ALTER TABLE appointments
ADD COLUMN IF NOT EXISTS followup_id UUID REFERENCES appointments(id);

-- Add column for appointment duration (in minutes)
ALTER TABLE appointments
ADD COLUMN IF NOT EXISTS duration_minutes INTEGER DEFAULT 60;

-- =====================================================
-- STEP 2: Create dentist availability table
-- =====================================================

CREATE TABLE IF NOT EXISTS dentist_availability (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  dentist_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0=Sunday, 6=Saturday
  is_available BOOLEAN DEFAULT true,
  start_time TIME NOT NULL DEFAULT '10:00:00',
  end_time TIME NOT NULL DEFAULT '18:00:00',
  lunch_start TIME,
  lunch_end TIME,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(dentist_id, day_of_week)
);

-- Create index for availability lookups
CREATE INDEX IF NOT EXISTS idx_dentist_availability_dentist 
ON dentist_availability(dentist_id, day_of_week);

-- Insert default availability for all dentists (Mon-Sat, 10am-6pm, closed Sunday)
INSERT INTO dentist_availability (dentist_id, day_of_week, is_available, start_time, end_time)
SELECT DISTINCT 
  u.id as dentist_id,
  dow.day as day_of_week,
  CASE WHEN dow.day = 0 THEN false ELSE true END as is_available,
  '10:00:00'::TIME as start_time,
  '18:00:00'::TIME as end_time
FROM users u
CROSS JOIN generate_series(0, 6) as dow(day)
WHERE u.role = 'dentist'
ON CONFLICT (dentist_id, day_of_week) DO NOTHING;

-- =====================================================
-- STEP 3: Create appointment slots configuration table
-- =====================================================

CREATE TABLE IF NOT EXISTS appointment_slots (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  slot_time TIME NOT NULL UNIQUE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert 1-hour slots from 10am to 5pm (last appointment at 5pm ends at 6pm)
INSERT INTO appointment_slots (slot_time, is_active)
VALUES 
  ('10:00:00', true),
  ('11:00:00', true),
  ('12:00:00', true),
  ('13:00:00', true), -- 1pm
  ('14:00:00', true), -- 2pm
  ('15:00:00', true), -- 3pm
  ('16:00:00', true), -- 4pm
  ('17:00:00', true)  -- 5pm (last slot)
ON CONFLICT (slot_time) DO NOTHING;

-- =====================================================
-- STEP 4: Add conflict prevention function and triggers
-- =====================================================

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS check_appointment_overlap_insert ON appointments;
DROP TRIGGER IF EXISTS check_appointment_overlap_update ON appointments;
DROP FUNCTION IF EXISTS check_appointment_overlap();

-- Create improved conflict prevention function
CREATE OR REPLACE FUNCTION check_appointment_overlap()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the appointment is on a working day (not Sunday)
  IF EXTRACT(DOW FROM NEW.appointment_date) = 0 THEN
    RAISE EXCEPTION 'Cannot schedule appointments on Sundays. The clinic is closed.';
  END IF;
  
  -- Check if the appointment is within clinic hours (10am-6pm)
  IF NEW.appointment_time < '10:00:00'::TIME OR NEW.appointment_time >= '18:00:00'::TIME THEN
    RAISE EXCEPTION 'Appointment time must be between 10:00 AM and 6:00 PM';
  END IF;
  
  IF NEW.end_time > '18:00:00'::TIME THEN
    RAISE EXCEPTION 'Appointment cannot end after 6:00 PM';
  END IF;
  
  -- Check if there's any overlap with existing appointments for the same dentist
  IF EXISTS (
    SELECT 1 FROM appointments
    WHERE dentist_id = NEW.dentist_id
      AND appointment_date = NEW.appointment_date
      AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000')
      AND status NOT IN ('cancelled', 'no-show')
      AND (
        -- Check for time overlap
        (NEW.appointment_time >= appointment_time AND NEW.appointment_time < end_time)
        OR (NEW.end_time > appointment_time AND NEW.end_time <= end_time)
        OR (NEW.appointment_time <= appointment_time AND NEW.end_time >= end_time)
      )
  ) THEN
    RAISE EXCEPTION 'This time slot conflicts with an existing appointment for the selected dentist';
  END IF;
  
  -- Check if patient already has an appointment on the same day (optional - can be removed if multiple appointments allowed)
  IF TG_OP = 'INSERT' AND EXISTS (
    SELECT 1 FROM appointments
    WHERE patient_id = NEW.patient_id
      AND appointment_date = NEW.appointment_date
      AND status NOT IN ('cancelled', 'no-show')
  ) THEN
    -- Just a warning, not blocking
    -- RAISE NOTICE 'Patient already has an appointment on this date';
    NULL; -- Comment out if you want to enforce one appointment per day
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for insert
CREATE TRIGGER check_appointment_overlap_insert
BEFORE INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION check_appointment_overlap();

-- Create trigger for update
CREATE TRIGGER check_appointment_overlap_update
BEFORE UPDATE ON appointments
FOR EACH ROW
WHEN (
  OLD.dentist_id IS DISTINCT FROM NEW.dentist_id 
  OR OLD.appointment_date IS DISTINCT FROM NEW.appointment_date
  OR OLD.appointment_time IS DISTINCT FROM NEW.appointment_time
  OR OLD.end_time IS DISTINCT FROM NEW.end_time
  OR OLD.status IS DISTINCT FROM NEW.status
)
EXECUTE FUNCTION check_appointment_overlap();

-- =====================================================
-- STEP 5: Add helpful indexes for performance
-- =====================================================

-- Index for conflict checks
CREATE INDEX IF NOT EXISTS idx_appointments_dentist_date_time 
ON appointments(dentist_id, appointment_date, appointment_time, end_time)
WHERE status NOT IN ('cancelled', 'no-show');

-- Index for patient appointments lookup
CREATE INDEX IF NOT EXISTS idx_appointments_patient_date 
ON appointments(patient_id, appointment_date)
WHERE status NOT IN ('cancelled', 'no-show');

-- Index for follow-up lookups
CREATE INDEX IF NOT EXISTS idx_appointments_followup 
ON appointments(followup_id)
WHERE followup_id IS NOT NULL;

-- Index for appointment type
CREATE INDEX IF NOT EXISTS idx_appointments_type
ON appointments(appointment_type);

-- =====================================================
-- STEP 6: Create helper functions for appointments
-- =====================================================

-- Function to get available slots for a dentist on a specific date
CREATE OR REPLACE FUNCTION get_available_slots(
  p_date DATE,
  p_dentist_id UUID
)
RETURNS TABLE (
  slot_time TIME,
  is_available BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.slot_time,
    NOT EXISTS (
      SELECT 1 
      FROM appointments a
      WHERE a.dentist_id = p_dentist_id
        AND a.appointment_date = p_date
        AND a.appointment_time = s.slot_time
        AND a.status NOT IN ('cancelled', 'no-show')
    ) as is_available
  FROM appointment_slots s
  WHERE s.is_active = true
  ORDER BY s.slot_time;
END;
$$ LANGUAGE plpgsql;

-- Function to check if a dentist is available on a specific day
CREATE OR REPLACE FUNCTION is_dentist_available(
  p_dentist_id UUID,
  p_date DATE
)
RETURNS BOOLEAN AS $$
DECLARE
  v_day_of_week INTEGER;
  v_is_available BOOLEAN;
BEGIN
  v_day_of_week := EXTRACT(DOW FROM p_date);
  
  -- Check if it's Sunday (clinic closed)
  IF v_day_of_week = 0 THEN
    RETURN FALSE;
  END IF;
  
  -- Check dentist's availability for this day of week
  SELECT is_available INTO v_is_available
  FROM dentist_availability
  WHERE dentist_id = p_dentist_id
    AND day_of_week = v_day_of_week;
  
  -- If no record found, assume available (Mon-Sat)
  IF v_is_available IS NULL THEN
    RETURN TRUE;
  END IF;
  
  RETURN v_is_available;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 7: Add constraints (without dropping existing ones)
-- =====================================================

-- Add constraint to ensure appointments are within clinic hours (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'check_clinic_hours'
  ) THEN
    ALTER TABLE appointments
    ADD CONSTRAINT check_clinic_hours
    CHECK (
      appointment_time >= '10:00:00'::TIME 
      AND appointment_time < '18:00:00'::TIME
      AND end_time <= '18:00:00'::TIME
    );
  END IF;
END $$;

-- Add constraint to ensure appointments are not on Sundays (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'check_clinic_days'
  ) THEN
    ALTER TABLE appointments
    ADD CONSTRAINT check_clinic_days
    CHECK (
      EXTRACT(DOW FROM appointment_date) != 0
    );
  END IF;
END $$;

-- =====================================================
-- STEP 8: Add comments for documentation
-- =====================================================

COMMENT ON TABLE dentist_availability IS 'Stores availability schedule for each dentist by day of week';
COMMENT ON COLUMN appointments.end_time IS 'End time of the appointment (typically 1 hour after start time)';
COMMENT ON COLUMN appointments.followup_id IS 'Reference to the original appointment if this is a follow-up';
COMMENT ON COLUMN appointments.appointment_type IS 'Type of appointment (checkup, cleaning, filling, etc.)';
COMMENT ON COLUMN appointments.duration_minutes IS 'Duration of the appointment in minutes (default 60)';
COMMENT ON CONSTRAINT check_clinic_hours ON appointments IS 'Ensures appointments are within clinic hours (10am-6pm)';
COMMENT ON CONSTRAINT check_clinic_days ON appointments IS 'Ensures appointments are not scheduled on Sundays';

-- =====================================================
-- STEP 9: Create view for appointment schedule
-- =====================================================

CREATE OR REPLACE VIEW appointment_schedule AS
SELECT 
  a.id,
  a.appointment_date,
  a.appointment_time,
  a.end_time,
  a.appointment_type,
  a.status,
  a.notes,
  a.followup_id,
  p.name as patient_name,
  p.email as patient_email,
  p.phone as patient_phone,
  d.name as dentist_name,
  d.email as dentist_email,
  CASE 
    WHEN a.followup_id IS NOT NULL THEN 'Follow-up'
    ELSE 'Regular'
  END as appointment_category,
  CASE 
    WHEN a.appointment_date < CURRENT_DATE THEN 'Past'
    WHEN a.appointment_date = CURRENT_DATE THEN 'Today'
    WHEN a.appointment_date = CURRENT_DATE + INTERVAL '1 day' THEN 'Tomorrow'
    ELSE 'Future'
  END as time_category
FROM appointments a
LEFT JOIN users p ON a.patient_id = p.id
LEFT JOIN users d ON a.dentist_id = d.id
WHERE a.status NOT IN ('cancelled', 'no-show')
ORDER BY a.appointment_date, a.appointment_time;

-- Grant appropriate permissions
GRANT SELECT ON appointment_schedule TO authenticated;
GRANT SELECT ON dentist_availability TO authenticated;
GRANT SELECT ON appointment_slots TO authenticated;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================
-- This migration has:
-- 1. Updated appointments table with end_time and followup_id
-- 2. Created dentist_availability table for managing schedules
-- 3. Created appointment_slots table for slot configuration
-- 4. Added conflict prevention triggers
-- 5. Added helpful functions and views
-- 6. Added appropriate indexes for performance
-- 7. Added constraints for clinic hours and days
-- =====================================================
