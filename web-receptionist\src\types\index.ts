// User roles enum
export enum UserRole {
  PATIENT = 'patient',
  STAFF = 'staff',
  DENTIST = 'dentist',
  ADMIN = 'admin'
}

// User status enum
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

// Appointment status enum
export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

// Notification type enum
export enum NotificationType {
  APPOINTMENT_REMINDER = 'appointment_reminder',
  APPOINTMENT_CONFIRMATION = 'appointment_confirmation',
  APPOINTMENT_CANCELLATION = 'appointment_cancellation',
  APPOINTMENT_RESCHEDULED = 'appointment_rescheduled',
  FOLLOW_UP_REMINDER = 'follow_up_reminder'
}

// Notification status enum
export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  FAILED = 'failed',
  DELIVERED = 'delivered'
}

// Admin action types
export enum AdminActionType {
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  ROLE_CHANGED = 'role_changed',
  APPOINTMENT_MODIFIED = 'appointment_modified',
  APPOINTMENT_CANCELLED = 'appointment_cancelled',
  SYSTEM_SETTING_CHANGED = 'system_setting_changed',
  NOTIFICATION_TRIGGERED = 'notification_triggered'
}

// User interface
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: string;
  role: UserRole;
  status: UserStatus;
  created_at: string;
  updated_at: string;
  last_login?: string;
  avatar_url?: string;
  date_of_birth?: string;
  gender?: string;
  blood_group?: string;
  allergies?: string;
  medical_conditions?: string;
  emergency_contact?: string;
  insurance_provider?: string;
  insurance_number?: string;
  notes?: string;
}

// Appointment interface
export interface Appointment {
  id: string;
  patient_id: string;
  staff_id?: string;
  dentist_id: string;
  date: string; // ISO date string (YYYY-MM-DD)
  start_time: string; // HH:mm format
  end_time: string; // HH:mm format
  appointment_type?: string; // Type of appointment (checkup, cleaning, etc.)
  duration_minutes?: number; // Duration in minutes
  notes?: string;
  status: AppointmentStatus;
  followup_id?: string;
  created_at: string;
  updated_at: string;
  // Generated columns for compatibility
  appointment_date?: string; // Same as date, for backward compatibility
  appointment_time?: string; // Same as start_time, for backward compatibility
  // Relations
  patient?: User;
  staff?: User;
  dentist?: User;
  followup?: Appointment;
}

// Notification interface
export interface Notification {
  id: string;
  user_id: string;
  appointment_id?: string;
  type: NotificationType;
  title: string;
  message: string;
  sent_at?: string;
  status: NotificationStatus;
  created_at: string;
  updated_at: string;
  // Relations
  user?: User;
  appointment?: Appointment;
}

// Admin Activity Log interface
export interface AdminActivityLog {
  id: string;
  admin_id: string;
  action_type: AdminActionType;
  target_id?: string;
  target_type?: string;
  description: string;
  metadata?: Record<string, any>;
  timestamp: string;
  ip_address?: string;
  // Relations
  admin?: User;
}

// Time slot interface for appointment booking
export interface TimeSlot {
  date: string;
  startTime: string;
  endTime: string;
  available: boolean;
  dentistId?: string;
  dentistName?: string;
}

// Dashboard stats interface
export interface DashboardStats {
  totalAppointments: number;
  upcomingAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  todayAppointments: number;
  weekAppointments: number;
  monthAppointments: number;
}

// Auth context types
export interface AuthContextType {
  user: User | null;
  session: any | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, role?: UserRole) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
}

// Appointment context types
export interface AppointmentContextType {
  appointments: Appointment[];
  loading: boolean;
  error: string | null;
  fetchAppointments: () => Promise<void>;
  createAppointment: (appointment: Partial<Appointment>) => Promise<void>;
  updateAppointment: (id: string, updates: Partial<Appointment>) => Promise<void>;
  cancelAppointment: (id: string) => Promise<void>;
  getAvailableSlots: (date: string, dentistId?: string) => Promise<TimeSlot[]>;
}

// System settings interface
export interface SystemSettings {
  id: string;
  clinic_name: string;
  clinic_address: string;
  clinic_phone: string;
  clinic_email: string;
  working_days: string[]; // ['monday', 'tuesday', ...]
  working_hours: {
    start: string; // '10:00'
    end: string; // '18:00'
  };
  slot_duration: number; // in minutes
  booking_advance_limit: number; // days in advance
  cancellation_advance_limit: number; // hours before appointment
  reminder_advance_time: number; // hours before appointment
  max_appointments_per_day?: number;
  notification_templates?: {
    [key in NotificationType]?: {
      title: string;
      message: string;
    };
  };
  updated_at: string;
}

// Filter options for appointments
export interface AppointmentFilters {
  status?: AppointmentStatus;
  dentistId?: string;
  patientId?: string;
  dateFrom?: string;
  dateTo?: string;
  searchQuery?: string;
}

// Pagination interface
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// API Response interface
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}