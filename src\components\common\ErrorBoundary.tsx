import React, { Component, ReactNode, ErrorInfo } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Button, Title, Surface, IconButton } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { lightTheme } from '../../theme';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      showDetails: false,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call the optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In production, you might want to log to an error reporting service
    // logErrorToService(error, errorInfo);
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    });
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails,
    }));
  };

  render() {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <Surface style={styles.errorCard} elevation={2}>
            <View style={styles.iconContainer}>
              <MaterialCommunityIcons
                name="alert-circle-outline"
                size={80}
                color={lightTheme.custom.colors.error.main}
              />
            </View>

            <Title style={styles.title}>Oops! Something went wrong</Title>
            
            <Text style={styles.message}>
              We're sorry for the inconvenience. The application encountered an unexpected error.
            </Text>

            <View style={styles.actions}>
              <Button
                mode="contained"
                onPress={this.resetError}
                style={styles.primaryButton}
                icon="refresh"
              >
                Try Again
              </Button>

              <Button
                mode="outlined"
                onPress={this.toggleDetails}
                style={styles.secondaryButton}
                icon={this.state.showDetails ? "chevron-up" : "chevron-down"}
              >
                {this.state.showDetails ? 'Hide' : 'Show'} Details
              </Button>
            </View>

            {this.state.showDetails && (
              <ScrollView style={styles.detailsContainer}>
                <Surface style={styles.detailsCard} elevation={1}>
                  <Text style={styles.detailsTitle}>Error Details:</Text>
                  <Text style={styles.errorText}>
                    {this.state.error?.toString()}
                  </Text>
                  
                  {this.state.errorInfo && (
                    <>
                      <Text style={styles.detailsTitle}>Component Stack:</Text>
                      <Text style={styles.stackTrace}>
                        {this.state.errorInfo.componentStack}
                      </Text>
                    </>
                  )}
                </Surface>
              </ScrollView>
            )}
          </Surface>
        </View>
      );
    }

    return this.props.children;
  }
}

// Functional error component for displaying errors in UI
interface ErrorMessageProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  type?: 'error' | 'warning' | 'info';
  showIcon?: boolean;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Something went wrong',
  message = 'Please try again later',
  onRetry,
  type = 'error',
  showIcon = true,
}) => {
  const getIconName = () => {
    switch (type) {
      case 'warning':
        return 'alert';
      case 'info':
        return 'information';
      default:
        return 'alert-circle';
    }
  };

  const getIconColor = () => {
    switch (type) {
      case 'warning':
        return lightTheme.custom.colors.warning.main;
      case 'info':
        return lightTheme.custom.colors.info.main;
      default:
        return lightTheme.custom.colors.error.main;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'warning':
        return lightTheme.custom.colors.warning.background;
      case 'info':
        return lightTheme.custom.colors.info.background;
      default:
        return lightTheme.custom.colors.error.background;
    }
  };

  return (
    <Surface style={[styles.errorMessageContainer, { backgroundColor: getBackgroundColor() }]} elevation={1}>
      {showIcon && (
        <MaterialCommunityIcons
          name={getIconName()}
          size={48}
          color={getIconColor()}
          style={styles.errorIcon}
        />
      )}
      <View style={styles.errorContent}>
        <Text style={[styles.errorTitle, { color: getIconColor() }]}>{title}</Text>
        <Text style={styles.errorDescription}>{message}</Text>
      </View>
      {onRetry && (
        <Button
          mode="text"
          onPress={onRetry}
          style={styles.retryButton}
          textColor={getIconColor()}
        >
          Retry
        </Button>
      )}
    </Surface>
  );
};

// Empty state component
interface EmptyStateProps {
  title?: string;
  message?: string;
  icon?: string;
  onAction?: () => void;
  actionLabel?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title = 'No data found',
  message = 'There is no data to display at the moment',
  icon = 'folder-open-outline',
  onAction,
  actionLabel = 'Refresh',
}) => {
  return (
    <View style={styles.emptyStateContainer}>
      <MaterialCommunityIcons
        name={icon}
        size={80}
        color={lightTheme.custom.colors.neutral.disabled}
        style={styles.emptyIcon}
      />
      <Title style={styles.emptyTitle}>{title}</Title>
      <Text style={styles.emptyMessage}>{message}</Text>
      {onAction && (
        <Button
          mode="contained"
          onPress={onAction}
          style={styles.emptyButton}
        >
          {actionLabel}
        </Button>
      )}
    </View>
  );
};

// Network error component
interface NetworkErrorProps {
  onRetry?: () => void;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({ onRetry }) => {
  return (
    <View style={styles.networkErrorContainer}>
      <MaterialCommunityIcons
        name="wifi-off"
        size={80}
        color={lightTheme.custom.colors.neutral.disabled}
      />
      <Title style={styles.networkTitle}>No Internet Connection</Title>
      <Text style={styles.networkMessage}>
        Please check your internet connection and try again
      </Text>
      {onRetry && (
        <Button
          mode="contained"
          onPress={onRetry}
          style={styles.networkButton}
          icon="refresh"
        >
          Try Again
        </Button>
      )}
    </View>
  );
};

// Permission denied component
interface PermissionDeniedProps {
  message?: string;
  onGoBack?: () => void;
}

export const PermissionDenied: React.FC<PermissionDeniedProps> = ({
  message = "You don't have permission to access this content",
  onGoBack,
}) => {
  return (
    <View style={styles.permissionContainer}>
      <MaterialCommunityIcons
        name="lock-outline"
        size={80}
        color={lightTheme.custom.colors.warning.main}
      />
      <Title style={styles.permissionTitle}>Access Denied</Title>
      <Text style={styles.permissionMessage}>{message}</Text>
      {onGoBack && (
        <Button
          mode="outlined"
          onPress={onGoBack}
          style={styles.permissionButton}
          icon="arrow-left"
        >
          Go Back
        </Button>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: lightTheme.custom.colors.neutral.background,
  },
  errorCard: {
    padding: 24,
    borderRadius: 12,
    width: '100%',
    maxWidth: 400,
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
    color: lightTheme.custom.colors.neutral.text.primary,
  },
  message: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    color: lightTheme.custom.colors.neutral.text.secondary,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'column',
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    width: '100%',
  },
  secondaryButton: {
    width: '100%',
  },
  detailsContainer: {
    width: '100%',
    marginTop: 20,
    maxHeight: 200,
  },
  detailsCard: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: lightTheme.custom.colors.neutral.surfaceVariant,
  },
  detailsTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
    color: lightTheme.custom.colors.neutral.text.primary,
  },
  errorText: {
    fontSize: 12,
    color: lightTheme.custom.colors.error.main,
    marginBottom: 12,
    fontFamily: 'monospace',
  },
  stackTrace: {
    fontSize: 10,
    color: lightTheme.custom.colors.neutral.text.secondary,
    fontFamily: 'monospace',
  },

  // Error Message styles
  errorMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    margin: 16,
  },
  errorIcon: {
    marginRight: 12,
  },
  errorContent: {
    flex: 1,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  errorDescription: {
    fontSize: 14,
    color: lightTheme.custom.colors.neutral.text.secondary,
  },
  retryButton: {
    marginLeft: 8,
  },

  // Empty State styles
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyIcon: {
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: lightTheme.custom.colors.neutral.text.primary,
  },
  emptyMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    color: lightTheme.custom.colors.neutral.text.secondary,
  },
  emptyButton: {
    paddingHorizontal: 32,
  },

  // Network Error styles
  networkErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  networkTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 8,
  },
  networkMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    color: lightTheme.custom.colors.neutral.text.secondary,
  },
  networkButton: {
    paddingHorizontal: 32,
  },

  // Permission Denied styles
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 8,
  },
  permissionMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    color: lightTheme.custom.colors.neutral.text.secondary,
  },
  permissionButton: {
    paddingHorizontal: 32,
  },
});
