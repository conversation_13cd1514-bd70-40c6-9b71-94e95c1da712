/**
 * Appointment Configuration Constants
 * As per PRD: Monday to Saturday, 10am to 6pm, 1-hour slots
 */

import { addHours, format, setHours, setMinutes } from 'date-fns';

// Clinic Operating Days (0 = Sunday, 6 = Saturday)
export const CLINIC_DAYS = {
  MONDAY: 1,
  TUESDAY: 2,
  WEDNESDAY: 3,
  THURSDAY: 4,
  FRIDAY: 5,
  SATURDAY: 6,
};

// Clinic is closed on Sunday
export const CLOSED_DAYS = [0]; // Sunday

// Working Hours
export const CLINIC_HOURS = {
  START_HOUR: 10, // 10:00 AM
  END_HOUR: 18,   // 6:00 PM (18:00)
  LUNCH_START: 13, // 1:00 PM (optional lunch break)
  LUNCH_END: 14,   // 2:00 PM (optional lunch break)
};

// Slot Duration
export const SLOT_DURATION_MINUTES = 60; // 1-hour slots as per PRD

// Generate time slots for the clinic hours
export const generateTimeSlots = (includeLunchBreak: boolean = false): string[] => {
  const slots: string[] = [];
  const startHour = CLINIC_HOURS.START_HOUR;
  const endHour = CLINIC_HOURS.END_HOUR;
  
  for (let hour = startHour; hour < endHour; hour++) {
    // Skip lunch hour if lunch break is enabled
    if (includeLunchBreak && hour === CLINIC_HOURS.LUNCH_START) {
      continue;
    }
    
    const time = `${hour.toString().padStart(2, '0')}:00`;
    slots.push(time);
  }
  
  return slots;
};

// Default time slots (without lunch break)
export const TIME_SLOTS = generateTimeSlots(false);

// Time slots with lunch break
export const TIME_SLOTS_WITH_LUNCH = generateTimeSlots(true);

// Appointment Types with durations (all set to 60 minutes for 1-hour slots)
export const APPOINTMENT_TYPES = [
  {
    value: 'checkup',
    label: 'Regular Checkup',
    duration: SLOT_DURATION_MINUTES,
    color: '#4CAF50',
    icon: 'tooth',
    description: 'Routine dental examination and oral health assessment'
  },
  {
    value: 'cleaning',
    label: 'Teeth Cleaning',
    duration: SLOT_DURATION_MINUTES,
    color: '#2196F3',
    icon: 'tooth',
    description: 'Professional dental cleaning and plaque removal'
  },
  {
    value: 'filling',
    label: 'Filling',
    duration: SLOT_DURATION_MINUTES,
    color: '#FF9800',
    icon: 'tooth',
    description: 'Cavity filling and tooth restoration'
  },
  {
    value: 'extraction',
    label: 'Tooth Extraction',
    duration: SLOT_DURATION_MINUTES,
    color: '#F44336',
    icon: 'tooth',
    description: 'Tooth removal procedure'
  },
  {
    value: 'root_canal',
    label: 'Root Canal',
    duration: SLOT_DURATION_MINUTES,
    color: '#9C27B0',
    icon: 'tooth',
    description: 'Root canal treatment'
  },
  {
    value: 'crown',
    label: 'Crown',
    duration: SLOT_DURATION_MINUTES,
    color: '#E91E63',
    icon: 'tooth',
    description: 'Dental crown placement'
  },
  {
    value: 'orthodontics',
    label: 'Orthodontics',
    duration: SLOT_DURATION_MINUTES,
    color: '#00BCD4',
    icon: 'tooth',
    description: 'Braces adjustment and orthodontic consultation'
  },
  {
    value: 'consultation',
    label: 'Consultation',
    duration: SLOT_DURATION_MINUTES,
    color: '#795548',
    icon: 'tooth',
    description: 'General dental consultation'
  },
  {
    value: 'emergency',
    label: 'Emergency',
    duration: SLOT_DURATION_MINUTES,
    color: '#FF5722',
    icon: 'tooth',
    description: 'Emergency dental care'
  },
  {
    value: 'followup',
    label: 'Follow-up',
    duration: SLOT_DURATION_MINUTES,
    color: '#607D8B',
    icon: 'tooth',
    description: 'Follow-up appointment for previous treatment'
  },
];

// Appointment Status Configuration
export const APPOINTMENT_STATUS = {
  scheduled: { 
    label: 'Scheduled', 
    color: '#2196F3', 
    icon: 'calendar-clock',
    description: 'Appointment is scheduled'
  },
  confirmed: { 
    label: 'Confirmed', 
    color: '#4CAF50', 
    icon: 'check-circle',
    description: 'Appointment is confirmed'
  },
  completed: { 
    label: 'Completed', 
    color: '#9E9E9E', 
    icon: 'check-all',
    description: 'Appointment completed successfully'
  },
  cancelled: { 
    label: 'Cancelled', 
    color: '#F44336', 
    icon: 'cancel',
    description: 'Appointment was cancelled'
  },
  'no-show': { 
    label: 'No Show', 
    color: '#FF9800', 
    icon: 'account-off',
    description: 'Patient did not show up'
  },
};

// Days of week for display
export const DAYS_OF_WEEK = [
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' },
];

// Check if a date is a clinic working day
export const isClinicWorkingDay = (date: Date): boolean => {
  const dayOfWeek = date.getDay();
  return !CLOSED_DAYS.includes(dayOfWeek);
};

// Check if a time slot is within clinic hours
export const isWithinClinicHours = (time: string): boolean => {
  const [hours] = time.split(':').map(Number);
  return hours >= CLINIC_HOURS.START_HOUR && hours < CLINIC_HOURS.END_HOUR;
};

// Calculate end time based on start time and duration
export const calculateEndTime = (startTime: string, durationMinutes: number = SLOT_DURATION_MINUTES): string => {
  const [hours, minutes] = startTime.split(':').map(Number);
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);
  const endDate = addHours(date, durationMinutes / 60);
  return format(endDate, 'HH:mm');
};

// Format time for display (12-hour format)
export const formatTimeDisplay = (time: string): string => {
  const [hours, minutes] = time.split(':').map(Number);
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);
  return format(date, 'h:mm a');
};

// Get clinic status message
export const getClinicStatusMessage = (date: Date): string => {
  if (!isClinicWorkingDay(date)) {
    return 'Clinic is closed on Sundays';
  }
  
  const currentHour = date.getHours();
  
  if (currentHour < CLINIC_HOURS.START_HOUR) {
    return `Clinic opens at ${formatTimeDisplay(`${CLINIC_HOURS.START_HOUR}:00`)}`;
  }
  
  if (currentHour >= CLINIC_HOURS.END_HOUR) {
    return `Clinic is closed. Opens tomorrow at ${formatTimeDisplay(`${CLINIC_HOURS.START_HOUR}:00`)}`;
  }
  
  return 'Clinic is open';
};

// Booking Rules
export const BOOKING_RULES = {
  MAX_ADVANCE_DAYS: 30, // Can book up to 30 days in advance
  MIN_ADVANCE_HOURS: 2, // Must book at least 2 hours in advance
  MAX_APPOINTMENTS_PER_DAY: 1, // Max appointments per patient per day
  ALLOW_BACK_TO_BACK: false, // Don't allow back-to-back appointments for same dentist
  BUFFER_MINUTES: 0, // No buffer needed with 1-hour slots
};

// Notification Timings
export const NOTIFICATION_TIMINGS = {
  REMINDER_HOURS_BEFORE: 24, // Send reminder 24 hours before appointment
  CONFIRMATION_IMMEDIATE: true, // Send confirmation immediately after booking
  FOLLOW_UP_DAYS_AFTER: 7, // Send follow-up reminder 7 days after appointment
};

// Error Messages
export const APPOINTMENT_ERRORS = {
  SLOT_UNAVAILABLE: 'This time slot is no longer available. Please select another time.',
  CLINIC_CLOSED: 'The clinic is closed at this time. Please select a time during clinic hours.',
  INVALID_DATE: 'Please select a valid date for your appointment.',
  PAST_TIME: 'Cannot book appointments in the past. Please select a future time.',
  DOUBLE_BOOKING: 'You already have an appointment at this time.',
  MAX_LIMIT_REACHED: 'You have reached the maximum number of appointments for this day.',
  TOO_SOON: 'Appointments must be booked at least 2 hours in advance.',
  TOO_FAR: 'Appointments can only be booked up to 30 days in advance.',
};

// Success Messages
export const APPOINTMENT_SUCCESS = {
  BOOKED: 'Your appointment has been successfully booked!',
  RESCHEDULED: 'Your appointment has been rescheduled successfully.',
  CANCELLED: 'Your appointment has been cancelled.',
  CONFIRMED: 'Your appointment has been confirmed.',
  UPDATED: 'Appointment details have been updated.',
};
