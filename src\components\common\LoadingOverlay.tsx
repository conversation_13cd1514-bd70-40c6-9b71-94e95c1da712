import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ActivityIndicator, Text, Surface } from 'react-native-paper';
import theme from '../../theme';

interface LoadingOverlayProps {
  message?: string;
  fullscreen?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ message = 'Loading...', fullscreen = true }) => {
  if (fullscreen) {
    return (
      <View style={styles.fullscreen}>
        <Surface style={styles.card} elevation={2}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.text}>{message}</Text>
        </Surface>
      </View>
    );
  }

  return (
    <View style={styles.inline}>
      <ActivityIndicator size="small" color={theme.colors.primary} />
      <Text style={styles.inlineText}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  fullscreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  card: {
    padding: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  text: {
    marginTop: 12,
  },
  inline: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inlineText: {
    marginLeft: 8,
  },
});

