import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  RefreshControl,
  Alert,
  FlatList,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../../lib/supabase';
import { format, parseISO, isPast } from 'date-fns';
import { NavigationProp } from '@react-navigation/native';

interface PatientRecordsProps {
  navigation: NavigationProp<any>;
}

interface Patient {
  id: string;
  email: string;
  phone?: string;
  name?: string;
  created_at: string;
}

interface PatientAppointment {
  id: string;
  date: string;
  start_time: string;
  end_time: string;
  appointment_type?: string;
  status: string;
  notes?: string;
}

const PatientRecordsNew: React.FC<PatientRecordsProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [patientAppointments, setPatientAppointments] = useState<PatientAppointment[]>([]);
  const [loadingAppointments, setLoadingAppointments] = useState(false);

  const fetchPatients = useCallback(async () => {
    if (!user?.id) return;

    try {
      // Fetch unique patients who have appointments with this dentist
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          patient_id,
          patient:users!appointments_patient_id_fkey(id, email, phone, name, created_at)
        `)
        .eq('dentist_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Remove duplicates based on patient_id
      const uniquePatientsMap = new Map<string, any>();
      data?.forEach(item => {
        const patientObj = Array.isArray(item.patient) ? item.patient[0] : item.patient;
        if (patientObj && patientObj.id && !uniquePatientsMap.has(patientObj.id)) {
          uniquePatientsMap.set(patientObj.id, patientObj);
        }
      });

      const uniquePatients = Array.from(uniquePatientsMap.values());
      setPatients(uniquePatients);
      setFilteredPatients(uniquePatients);
    } catch (error) {
      console.error('Error fetching patients:', error);
      Alert.alert('Error', 'Failed to load patients');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user?.id]);

  const fetchPatientAppointments = async (patientId: string) => {
    setLoadingAppointments(true);
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('patient_id', patientId)
        .eq('dentist_id', user?.id)
        .order('date', { ascending: false })
        .order('start_time', { ascending: false });

      if (error) throw error;

      setPatientAppointments(data || []);
    } catch (error) {
      console.error('Error fetching patient appointments:', error);
      Alert.alert('Error', 'Failed to load appointment history');
    } finally {
      setLoadingAppointments(false);
    }
  };

  useEffect(() => {
    fetchPatients();
  }, [fetchPatients]);

  useEffect(() => {
    // Filter patients based on search text
    if (searchText) {
      const filtered = patients.filter(patient => {
        const searchLower = searchText.toLowerCase();
        return (
          patient.name?.toLowerCase().includes(searchLower) ||
          patient.email.toLowerCase().includes(searchLower) ||
          patient.phone?.includes(searchText)
        );
      });
      setFilteredPatients(filtered);
    } else {
      setFilteredPatients(patients);
    }
  }, [searchText, patients]);

  const onRefresh = () => {
    setRefreshing(true);
    setSelectedPatient(null);
    setPatientAppointments([]);
    fetchPatients();
  };

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient);
    fetchPatientAppointments(patient.id);
  };

  const handleCall = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  const handleEmail = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return '#2196F3';
      case 'completed': return '#4CAF50';
      case 'cancelled': return '#F44336';
      case 'no_show': return '#FF9800';
      default: return '#666';
    }
  };

  const renderPatientItem = ({ item }: { item: Patient }) => (
    <TouchableOpacity
      style={styles.patientCard}
      onPress={() => handlePatientSelect(item)}
    >
      <View style={styles.patientAvatar}>
        <Text style={styles.avatarText}>
          {item.name ? item.name.substring(0, 2).toUpperCase() : 'PT'}
        </Text>
      </View>
      <View style={styles.patientInfo}>
        <Text style={styles.patientName}>{item.name || 'Unknown Patient'}</Text>
        <Text style={styles.patientContact}>{item.email}</Text>
        {item.phone && (
          <Text style={styles.patientContact}>{item.phone}</Text>
        )}
      </View>
      <MaterialIcons name="chevron-right" size={24} color="#666" />
    </TouchableOpacity>
  );

  const renderAppointmentItem = ({ item }: { item: PatientAppointment }) => {
    const appointmentDate = parseISO(item.date);
    const isPastAppointment = isPast(appointmentDate);

    return (
      <View style={[styles.appointmentCard, isPastAppointment && styles.pastAppointment]}>
        <View style={styles.appointmentHeader}>
          <View style={styles.appointmentDateTime}>
            <Text style={styles.appointmentDate}>
              {format(appointmentDate, 'MMM dd, yyyy')}
            </Text>
            <Text style={styles.appointmentTime}>
              {format(parseISO(`2000-01-01T${item.start_time}`), 'h:mm a')} - 
              {format(parseISO(`2000-01-01T${item.end_time}`), 'h:mm a')}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>
        {item.appointment_type && (
          <Text style={styles.appointmentType}>{item.appointment_type}</Text>
        )}
        {item.notes && (
          <View style={styles.notesContainer}>
            <MaterialIcons name="note" size={14} color="#999" />
            <Text style={styles.notesText}>Scheduling Note: {item.notes}</Text>
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading Patients...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {!selectedPatient ? (
        // Patient List View
        <>
          <View style={styles.header}>
            <Text style={styles.headerTitle}>Patient Quick View</Text>
            <Text style={styles.headerSubtitle}>Contact info and appointment history only</Text>
          </View>

          <View style={styles.searchContainer}>
            <MaterialIcons name="search" size={20} color="#666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search by name, email, or phone..."
              value={searchText}
              onChangeText={setSearchText}
              autoCapitalize="none"
            />
            {searchText !== '' && (
              <TouchableOpacity onPress={() => setSearchText('')}>
                <MaterialIcons name="clear" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>

          <FlatList
            data={filteredPatients}
            renderItem={renderPatientItem}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <MaterialIcons name="people-outline" size={48} color="#ccc" />
                <Text style={styles.emptyText}>
                  {searchText ? 'No patients found matching your search' : 'No patients found'}
                </Text>
              </View>
            }
            contentContainerStyle={filteredPatients.length === 0 ? styles.emptyListContainer : undefined}
          />
        </>
      ) : (
        // Patient Detail View
        <ScrollView
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <View style={styles.detailHeader}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => {
                setSelectedPatient(null);
                setPatientAppointments([]);
              }}
            >
              <MaterialIcons name="arrow-back" size={24} color="#007AFF" />
              <Text style={styles.backText}>Back to List</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.patientDetailCard}>
            <View style={styles.patientDetailAvatar}>
              <Text style={styles.detailAvatarText}>
                {selectedPatient.name ?
                  selectedPatient.name.substring(0, 2).toUpperCase() : 'PT'}
              </Text>
            </View>
            <Text style={styles.patientDetailName}>
              {selectedPatient.name || 'Unknown Patient'}
            </Text>
            
            <View style={styles.contactActions}>
              <TouchableOpacity
                style={styles.contactButton}
                onPress={() => handleEmail(selectedPatient.email)}
              >
                <MaterialIcons name="email" size={20} color="#007AFF" />
                <Text style={styles.contactButtonText}>{selectedPatient.email}</Text>
              </TouchableOpacity>
              
              {selectedPatient.phone && (
                <TouchableOpacity
                  style={styles.contactButton}
                  onPress={() => handleCall(selectedPatient.phone!)}
                >
                  <FontAwesome5 name="phone" size={18} color="#4CAF50" />
                  <Text style={styles.contactButtonText}>{selectedPatient.phone}</Text>
                </TouchableOpacity>
              )}
            </View>

            <Text style={styles.memberSince}>
              Patient since {format(parseISO(selectedPatient.created_at), 'MMM yyyy')}
            </Text>
          </View>

          <View style={styles.appointmentHistorySection}>
            <Text style={styles.sectionTitle}>Appointment History</Text>
            <Text style={styles.sectionSubtitle}>
              Showing all appointments with you
            </Text>

            {loadingAppointments ? (
              <ActivityIndicator size="small" color="#007AFF" style={styles.appointmentLoader} />
            ) : patientAppointments.length > 0 ? (
              <FlatList
                data={patientAppointments}
                renderItem={renderAppointmentItem}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
              />
            ) : (
              <View style={styles.noAppointments}>
                <MaterialIcons name="event-busy" size={32} color="#ccc" />
                <Text style={styles.noAppointmentsText}>No appointment history found</Text>
              </View>
            )}
          </View>

          <View style={styles.disclaimer}>
            <MaterialIcons name="info-outline" size={16} color="#999" />
            <Text style={styles.disclaimerText}>
              This view shows contact and scheduling information only. 
              No medical records or treatment details are displayed.
            </Text>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 15,
    marginVertical: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  patientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    marginHorizontal: 15,
    marginVertical: 5,
    padding: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  patientAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  patientContact: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  emptyState: {
    alignItems: 'center',
    padding: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 15,
    textAlign: 'center',
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  detailHeader: {
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backText: {
    fontSize: 16,
    color: '#007AFF',
    marginLeft: 5,
  },
  patientDetailCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  patientDetailAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  detailAvatarText: {
    color: '#fff',
    fontSize: 28,
    fontWeight: 'bold',
  },
  patientDetailName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  contactActions: {
    width: '100%',
    marginVertical: 10,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginVertical: 5,
  },
  contactButtonText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 10,
  },
  memberSince: {
    fontSize: 12,
    color: '#999',
    marginTop: 10,
  },
  appointmentHistorySection: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  appointmentLoader: {
    marginTop: 20,
  },
  appointmentCard: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
    marginVertical: 5,
    borderLeftWidth: 3,
    borderLeftColor: '#007AFF',
  },
  pastAppointment: {
    opacity: 0.7,
    borderLeftColor: '#ccc',
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  appointmentDateTime: {
    flex: 1,
  },
  appointmentDate: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  appointmentTime: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  appointmentType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  notesContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  notesText: {
    fontSize: 12,
    color: '#999',
    marginLeft: 5,
    flex: 1,
  },
  noAppointments: {
    alignItems: 'center',
    padding: 30,
  },
  noAppointmentsText: {
    fontSize: 14,
    color: '#999',
    marginTop: 10,
  },
  disclaimer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    margin: 15,
    padding: 15,
    borderRadius: 8,
  },
  disclaimerText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 10,
    flex: 1,
  },
});

export default PatientRecordsNew;
