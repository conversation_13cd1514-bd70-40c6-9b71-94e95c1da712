import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  FlatList,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  Searchbar,
  Chip,
  IconButton,
  List,
  Divider,
  Portal,
  Modal,
  FAB,
  SegmentedButtons,
  DataTable,
  Badge,
  Surface,
  Avatar,
  Menu,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../../lib/supabase';
import { format, formatDistanceToNow, startOfDay, endOfDay, subDays, isToday, isYesterday } from 'date-fns';

interface ActivityLog {
  id: string;
  admin_id: string;
  admin_name?: string;
  admin_email?: string;
  action_type: string;
  target_type?: string;
  target_id?: string;
  description: string;
  metadata?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

interface FilterOptions {
  dateRange: 'today' | 'yesterday' | 'week' | 'month' | 'all';
  actionType: string;
  targetType: string;
  adminId: string;
}

const ACTION_TYPES: Record<string, { label: string; icon: string; color: string }> = {
  login: { label: 'Login', icon: 'login', color: '#4CAF50' },
  logout: { label: 'Logout', icon: 'logout', color: '#FF9800' },
  user_created: { label: 'User Created', icon: 'account-plus', color: '#2196F3' },
  user_updated: { label: 'User Updated', icon: 'account-edit', color: '#9C27B0' },
  user_deleted: { label: 'User Deleted', icon: 'account-remove', color: '#F44336' },
  appointment_created: { label: 'Appointment Created', icon: 'calendar-plus', color: '#00BCD4' },
  appointment_updated: { label: 'Appointment Updated', icon: 'calendar-edit', color: '#FF5722' },
  appointment_cancelled: { label: 'Appointment Cancelled', icon: 'calendar-remove', color: '#E91E63' },
  settings_updated: { label: 'Settings Updated', icon: 'cog', color: '#795548' },
  backup_created: { label: 'Backup Created', icon: 'backup-restore', color: '#607D8B' },
  report_generated: { label: 'Report Generated', icon: 'file-document', color: '#8BC34A' },
  payment_processed: { label: 'Payment Processed', icon: 'cash', color: '#FFC107' },
  notification_sent: { label: 'Notification Sent', icon: 'send', color: '#3F51B5' },
  data_exported: { label: 'Data Exported', icon: 'download', color: '#009688' },
  data_imported: { label: 'Data Imported', icon: 'upload', color: '#CDDC39' },
  password_reset: { label: 'Password Reset', icon: 'lock-reset', color: '#FF6B6B' },
  role_changed: { label: 'Role Changed', icon: 'shield-account', color: '#6C5CE7' },
};

const TARGET_TYPES: Record<string, { label: string; icon: string; color: string }> = {
  user: { label: 'User', icon: 'account', color: '#2196F3' },
  appointment: { label: 'Appointment', icon: 'calendar', color: '#4CAF50' },
  system: { label: 'System', icon: 'cog', color: '#FF9800' },
  payment: { label: 'Payment', icon: 'cash', color: '#9C27B0' },
  notification: { label: 'Notification', icon: 'bell', color: '#00BCD4' },
  report: { label: 'Report', icon: 'file-document', color: '#795548' },
  backup: { label: 'Backup', icon: 'backup-restore', color: '#607D8B' },
};

export const ActivityLogs = () => {
  const theme = useTheme();
  const [logs, setLogs] = useState<ActivityLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLog, setSelectedLog] = useState<ActivityLog | null>(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [exportMenuVisible, setExportMenuVisible] = useState(false);
  
  const [filters, setFilters] = useState<FilterOptions>({
    dateRange: 'today',
    actionType: 'all',
    targetType: 'all',
    adminId: 'all',
  });
  
  const [stats, setStats] = useState({
    totalActions: 0,
    todayActions: 0,
    activeAdmins: 0,
    criticalActions: 0,
  });

  const [admins, setAdmins] = useState<Array<{ id: string; name: string; email: string }>>([]);

  useEffect(() => {
    fetchLogs();
    fetchAdmins();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [logs, searchQuery, filters]);

  useEffect(() => {
    calculateStats();
  }, [filteredLogs]);

  const fetchLogs = async () => {
    setLoading(true);
    try {
      // Calculate date range
      let startDate: Date | null = null;
      const endDate = new Date();
      
      switch (filters.dateRange) {
        case 'today':
          startDate = startOfDay(new Date());
          break;
        case 'yesterday':
          startDate = startOfDay(subDays(new Date(), 1));
          break;
        case 'week':
          startDate = subDays(new Date(), 7);
          break;
        case 'month':
          startDate = subDays(new Date(), 30);
          break;
        default:
          startDate = null;
      }

      let query = supabase
        .from('admin_activity_logs')
        .select(`
          *,
          admin:admin_id (
            id,
            name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (startDate) {
        query = query.gte('created_at', startDate.toISOString());
      }

      const { data, error } = await query;

      if (error) throw error;

      // Map the data with admin info
      const mappedLogs = data?.map(log => ({
        ...log,
        admin_name: log.admin?.name,
        admin_email: log.admin?.email,
      })) || [];

      setLogs(mappedLogs);
    } catch (error) {
      console.error('Error fetching logs:', error);
      Alert.alert('Error', 'Failed to fetch activity logs');
    } finally {
      setLoading(false);
    }
  };

  const fetchAdmins = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email')
        .eq('role', 'admin');

      if (error) throw error;
      setAdmins(data || []);
    } catch (error) {
      console.error('Error fetching admins:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchLogs();
    setRefreshing(false);
  };

  const applyFilters = () => {
    let filtered = [...logs];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(log =>
        log.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.admin_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.admin_email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.action_type.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Action type filter
    if (filters.actionType !== 'all') {
      filtered = filtered.filter(log => log.action_type === filters.actionType);
    }

    // Target type filter
    if (filters.targetType !== 'all') {
      filtered = filtered.filter(log => log.target_type === filters.targetType);
    }

    // Admin filter
    if (filters.adminId !== 'all') {
      filtered = filtered.filter(log => log.admin_id === filters.adminId);
    }

    setFilteredLogs(filtered);
  };

  const calculateStats = () => {
    const today = startOfDay(new Date());
    const todayLogs = filteredLogs.filter(log => 
      new Date(log.created_at) >= today
    );

    const uniqueAdmins = new Set(filteredLogs.map(log => log.admin_id));
    
    const criticalActionTypes = ['user_deleted', 'settings_updated', 'data_exported', 'role_changed'];
    const criticalLogs = filteredLogs.filter(log => 
      criticalActionTypes.includes(log.action_type)
    );

    setStats({
      totalActions: filteredLogs.length,
      todayActions: todayLogs.length,
      activeAdmins: uniqueAdmins.size,
      criticalActions: criticalLogs.length,
    });
  };

  const getActionIcon = (actionType: string) => {
    return ACTION_TYPES[actionType]?.icon || 'alert-circle';
  };

  const getActionColor = (actionType: string) => {
    return ACTION_TYPES[actionType]?.color || '#757575';
  };

  const getActionLabel = (actionType: string) => {
    return ACTION_TYPES[actionType]?.label || actionType.replace(/_/g, ' ').toUpperCase();
  };

  const getTargetIcon = (targetType?: string) => {
    const key = targetType || 'system';
    return TARGET_TYPES[key]?.icon || 'help-circle';
  };

  const getTargetColor = (targetType?: string) => {
    const key = targetType || 'system';
    return TARGET_TYPES[key]?.color || '#757575';
  };

  const formatLogTime = (timestamp: string) => {
    const date = new Date(timestamp);
    
    if (isToday(date)) {
      return `Today, ${format(date, 'h:mm a')}`;
    } else if (isYesterday(date)) {
      return `Yesterday, ${format(date, 'h:mm a')}`;
    } else {
      return format(date, 'MMM d, yyyy h:mm a');
    }
  };

  const exportLogs = async (fmt: 'csv' | 'json') => {
    try {
      let exportData = '';

      if (fmt === 'csv') {
        // CSV Header
        exportData = 'Date,Time,Admin,Action,Target Type,Description\n';

        // CSV Data
        filteredLogs.forEach(log => {
          const date = new Date(log.created_at);
          exportData += `${format(date, 'yyyy-MM-dd')},${format(date, 'HH:mm:ss')},`;
          exportData += `${log.admin_name || 'Unknown'},${log.action_type},`;
          exportData += `${log.target_type || 'N/A'},"${log.description}"\n`;
        });
      } else {
        // JSON format
        exportData = JSON.stringify(filteredLogs, null, 2);
      }

      // In a real implementation, you would save this to a file or send it
      Alert.alert('Export Ready', `${filteredLogs.length} logs ready for export in ${fmt.toUpperCase()} format`);

      // Log the export action
      await logAdminAction('data_exported', `Exported ${filteredLogs.length} activity logs in ${fmt.toUpperCase()} format`);
    } catch (error) {
      Alert.alert('Error', 'Failed to export logs');
    }
  };

  const logAdminAction = async (action: string, description: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase.from('admin_activity_logs').insert({
          admin_id: user.id,
          action_type: action,
          target_type: 'report',
          description,
        });
      }
    } catch (error) {
      console.error('Error logging admin action:', error);
    }
  };

  const clearLogs = async () => {
    Alert.alert(
      'Clear Logs',
      'Are you sure you want to clear all activity logs older than 30 days?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              const thirtyDaysAgo = subDays(new Date(), 30);
              
              const { error } = await supabase
                .from('admin_activity_logs')
                .delete()
                .lt('created_at', thirtyDaysAgo.toISOString());

              if (error) throw error;

              await logAdminAction('data_exported', 'Cleared activity logs older than 30 days');
              
              Alert.alert('Success', 'Old logs cleared successfully');
              fetchLogs();
            } catch (error) {
              Alert.alert('Error', 'Failed to clear logs');
            }
          },
        },
      ]
    );
  };

  const renderLogItem = ({ item }: { item: ActivityLog }) => {
    const actionInfo = ACTION_TYPES[item.action_type];
    const targetInfo = item.target_type ? TARGET_TYPES[item.target_type] : undefined;
    
    return (
      <Card 
        style={styles.logCard}
        onPress={() => {
          setSelectedLog(item);
          setDetailsModalVisible(true);
        }}
      >
        <Card.Content>
          <View style={styles.logHeader}>
            <View style={styles.logIconContainer}>
              <Avatar.Icon
                size={40}
                icon={getActionIcon(item.action_type)}
                style={{ backgroundColor: getActionColor(item.action_type) }}
              />
            </View>
            
            <View style={styles.logContent}>
              <Text style={styles.logTitle}>{getActionLabel(item.action_type)}</Text>
              <Text style={styles.logDescription}>{item.description}</Text>
              
              <View style={styles.logMeta}>
                <Chip
                  style={styles.metaChip}
                  textStyle={styles.metaChipText}
                  icon="account"
                >
                  {item.admin_name || 'Unknown Admin'}
                </Chip>
                
                {item.target_type && (
                  <Chip
                    style={[styles.metaChip, { backgroundColor: getTargetColor(item.target_type) + '20' }]}
                    textStyle={[styles.metaChipText, { color: getTargetColor(item.target_type) }]}
                    icon={getTargetIcon(item.target_type)}
                  >
                    {TARGET_TYPES[item.target_type]?.label || item.target_type}
                  </Chip>
                )}
              </View>
              
              <Text style={styles.logTime}>
                <MaterialCommunityIcons name="clock-outline" size={12} color="#999" />
                {' '}{formatLogTime(item.created_at)}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderDetailsModal = () => {
    if (!selectedLog) return null;
    
    return (
      <Portal>
        <Modal
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Activity Details</Title>
            
            <List.Item
              title="Action Type"
              description={getActionLabel(selectedLog.action_type)}
              left={props => (
                <List.Icon
                  {...props}
                  icon={getActionIcon(selectedLog.action_type)}
                  color={getActionColor(selectedLog.action_type)}
                />
              )}
            />
            
            <Divider />
            
            <List.Item
              title="Description"
              description={selectedLog.description}
              left={props => <List.Icon {...props} icon="text" />}
            />
            
            <Divider />
            
            <List.Item
              title="Admin"
              description={`${selectedLog.admin_name}\n${selectedLog.admin_email}`}
              left={props => <List.Icon {...props} icon="account" />}
            />
            
            <Divider />
            
            <List.Item
              title="Timestamp"
              description={format(new Date(selectedLog.created_at), 'EEEE, MMMM d, yyyy h:mm:ss a')}
              left={props => <List.Icon {...props} icon="clock" />}
            />
            
            {selectedLog.target_type && (
              <>
                <Divider />
                <List.Item
                  title="Target Type"
                  description={TARGET_TYPES[selectedLog.target_type]?.label || selectedLog.target_type}
                  left={props => (
                    <List.Icon
                      {...props}
                      icon={getTargetIcon(selectedLog.target_type)}
                      color={getTargetColor(selectedLog.target_type)}
                    />
                  )}
                />
              </>
            )}
            
            {selectedLog.target_id && (
              <>
                <Divider />
                <List.Item
                  title="Target ID"
                  description={selectedLog.target_id}
                  left={props => <List.Icon {...props} icon="identifier" />}
                />
              </>
            )}
            
            {selectedLog.ip_address && (
              <>
                <Divider />
                <List.Item
                  title="IP Address"
                  description={selectedLog.ip_address}
                  left={props => <List.Icon {...props} icon="ip-network" />}
                />
              </>
            )}
            
            {selectedLog.metadata && Object.keys(selectedLog.metadata).length > 0 && (
              <>
                <Divider />
                <List.Item
                  title="Additional Data"
                  description={JSON.stringify(selectedLog.metadata, null, 2)}
                  left={props => <List.Icon {...props} icon="code-json" />}
                />
              </>
            )}
            
            <Button
              mode="contained"
              onPress={() => setDetailsModalVisible(false)}
              style={styles.modalButton}
            >
              Close
            </Button>
          </ScrollView>
        </Modal>
      </Portal>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={styles.title}>Activity Logs</Title>
        <View style={styles.headerActions}>
          <Menu
            visible={exportMenuVisible}
            onDismiss={() => setExportMenuVisible(false)}
            anchor={
              <IconButton
                icon="download"
                onPress={() => setExportMenuVisible(true)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                setExportMenuVisible(false);
                exportLogs('csv');
              }}
              title="Export as CSV"
              leadingIcon="file-delimited"
            />
            <Menu.Item
              onPress={() => {
                setExportMenuVisible(false);
                exportLogs('json');
              }}
              title="Export as JSON"
              leadingIcon="code-json"
            />
          </Menu>
          
          <IconButton
            icon="delete-sweep"
            onPress={clearLogs}
          />
        </View>
      </View>

      {/* Stats Cards */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.statsContainer}
      >
        <Surface style={[styles.statCard, { backgroundColor: '#E3F2FD' }]} elevation={1}>
          <MaterialCommunityIcons name="sigma" size={24} color="#2196F3" />
          <Text style={styles.statValue}>{stats.totalActions}</Text>
          <Text style={styles.statLabel}>Total Actions</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#E8F5E9' }]} elevation={1}>
          <MaterialCommunityIcons name="calendar-today" size={24} color="#4CAF50" />
          <Text style={styles.statValue}>{stats.todayActions}</Text>
          <Text style={styles.statLabel}>Today</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#FFF3E0' }]} elevation={1}>
          <MaterialCommunityIcons name="account-multiple" size={24} color="#FF9800" />
          <Text style={styles.statValue}>{stats.activeAdmins}</Text>
          <Text style={styles.statLabel}>Active Admins</Text>
        </Surface>
        
        <Surface style={[styles.statCard, { backgroundColor: '#FFEBEE' }]} elevation={1}>
          <MaterialCommunityIcons name="alert" size={24} color="#F44336" />
          <Text style={styles.statValue}>{stats.criticalActions}</Text>
          <Text style={styles.statLabel}>Critical Actions</Text>
        </Surface>
      </ScrollView>

      {/* Filters */}
      <View style={styles.filters}>
        <Searchbar
          placeholder="Search logs..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        {/* Date Range Filter */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          {['today', 'yesterday', 'week', 'month', 'all'].map(range => (
            <Chip
              key={range}
              selected={filters.dateRange === range}
              onPress={() => setFilters({ ...filters, dateRange: range as any })}
              style={styles.filterChip}
            >
              {range.charAt(0).toUpperCase() + range.slice(1)}
            </Chip>
          ))}
        </ScrollView>

        {/* Action Type Filter */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          <Chip
            selected={filters.actionType === 'all'}
            onPress={() => setFilters({ ...filters, actionType: 'all' })}
            style={styles.filterChip}
          >
            All Actions
          </Chip>
          {Object.entries(ACTION_TYPES).map(([key, value]) => (
            <Chip
              key={key}
              selected={filters.actionType === key}
              onPress={() => setFilters({ ...filters, actionType: key })}
              style={styles.filterChip}
              icon={value.icon}
            >
              {value.label}
            </Chip>
          ))}
        </ScrollView>

        {/* Target Type Filter */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterRow}>
          <Chip
            selected={filters.targetType === 'all'}
            onPress={() => setFilters({ ...filters, targetType: 'all' })}
            style={styles.filterChip}
          >
            All Targets
          </Chip>
          {Object.entries(TARGET_TYPES).map(([key, value]) => (
            <Chip
              key={key}
              selected={filters.targetType === key}
              onPress={() => setFilters({ ...filters, targetType: key })}
              style={styles.filterChip}
              icon={value.icon}
            >
              {value.label}
            </Chip>
          ))}
        </ScrollView>
      </View>

      {/* Logs List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </View>
      ) : filteredLogs.length === 0 ? (
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons
            name="file-document-outline"
            size={64}
            color="#ccc"
          />
          <Text style={styles.emptyText}>No activity logs found</Text>
          <Text style={styles.emptySubtext}>
            {searchQuery || filters.dateRange !== 'all' 
              ? 'Try adjusting your filters' 
              : 'Activity logs will appear here'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredLogs}
          renderItem={renderLogItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.logsList}
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Details Modal */}
      {renderDetailsModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
  },
  statsContainer: {
    padding: 16,
    maxHeight: 120,
  },
  statCard: {
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 100,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  filters: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    elevation: 1,
  },
  searchBar: {
    marginBottom: 8,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  filterRow: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  filterChip: {
    marginRight: 8,
  },
  logsList: {
    padding: 16,
  },
  logCard: {
    marginBottom: 12,
    elevation: 2,
  },
  logHeader: {
    flexDirection: 'row',
  },
  logIconContainer: {
    marginRight: 12,
  },
  logContent: {
    flex: 1,
  },
  logTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  logDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  logMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  metaChip: {
    height: 26,
    backgroundColor: '#E3F2FD',
  },
  metaChipText: {
    fontSize: 11,
    color: '#2196F3',
  },
  logTime: {
    fontSize: 12,
    color: '#999',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#999',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#aaa',
    marginTop: 8,
    textAlign: 'center',
  },
  modal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  modalButton: {
    marginTop: 16,
  },
});
