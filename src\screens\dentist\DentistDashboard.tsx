import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  IconButton,
  Avatar,
  List,
  Divider,
  Surface,
  Chip,
  Badge,
  ProgressBar,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { supabase } from '../../../lib/supabase';
import { format, parseISO, isToday, isTomorrow, addDays, startOfDay, endOfDay } from 'date-fns';

interface Appointment {
  id: string;
  patient_id: string;
  patient_name?: string;
  patient_phone?: string;
  date: string;
  start_time: string;
  end_time: string;
  appointment_type: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
}

interface DashboardStats {
  todayAppointments: number;
  completedToday: number;
  pendingToday: number;
  tomorrowAppointments: number;
  weekTotal: number;
  monthTotal: number;
  completionRate: number;
  averagePerDay: number;
}

const APPOINTMENT_TYPES = [
  { value: 'checkup', label: 'Regular Checkup', icon: 'tooth', color: '#4CAF50' },
  { value: 'cleaning', label: 'Teeth Cleaning', icon: 'toothbrush', color: '#2196F3' },
  { value: 'filling', label: 'Filling', icon: 'medical-bag', color: '#FF9800' },
  { value: 'extraction', label: 'Tooth Extraction', icon: 'tooth-off', color: '#F44336' },
  { value: 'root_canal', label: 'Root Canal', icon: 'needle', color: '#9C27B0' },
  { value: 'crown', label: 'Crown', icon: 'crown', color: '#E91E63' },
  { value: 'orthodontics', label: 'Orthodontics', icon: 'format-align-center', color: '#00BCD4' },
  { value: 'consultation', label: 'Consultation', icon: 'chat', color: '#795548' },
  { value: 'emergency', label: 'Emergency', icon: 'alert', color: '#FF5722' },
];

export const DentistDashboard = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [todayAppointments, setTodayAppointments] = useState<Appointment[]>([]);
  const [upcomingAppointments, setUpcomingAppointments] = useState<Appointment[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    todayAppointments: 0,
    completedToday: 0,
    pendingToday: 0,
    tomorrowAppointments: 0,
    weekTotal: 0,
    monthTotal: 0,
    completionRate: 0,
    averagePerDay: 0,
  });
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    if (user) {
      fetchDashboardData();
      // Update current time every minute
      const interval = setInterval(() => setCurrentTime(new Date()), 60000);
      return () => clearInterval(interval);
    }
  }, [user]);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchTodayAppointments(),
        fetchUpcomingAppointments(),
        fetchStats(),
      ]);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const fetchTodayAppointments = async () => {
    if (!user) return;
    
    const today = format(new Date(), 'yyyy-MM-dd');
    const { data, error } = await supabase
      .from('appointments')
      .select(`
        *,
        patient:patient_id(id, name, phone)
      `)
      .eq('dentist_id', user.id)
      .eq('date', today)
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching today appointments:', error);
      return;
    }

    const mappedData = data?.map(apt => ({
      ...apt,
      patient_name: apt.patient?.name,
      patient_phone: apt.patient?.phone,
    })) || [];

    setTodayAppointments(mappedData);
  };

  const fetchUpcomingAppointments = async () => {
    if (!user) return;
    
    const today = format(new Date(), 'yyyy-MM-dd');
    const nextWeek = format(addDays(new Date(), 7), 'yyyy-MM-dd');
    
    const { data, error } = await supabase
      .from('appointments')
      .select(`
        *,
        patient:patient_id(id, name, phone)
      `)
      .eq('dentist_id', user.id)
      .gt('date', today)
      .lte('date', nextWeek)
      .eq('status', 'scheduled')
      .order('date', { ascending: true })
      .order('start_time', { ascending: true })
      .limit(5);

    if (error) {
      console.error('Error fetching upcoming appointments:', error);
      return;
    }

    const mappedData = data?.map(apt => ({
      ...apt,
      patient_name: apt.patient?.name,
      patient_phone: apt.patient?.phone,
    })) || [];

    setUpcomingAppointments(mappedData);
  };

  const fetchStats = async () => {
    if (!user) return;
    
    const today = format(new Date(), 'yyyy-MM-dd');
    const tomorrow = format(addDays(new Date(), 1), 'yyyy-MM-dd');
    const weekStart = format(startOfDay(new Date()), 'yyyy-MM-dd');
    const weekEnd = format(addDays(new Date(), 7), 'yyyy-MM-dd');
    const monthStart = format(new Date(new Date().getFullYear(), new Date().getMonth(), 1), 'yyyy-MM-dd');
    const monthEnd = format(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0), 'yyyy-MM-dd');

    // Today's stats
    const { data: todayData } = await supabase
      .from('appointments')
      .select('status')
      .eq('dentist_id', user.id)
      .eq('date', today);

    const todayCompleted = todayData?.filter(a => a.status === 'completed').length || 0;
    const todayPending = todayData?.filter(a => a.status === 'scheduled').length || 0;

    // Tomorrow's count
    const { count: tomorrowCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('dentist_id', user.id)
      .eq('date', tomorrow);

    // Week total
    const { count: weekCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('dentist_id', user.id)
      .gte('date', weekStart)
      .lte('date', weekEnd);

    // Month total
    const { count: monthCount } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('dentist_id', user.id)
      .gte('date', monthStart)
      .lte('date', monthEnd);

    // Completion rate
    const { data: allAppointments } = await supabase
      .from('appointments')
      .select('status')
      .eq('dentist_id', user.id)
      .gte('date', monthStart);

    const totalCompleted = allAppointments?.filter(a => a.status === 'completed').length || 0;
    const totalAppointments = allAppointments?.length || 0;
    const completionRate = totalAppointments > 0 ? (totalCompleted / totalAppointments) * 100 : 0;

    // Average per day
    const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
    const averagePerDay = monthCount ? monthCount / daysInMonth : 0;

    setStats({
      todayAppointments: todayData?.length || 0,
      completedToday: todayCompleted,
      pendingToday: todayPending,
      tomorrowAppointments: tomorrowCount || 0,
      weekTotal: weekCount || 0,
      monthTotal: monthCount || 0,
      completionRate: Math.round(completionRate),
      averagePerDay: Math.round(averagePerDay * 10) / 10,
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  const handleCompleteAppointment = async (appointmentId: string) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .update({ status: 'completed', updated_at: new Date().toISOString() })
        .eq('id', appointmentId);

      if (error) throw error;

      Alert.alert('Success', 'Appointment marked as completed');
      fetchDashboardData();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update appointment');
    }
  };

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const getAppointmentTypeInfo = (type: string) => {
    return APPOINTMENT_TYPES.find(t => t.value === type) || APPOINTMENT_TYPES[0];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return '#2196F3';
      case 'confirmed': return '#4CAF50';
      case 'completed': return '#9E9E9E';
      case 'cancelled': return '#F44336';
      case 'no-show': return '#FF9800';
      default: return '#757575';
    }
  };

  const renderStatCard = (icon: string, label: string, value: number | string, color: string, progress?: number) => (
    <Surface style={[styles.statCard, { backgroundColor: color + '15' }]} elevation={0}>
      <View style={styles.statContent}>
        <MaterialCommunityIcons name={icon} size={24} color={color} />
        <Text style={[styles.statValue, { color }]}>{value}</Text>
        <Text style={styles.statLabel}>{label}</Text>
        {progress !== undefined && (
          <ProgressBar
            progress={progress}
            color={color}
            style={styles.progressBar}
          />
        )}
      </View>
    </Surface>
  );

  const renderAppointmentCard = (appointment: Appointment, showDate: boolean = false) => {
    const typeInfo = getAppointmentTypeInfo(appointment.appointment_type);
    const isPast = appointment.start_time < format(currentTime, 'HH:mm') && !showDate;
    
    return (
      <Card key={appointment.id} style={[styles.appointmentCard, isPast && styles.pastAppointment]}>
        <Card.Content>
          <View style={styles.appointmentHeader}>
            <View style={styles.appointmentInfo}>
              <View style={styles.timeContainer}>
                <MaterialCommunityIcons name="clock-outline" size={16} color="#666" />
                <Text style={styles.appointmentTime}>
                  {appointment.start_time} - {appointment.end_time}
                </Text>
              </View>
              {showDate && (
                <Text style={styles.appointmentDate}>
                  {format(parseISO(appointment.date), 'EEE, MMM d')}
                </Text>
              )}
            </View>
            <Chip
              mode="flat"
              style={[styles.statusChip, { backgroundColor: getStatusColor(appointment.status) }]}
              textStyle={{ color: '#fff', fontSize: 10 }}
            >
              {appointment.status}
            </Chip>
          </View>
          
          <View style={styles.appointmentDetails}>
            <View style={styles.patientInfo}>
              <Avatar.Text
                size={32}
                label={appointment.patient_name?.substring(0, 2).toUpperCase() || 'PT'}
                color="#fff"
                style={{ backgroundColor: theme.colors.primary }}
              />
              <View style={styles.patientText}>
                <Text style={styles.patientName}>{appointment.patient_name || 'Patient'}</Text>
                <View style={styles.appointmentTypeContainer}>
                  <MaterialCommunityIcons name={typeInfo.icon} size={14} color={typeInfo.color} />
                  <Text style={[styles.appointmentType, { color: typeInfo.color }]}>
                    {typeInfo.label}
                  </Text>
                </View>
              </View>
            </View>
            
            {appointment.status === 'scheduled' && !isPast && (
              <IconButton
                icon="check-circle"
                size={20}
                iconColor="#4CAF50"
                onPress={() => handleCompleteAppointment(appointment.id)}
              />
            )}
          </View>
          
          {appointment.notes && (
            <View style={styles.notesContainer}>
              <MaterialCommunityIcons name="note-text" size={14} color="#999" />
              <Text style={styles.notes} numberOfLines={1}>{appointment.notes}</Text>
            </View>
          )}
        </Card.Content>
      </Card>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>{getGreeting()},</Text>
            <Title style={styles.userName}>Dr. {user?.name || 'Doctor'}</Title>
            <Text style={styles.currentDate}>
              {format(currentTime, 'EEEE, MMMM d, yyyy')}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.scheduleButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate('Schedule')}
          >
            <MaterialCommunityIcons name="calendar-month" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Stats Overview */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.statsContainer}
        >
          {renderStatCard(
            'calendar-today',
            "Today's Total",
            stats.todayAppointments,
            '#2196F3',
            stats.todayAppointments > 0 ? stats.completedToday / stats.todayAppointments : 0
          )}
          {renderStatCard(
            'check-circle',
            'Completed',
            stats.completedToday,
            '#4CAF50'
          )}
          {renderStatCard(
            'clock-alert',
            'Pending',
            stats.pendingToday,
            '#FF9800'
          )}
          {renderStatCard(
            'calendar-range',
            'This Week',
            stats.weekTotal,
            '#9C27B0'
          )}
          {renderStatCard(
            'chart-line',
            'Avg/Day',
            stats.averagePerDay,
            '#00BCD4'
          )}
        </ScrollView>

        {/* Today's Schedule */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Title style={styles.sectionTitle}>Today's Schedule</Title>
              <Badge
                style={{ backgroundColor: theme.colors.primary }}
                size={20}
              >
                {todayAppointments.length}
              </Badge>
            </View>
            
            {todayAppointments.length > 0 ? (
              <View>
                {todayAppointments.map(appointment => renderAppointmentCard(appointment))}
              </View>
            ) : (
              <View style={styles.emptyState}>
                <MaterialCommunityIcons
                  name="calendar-blank"
                  size={48}
                  color="#ccc"
                />
                <Text style={styles.emptyText}>No appointments scheduled for today</Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Quick Actions */}
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Quick Actions</Title>
            <View style={styles.quickActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate('Schedule')}
              >
                <View style={[styles.actionIcon, { backgroundColor: '#E3F2FD' }]}>
                  <MaterialCommunityIcons name="calendar-clock" size={24} color="#2196F3" />
                </View>
                <Text style={styles.actionText}>My Schedule</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate('Patients')}
              >
                <View style={[styles.actionIcon, { backgroundColor: '#E8F5E9' }]}>
                  <MaterialCommunityIcons name="account-group" size={24} color="#4CAF50" />
                </View>
                <Text style={styles.actionText}>Patient Records</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate('Notifications')}
              >
                <View style={[styles.actionIcon, { backgroundColor: '#FFF3E0' }]}>
                  <MaterialCommunityIcons name="bell" size={24} color="#FF9800" />
                </View>
                <Text style={styles.actionText}>Notifications</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => navigation.navigate('Profile')}
              >
                <View style={[styles.actionIcon, { backgroundColor: '#F3E5F5' }]}>
                  <MaterialCommunityIcons name="account" size={24} color="#9C27B0" />
                </View>
                <Text style={styles.actionText}>My Profile</Text>
              </TouchableOpacity>
            </View>
          </Card.Content>
        </Card>

        {/* Upcoming Appointments */}
        {upcomingAppointments.length > 0 && (
          <Card style={[styles.sectionCard, styles.lastCard]}>
            <Card.Content>
              <View style={styles.sectionHeader}>
                <Title style={styles.sectionTitle}>Upcoming Appointments</Title>
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('Schedule')}
                  compact
                >
                  View All
                </Button>
              </View>
              
              <View>
                {upcomingAppointments.map(appointment => renderAppointmentCard(appointment, true))}
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Tomorrow's Preview */}
        {stats.tomorrowAppointments > 0 && (
          <Card style={[styles.sectionCard, styles.lastCard]}>
            <Card.Content>
              <View style={styles.tomorrowPreview}>
                <MaterialCommunityIcons name="calendar-arrow-right" size={24} color={theme.colors.primary} />
                <View style={styles.tomorrowText}>
                  <Text style={styles.tomorrowLabel}>Tomorrow</Text>
                  <Text style={styles.tomorrowCount}>
                    {stats.tomorrowAppointments} appointment{stats.tomorrowAppointments !== 1 ? 's' : ''}
                  </Text>
                </View>
                <Button
                  mode="text"
                  onPress={() => navigation.navigate('Schedule')}
                  compact
                >
                  View
                </Button>
              </View>
            </Card.Content>
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    marginBottom: 8,
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    color: '#666',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  currentDate: {
    fontSize: 12,
    color: '#999',
  },
  scheduleButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
    maxHeight: 120,
  },
  statCard: {
    padding: 12,
    marginRight: 12,
    borderRadius: 12,
    minWidth: 100,
    borderWidth: 0,
    borderColor: 'transparent',
    elevation: 0,
    shadowColor: 'transparent',
  },
  statContent: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 11,
    color: '#666',
    marginTop: 2,
  },
  progressBar: {
    width: '100%',
    height: 4,
    marginTop: 8,
    borderRadius: 2,
  },
  sectionCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  appointmentCard: {
    marginBottom: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    elevation: 1,
  },
  pastAppointment: {
    opacity: 0.6,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  appointmentInfo: {
    flex: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appointmentTime: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
    color: '#333',
  },
  appointmentDate: {
    fontSize: 12,
    color: '#555',
    marginTop: 2,
  },
  statusChip: {
    height: 24,
  },
  appointmentDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  patientText: {
    marginLeft: 12,
    flex: 1,
  },
  patientName: {
    fontSize: 14,
    fontWeight: '500',
  },
  appointmentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  appointmentType: {
    fontSize: 12,
    marginLeft: 4,
  },
  notesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  notes: {
    fontSize: 12,
    color: '#555',
    marginLeft: 4,
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    marginTop: 12,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    color: '#333',
  },
  tomorrowPreview: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tomorrowText: {
    flex: 1,
    marginLeft: 12,
  },
  tomorrowLabel: {
    fontSize: 14,
    color: '#666',
  },
  tomorrowCount: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  lastCard: {
    marginBottom: 24,
  },
});
