import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  DataTable,
  Searchbar,
  Avatar,
  IconButton,
  FAB,
  Portal,
  Modal,
  TextInput,
  RadioButton,
  Chip,
  Badge,
  Menu,
  Divider,
  List,
  Switch,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../../lib/supabase';
import { UserRole, UserStatus } from '../../types';
import { format } from 'date-fns';

interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: string;
  role: UserRole;
  status: UserStatus;
  created_at: string;
  last_login?: string;
}

export const UserManagement = () => {
  const theme = useTheme();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [menuVisible, setMenuVisible] = useState<{ [key: string]: boolean }>({});
  
  // Form states for new/edit user
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    password: '',
    role: UserRole.PATIENT,
    status: UserStatus.ACTIVE,
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchQuery, selectedRole, selectedStatus]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Error', 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchUsers();
    setRefreshing(false);
  };

  const filterUsers = () => {
    let filtered = [...users];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.phone?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Role filter
    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.role === selectedRole);
    }

    // Status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(user => user.status === selectedStatus);
    }

    setFilteredUsers(filtered);
  };

  const handleCreateUser = async () => {
    if (!formData.name || !formData.email) {
      Alert.alert('Error', 'Name and email are required');
      return;
    }

    setLoading(true);
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: formData.email,
        password: formData.password || 'TempPassword123!',
        email_confirm: true,
        user_metadata: {
          name: formData.name,
          role: formData.role,
        },
      });

      if (authError) throw authError;

      // Create user profile
      const { error: profileError } = await supabase.from('users').insert({
        id: authData.user.id,
        email: formData.email,
        name: formData.name,
        phone: formData.phone,
        address: formData.address,
        role: formData.role,
        status: formData.status,
      });

      if (profileError) throw profileError;

      Alert.alert('Success', 'User created successfully');
      setModalVisible(false);
      resetForm();
      fetchUsers();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to create user');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async () => {
    if (!selectedUser) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('users')
        .update({
          name: formData.name,
          phone: formData.phone,
          address: formData.address,
          role: formData.role,
          status: formData.status,
        })
        .eq('id', selectedUser.id);

      if (error) throw error;

      // Log admin action
      await logAdminAction('user_updated', selectedUser.id, `Updated user: ${formData.name}`);

      Alert.alert('Success', 'User updated successfully');
      setEditModalVisible(false);
      resetForm();
      fetchUsers();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update user');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = (user: User) => {
    Alert.alert(
      'Delete User',
      `Are you sure you want to delete ${user.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('users')
                .delete()
                .eq('id', user.id);

              if (error) throw error;

              // Log admin action
              await logAdminAction('user_deleted', user.id, `Deleted user: ${user.name}`);

              Alert.alert('Success', 'User deleted successfully');
              fetchUsers();
            } catch (error: any) {
              Alert.alert('Error', error.message || 'Failed to delete user');
            }
          },
        },
      ]
    );
  };

  const handleResetPassword = async (user: User) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(user.email);
      if (error) throw error;

      Alert.alert('Success', `Password reset email sent to ${user.email}`);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to send reset email');
    }
  };

  const handleStatusToggle = async (user: User) => {
    const newStatus = user.status === UserStatus.ACTIVE ? UserStatus.INACTIVE : UserStatus.ACTIVE;
    
    try {
      const { error } = await supabase
        .from('users')
        .update({ status: newStatus })
        .eq('id', user.id);

      if (error) throw error;

      // Log admin action
      await logAdminAction('user_updated', user.id, `Changed user status to ${newStatus}`);

      fetchUsers();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update status');
    }
  };

  const logAdminAction = async (action: string, targetId: string, description: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase.from('admin_activity_logs').insert({
          admin_id: user.id,
          action_type: action,
          target_id: targetId,
          target_type: 'user',
          description,
        });
      }
    } catch (error) {
      console.error('Error logging admin action:', error);
    }
  };

  const openEditModal = (user: User) => {
    setSelectedUser(user);
    setFormData({
      name: user.name,
      email: user.email,
      phone: user.phone || '',
      address: user.address || '',
      password: '',
      role: user.role,
      status: user.status,
    });
    setEditModalVisible(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      address: '',
      password: '',
      role: UserRole.PATIENT,
      status: UserStatus.ACTIVE,
    });
    setSelectedUser(null);
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return '#F44336';
      case UserRole.DENTIST:
        return '#4CAF50';
      case UserRole.STAFF:
        return '#2196F3';
      case UserRole.PATIENT:
        return '#9C27B0';
      default:
        return '#757575';
    }
  };

  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case UserStatus.ACTIVE:
        return '#4CAF50';
      case UserStatus.INACTIVE:
        return '#FF9800';
      case UserStatus.SUSPENDED:
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const renderUserCard = (user: User) => (
    <Card key={user.id} style={styles.userCard}>
      <Card.Content>
        <View style={styles.userHeader}>
          <View style={styles.userInfo}>
            <Avatar.Text
              size={48}
              label={user.name.substring(0, 2).toUpperCase()}
              style={{ backgroundColor: getRoleColor(user.role) }}
            />
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user.name}</Text>
              <Text style={styles.userEmail}>{user.email}</Text>
              {user.phone && <Text style={styles.userPhone}>📞 {user.phone}</Text>}
            </View>
          </View>
          <Menu
            visible={menuVisible[user.id] || false}
            onDismiss={() => setMenuVisible({ ...menuVisible, [user.id]: false })}
            anchor={
              <IconButton
                icon="dots-vertical"
                onPress={() => setMenuVisible({ ...menuVisible, [user.id]: true })}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                setMenuVisible({ ...menuVisible, [user.id]: false });
                openEditModal(user);
              }}
              title="Edit"
              leadingIcon="pencil"
            />
            <Menu.Item
              onPress={() => {
                setMenuVisible({ ...menuVisible, [user.id]: false });
                handleResetPassword(user);
              }}
              title="Reset Password"
              leadingIcon="lock-reset"
            />
            <Divider />
            <Menu.Item
              onPress={() => {
                setMenuVisible({ ...menuVisible, [user.id]: false });
                handleDeleteUser(user);
              }}
              title="Delete"
              titleStyle={{ color: theme.colors.error }}
              leadingIcon="delete"
            />
          </Menu>
        </View>

        <View style={styles.userMeta}>
          <Chip
            style={[styles.roleChip, { backgroundColor: getRoleColor(user.role) + '20' }]}
            textStyle={{ color: getRoleColor(user.role), fontSize: 12 }}
          >
            {user.role.toUpperCase()}
          </Chip>
          <Chip
            style={[styles.statusChip, { backgroundColor: getStatusColor(user.status) + '20' }]}
            textStyle={{ color: getStatusColor(user.status), fontSize: 12 }}
          >
            {user.status}
          </Chip>
          <Switch
            value={user.status === UserStatus.ACTIVE}
            onValueChange={() => handleStatusToggle(user)}
            color={theme.colors.primary}
          />
        </View>

        <Divider style={styles.divider} />

        <View style={styles.userTimestamps}>
          <Text style={styles.timestamp}>
            Created: {format(new Date(user.created_at), 'MMM d, yyyy')}
          </Text>
          {user.last_login && (
            <Text style={styles.timestamp}>
              Last login: {format(new Date(user.last_login), 'MMM d, yyyy h:mm a')}
            </Text>
          )}
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Title style={styles.title}>User Management</Title>
          <View style={styles.stats}>
            <Badge style={styles.statBadge}>{`${users.length} Total Users`}</Badge>
            <Badge style={[styles.statBadge, { backgroundColor: '#4CAF50' }]}>
              {`${users.filter(u => u.status === UserStatus.ACTIVE).length} Active`}
            </Badge>
          </View>
        </View>

        {/* Search and Filters */}
        <View style={styles.filters}>
          <Searchbar
            placeholder="Search users..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterChips}>
            <Chip
              selected={selectedRole === 'all'}
              onPress={() => setSelectedRole('all')}
              style={styles.filterChip}
            >
              All Roles
            </Chip>
            {Object.values(UserRole).map(role => (
              <Chip
                key={role}
                selected={selectedRole === role}
                onPress={() => setSelectedRole(role)}
                style={styles.filterChip}
              >
                {role}
              </Chip>
            ))}
          </ScrollView>

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterChips}>
            <Chip
              selected={selectedStatus === 'all'}
              onPress={() => setSelectedStatus('all')}
              style={styles.filterChip}
            >
              All Status
            </Chip>
            {Object.values(UserStatus).map(status => (
              <Chip
                key={status}
                selected={selectedStatus === status}
                onPress={() => setSelectedStatus(status)}
                style={styles.filterChip}
              >
                {status}
              </Chip>
            ))}
          </ScrollView>
        </View>

        {/* User List */}
        <View style={styles.userList}>
          {filteredUsers.map(renderUserCard)}
        </View>
      </ScrollView>

      {/* FAB for adding new user */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => {
          resetForm();
          setModalVisible(true);
        }}
        label="Add User"
      />

      {/* Add User Modal */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Add New User</Title>
            
            <TextInput
              label="Full Name *"
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
            />

            <TextInput
              label="Password (optional)"
              value={formData.password}
              onChangeText={(text) => setFormData({ ...formData, password: text })}
              mode="outlined"
              secureTextEntry
              style={styles.input}
              placeholder="Leave empty for auto-generated"
            />

            <TextInput
              label="Phone"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              mode="outlined"
              keyboardType="phone-pad"
              style={styles.input}
            />

            <TextInput
              label="Address"
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.input}
            />

            <Text style={styles.fieldLabel}>Role</Text>
            <RadioButton.Group
              onValueChange={(value) => setFormData({ ...formData, role: value as UserRole })}
              value={formData.role}
            >
              {Object.values(UserRole).map(role => (
                <RadioButton.Item key={role} label={role} value={role} />
              ))}
            </RadioButton.Group>

            <Text style={styles.fieldLabel}>Status</Text>
            <RadioButton.Group
              onValueChange={(value) => setFormData({ ...formData, status: value as UserStatus })}
              value={formData.status}
            >
              {Object.values(UserStatus).map(status => (
                <RadioButton.Item key={status} label={status} value={status} />
              ))}
            </RadioButton.Group>

            <View style={styles.modalActions}>
              <Button mode="outlined" onPress={() => setModalVisible(false)}>
                Cancel
              </Button>
              <Button mode="contained" onPress={handleCreateUser} loading={loading}>
                Create User
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>

      {/* Edit User Modal */}
      <Portal>
        <Modal
          visible={editModalVisible}
          onDismiss={() => setEditModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Edit User</Title>
            
            <TextInput
              label="Full Name *"
              value={formData.name}
              onChangeText={(text) => setFormData({ ...formData, name: text })}
              mode="outlined"
              style={styles.input}
            />

            <TextInput
              label="Email"
              value={formData.email}
              mode="outlined"
              disabled
              style={styles.input}
            />

            <TextInput
              label="Phone"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              mode="outlined"
              keyboardType="phone-pad"
              style={styles.input}
            />

            <TextInput
              label="Address"
              value={formData.address}
              onChangeText={(text) => setFormData({ ...formData, address: text })}
              mode="outlined"
              multiline
              numberOfLines={2}
              style={styles.input}
            />

            <Text style={styles.fieldLabel}>Role</Text>
            <RadioButton.Group
              onValueChange={(value) => setFormData({ ...formData, role: value as UserRole })}
              value={formData.role}
            >
              {Object.values(UserRole).map(role => (
                <RadioButton.Item key={role} label={role} value={role} />
              ))}
            </RadioButton.Group>

            <Text style={styles.fieldLabel}>Status</Text>
            <RadioButton.Group
              onValueChange={(value) => setFormData({ ...formData, status: value as UserStatus })}
              value={formData.status}
            >
              {Object.values(UserStatus).map(status => (
                <RadioButton.Item key={status} label={status} value={status} />
              ))}
            </RadioButton.Group>

            <View style={styles.modalActions}>
              <Button mode="outlined" onPress={() => setEditModalVisible(false)}>
                Cancel
              </Button>
              <Button mode="contained" onPress={handleUpdateUser} loading={loading}>
                Update User
              </Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  stats: {
    flexDirection: 'row',
    gap: 8,
  },
  statBadge: {
    backgroundColor: '#2196F3',
  },
  filters: {
    padding: 16,
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  searchBar: {
    marginBottom: 12,
    elevation: 0,
    backgroundColor: '#f5f5f5',
  },
  filterChips: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  userList: {
    padding: 16,
  },
  userCard: {
    marginBottom: 12,
    elevation: 2,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  userInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  userDetails: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  userPhone: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    gap: 8,
  },
  roleChip: {
    height: 28,
  },
  statusChip: {
    height: 28,
  },
  divider: {
    marginVertical: 12,
  },
  userTimestamps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timestamp: {
    fontSize: 11,
    color: '#999',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    marginTop: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
});
