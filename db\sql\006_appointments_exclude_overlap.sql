-- 006_appointments_exclude_overlap.sql
-- Purpose: Enforce server-side prevention of overlapping appointments per dentist
-- Uses exclusion constraint on dentist_id + time range

BEGIN;

-- Required for equality support in GiST indexes
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- Add exclusion constraint (idempotent)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint
    WHERE conname = 'appointments_no_overlap_per_dentist'
  ) THEN
    ALTER TABLE public.appointments
      ADD CONSTRAINT appointments_no_overlap_per_dentist
      EXCLUDE USING gist (
        dentist_id WITH =,
        effective_appt_range WITH &&
      );
  END IF;
END $$;

COMMIT;

