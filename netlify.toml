# Netlify config for Vite + React SPA located in webadmin/
# Monorepo-style: build runs in the webadmin folder

[build]
  base = "webadmin"
  command = "npm run build"
  publish = "dist"

[build.environment]
  # Use a modern Node runtime compatible with Vite
  NODE_VERSION = "18.20.3"

# SPA fallback: route all paths to index.html so React Router can handle them
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

