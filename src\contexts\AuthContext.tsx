import React, { createContext, useContext, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { Session } from '@supabase/supabase-js';
import { supabase } from '../../lib/supabase';
import { User, UserRole, AuthContextType } from '../types';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session?.user) {
        fetchUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      if (session?.user) {
        fetchUserProfile(session.user.id);
      } else {
        setUser(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching user profile:', error);
        // Try to create the profile if it doesn't exist
        const session = await supabase.auth.getSession();
        if (session.data.session?.user) {
          const { data: newUser, error: createError } = await supabase
            .from('users')
            .insert({
              id: userId,
              email: session.data.session.user.email,
              name: session.data.session.user.user_metadata?.name || session.data.session.user.email?.split('@')[0] || 'User',
              role: session.data.session.user.user_metadata?.role || 'patient',
            })
            .select()
            .maybeSingle();
          
          if (!createError && newUser) {
            setUser(newUser as User);
          } else {
            console.error('Failed to create user profile:', createError);
          }
        }
      } else if (data) {
        setUser(data as User);
      }
    } catch (error: any) {
      console.error('Error in fetchUserProfile:', error.message);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Ensure session is established and profile is loaded before finishing (prevents login flicker)
      const { data: sess } = await supabase.auth.getSession();
      const uid = data.user?.id ?? sess.session?.user?.id;
      if (uid) {
        await fetchUserProfile(uid);
        // Update last login after profile exists
        await supabase
          .from('users')
          .update({ last_login: new Date().toISOString() })
          .eq('id', uid);
      }
    } catch (error: any) {
      console.error('Sign in error:', error.message);
      Alert.alert('Sign In Error', error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (
    email: string,
    password: string,
    name: string,
    role: UserRole = UserRole.PATIENT
  ) => {
    try {
      setLoading(true);
      console.log('Attempting signup for:', email, 'with role:', role);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            role,
          },
        },
      });

      console.log('Signup response:', { data, error });

      if (error) {
        console.error('Signup error details:', error);
        throw error;
      }

      // Manual user profile creation if trigger fails
      if (data.user && !error) {
        try {
          // First check if user profile already exists
          const { data: existingUser, error: checkError } = await supabase
            .from('users')
            .select('id')
            .eq('id', data.user.id)
            .maybeSingle();

          if (!existingUser && !checkError) {
            // Only insert if user doesn't exist
            const { error: profileError } = await supabase
              .from('users')
              .insert({
                id: data.user.id,
                email: data.user.email || email,
                name: name,
                role: role,
              });
            
            if (profileError) {
              console.log('Profile creation error:', profileError);
              // Don't throw here, as the auth user is already created
            }
          } else {
            console.log('User profile already exists or check failed:', checkError);
          }
        } catch (profileErr) {
          console.log('Manual profile creation failed:', profileErr);
        }
      }

      if (data.user && !data.user.email_confirmed_at) {
        Alert.alert(
          'Check Your Email',
          'We sent you a confirmation email. Please click the link in the email to activate your account.'
        );
      } else if (data.user && data.user.email_confirmed_at) {
        Alert.alert(
          'Success',
          'Account created successfully! You can now sign in.'
        );
      } else {
        Alert.alert(
          'Success',
          'Account created successfully! Please check your email to verify your account.'
        );
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      const errorMessage = error.message || 'An error occurred during signup';
      Alert.alert('Sign Up Error', errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setUser(null);
      setSession(null);
    } catch (error: any) {
      console.error('Sign out error:', error.message);
      Alert.alert('Sign Out Error', error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'drshilpas://reset-password',
      });

      if (error) throw error;

      Alert.alert(
        'Password Reset',
        'Password reset instructions have been sent to your email.'
      );
    } catch (error: any) {
      console.error('Password reset error:', error.message);
      Alert.alert('Password Reset Error', error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      if (!user) throw new Error('No user logged in');

      setLoading(true);
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;

      setUser(data as User);
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error: any) {
      console.error('Profile update error:', error.message);
      Alert.alert('Profile Update Error', error.message);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
