-- 004_fn_is_web_user.sql
-- Purpose: Helper function to check if a caller (by uid) is a web-allowed user
--          without causing RLS recursion on public.users.
-- Returns true if uid has role in ('admin','staff','doctor','dentist','receptionist').

BEGIN;

CREATE OR REPLACE FUNCTION public.is_web_user(uid uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = uid
      AND u.role IN ('admin','staff','doctor','dentist','receptionist')
  );
$$;

-- Restrict default access and grant to authenticated users
REVOKE ALL ON FUNCTION public.is_web_user(uuid) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION public.is_web_user(uuid) TO authenticated;

COMMIT;

