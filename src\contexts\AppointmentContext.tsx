import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { supabase } from '../../lib/supabase';
import { useAuth } from './AuthContext';
import {
  Appointment,
  AppointmentStatus,
  TimeSlot,
  AppointmentContextType,
  AppointmentFilters,
} from '../types';
import { format, addDays, parse, setHours, setMinutes } from 'date-fns';
import {
  TIME_SLOTS,
  SLOT_DURATION_MINUTES,
  CLINIC_HOURS,
  isClinicWorkingDay,
  calculateEndTime,
} from '../constants/appointmentConfig';

const AppointmentContext = createContext<AppointmentContextType | undefined>(undefined);

export const useAppointments = () => {
  const context = useContext(AppointmentContext);
  if (!context) {
    throw new Error('useAppointments must be used within an AppointmentProvider');
  }
  return context;
};

interface AppointmentProviderProps {
  children: React.ReactNode;
}

export const AppointmentProvider: React.FC<AppointmentProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchAppointments();
    }
  }, [user]);

  const fetchAppointments = async (filters?: AppointmentFilters) => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from('appointments')
        .select(`
          *,
          patient:patient_id(id, name, email, phone),
          dentist:dentist_id(id, name),
          staff:staff_id(id, name)
        `);

      // Apply filters based on user role
      if (user?.role === 'patient') {
        query = query.eq('patient_id', user.id);
      } else if (user?.role === 'dentist') {
        query = query.eq('dentist_id', user.id);
      }

      // Apply additional filters
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.dentistId) {
        query = query.eq('dentist_id', filters.dentistId);
      }
      if (filters?.patientId) {
        query = query.eq('patient_id', filters.patientId);
      }
      if (filters?.dateFrom) {
        query = query.gte('date', filters.dateFrom);
      }
      if (filters?.dateTo) {
        query = query.lte('date', filters.dateTo);
      }

      const { data, error: fetchError } = await query
        .order('date', { ascending: true })
        .order('start_time', { ascending: true });

      if (fetchError) throw fetchError;

      let rows: any[] = data || [];

      // Auto-complete past appointments (default): if end time has passed and status is scheduled/confirmed
      try {
        if (user?.role && user.role !== 'patient') {
          const now = new Date();
          const toComplete = rows.filter((a: any) => {
            const status = (a.status || '').toLowerCase();
            if (!(status === 'scheduled' || status === 'confirmed')) return false;

            const dateStr: string | undefined = a.appointment_date || a.date;
            const startStr: string | undefined = a.appointment_time || a.start_time;
            const endStr: string | undefined = a.end_time;
            const dur: number = a.duration_minutes || SLOT_DURATION_MINUTES;
            if (!dateStr || !startStr) return false;

            const startHM = startStr.toString().slice(0, 5); // HH:mm
            const endHM = endStr ? endStr.toString().slice(0, 5) : undefined;

            const base = new Date(dateStr + 'T00:00:00');
            const [sh, sm] = startHM.split(':').map((n: string) => parseInt(n, 10));
            const startDT = new Date(base);
            startDT.setHours(sh || 0, sm || 0, 0, 0);

            let endDT: Date;
            if (endHM) {
              const [eh, em] = endHM.split(':').map((n: string) => parseInt(n, 10));
              endDT = new Date(base);
              endDT.setHours(eh || 0, em || 0, 0, 0);
            } else {
              endDT = new Date(startDT.getTime() + dur * 60000);
            }

            return now >= endDT;
          });

          if (toComplete.length) {
            // Update in DB (best-effort)
            await supabase
              .from('appointments')
              .update({ status: AppointmentStatus.COMPLETED, updated_at: new Date().toISOString() })
              .in('id', toComplete.map((a: any) => a.id));

            // Update locally
            const setIds = new Set(toComplete.map((a: any) => a.id));
            rows = rows.map(r => (setIds.has(r.id) ? { ...r, status: AppointmentStatus.COMPLETED } : r));
          }
        }
      } catch (autoErr) {
        // Non-fatal; continue rendering with fetched data
        console.log('Auto-complete past appointments skipped:', (autoErr as any)?.message || autoErr);
      }

      setAppointments(rows);
    } catch (err: any) {
      console.error('Error fetching appointments:', err.message);
      setError(err.message);
      Alert.alert('Error', 'Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const createAppointment = async (appointment: Partial<Appointment>) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: createError } = await supabase
        .from('appointments')
        .insert([{
          patient_id: appointment.patient_id || user?.id,
          dentist_id: appointment.dentist_id,
          date: appointment.date,
          start_time: appointment.start_time,
          end_time: appointment.end_time || calculateEndTime(appointment.start_time!),
          appointment_type: appointment.appointment_type || 'checkup',
          notes: appointment.notes,
          status: appointment.status || AppointmentStatus.SCHEDULED,
          followup_id: appointment.followup_id,
        }])
        .select()
        .single();

      if (createError) throw createError;

      setAppointments(prev => [...prev, data]);
      Alert.alert('Success', 'Appointment booked successfully!');
    } catch (err: any) {
      const msg: string = err?.message || '';
      const code: string | undefined = err?.code;
      console.error('Error creating appointment:', code, msg);
      setError(msg);
      if (code === '23P01' || code === '23505' || /no_overlap|Time slot is not available|overlap/i.test(msg)) {
        Alert.alert('Slot unavailable', 'The selected time slot has just been booked. Please choose another time.');
      } else {
        Alert.alert('Error', 'Failed to book appointment');
      }
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateAppointment = async (id: string, updates: Partial<Appointment>) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: updateError } = await supabase
        .from('appointments')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (updateError) throw updateError;

      setAppointments(prev =>
        prev.map(apt => (apt.id === id ? { ...apt, ...data } : apt))
      );
      Alert.alert('Success', 'Appointment updated successfully!');
    } catch (err: any) {
      console.error('Error updating appointment:', err.message);
      setError(err.message);
      Alert.alert('Error', 'Failed to update appointment');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const cancelAppointment = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error: cancelError } = await supabase
        .from('appointments')
        .update({ status: AppointmentStatus.CANCELLED })
        .eq('id', id);

      if (cancelError) throw cancelError;

      setAppointments(prev =>
        prev.map(apt =>
          apt.id === id ? { ...apt, status: AppointmentStatus.CANCELLED } : apt
        )
      );
      Alert.alert('Success', 'Appointment cancelled successfully!');
    } catch (err: any) {
      console.error('Error cancelling appointment:', err.message);
      setError(err.message);
      Alert.alert('Error', 'Failed to cancel appointment');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getAvailableSlots = async (date: string, dentistId?: string): Promise<TimeSlot[]> => {
    try {
      setLoading(true);
      
      // Check if the date is a working day
      const selectedDate = new Date(date);
      if (!isClinicWorkingDay(selectedDate)) {
        return []; // No slots available on Sundays
      }

      // Use configuration for working hours and slot duration
      const workingHours = {
        start: `${CLINIC_HOURS.START_HOUR.toString().padStart(2, '0')}:00`,
        end: `${CLINIC_HOURS.END_HOUR.toString().padStart(2, '0')}:00`,
      };
      const slotDuration = SLOT_DURATION_MINUTES;

      // Try Edge Function first (bypasses RLS safely) to get booked slots
      let edgeSlots: Array<{ start: string; end: string }> | null = null;
      if (dentistId) {
        try {
          const { data: fnData } = await supabase.functions.invoke('get-availability', {
            body: { date, dentist_id: dentistId },
          });
          if (fnData && Array.isArray((fnData as any).slots) && (fnData as any).slots.length) {
            edgeSlots = (fnData as any).slots as Array<{ start: string; end: string }>;
          }
        } catch (_) {
          // ignore and fall back to direct query
        }
      }

      // Fetch existing appointments for the date (fallback if edge function unavailable)
      let existingAppointments: any[] = [];
      if (edgeSlots) {
        existingAppointments = edgeSlots.map(s => ({ start_time: s.start, end_time: s.end, duration_minutes: slotDuration }));
      } else {
        // Support both schemas: (date,start_time,end_time) and (appointment_date,appointment_time)
        let query = supabase
          .from('appointments')
          .select('start_time, end_time, appointment_time, appointment_date, duration_minutes')
          .or(`date.eq.${date},appointment_date.eq.${date}`)
          .neq('status', AppointmentStatus.CANCELLED);
        if (dentistId) {
          query = query.eq('dentist_id', dentistId);
        }
        const { data } = await query;
        existingAppointments = data || [];
      }

      // Generate time slots
      const slots: TimeSlot[] = [];
      const startTime = parse(workingHours.start, 'HH:mm', new Date());
      const endTime = parse(workingHours.end, 'HH:mm', new Date());
      
      let currentSlot = startTime;
      
      while (currentSlot < endTime) {
        const slotEndTime = new Date(currentSlot.getTime() + slotDuration * 60000);
        
        // Check if slot is available (robust to HH:mm:ss strings)
        const cs = currentSlot.getHours() * 60 + currentSlot.getMinutes();
        const se = slotEndTime.getHours() * 60 + slotEndTime.getMinutes();

        const isAvailable = !existingAppointments?.some(apt => {
          const rawStart: string | undefined = (apt as any).start_time || (apt as any).appointment_time;
          if (!rawStart) return true; // missing data -> be conservative and block only when certain
          const startHM = rawStart.toString().slice(0, 5); // HH:mm
          const rawEnd: string | undefined = (apt as any).end_time || undefined;
          const dur = (apt as any).duration_minutes || slotDuration;

          const [sh, sm] = startHM.split(':').map((n: string) => parseInt(n, 10));
          const aptStartMin = (sh || 0) * 60 + (sm || 0);
          const aptEndMin = rawEnd
            ? (() => { const [eh, em] = rawEnd.toString().slice(0,5).split(':').map((n: string) => parseInt(n,10)); return (eh||0)*60 + (em||0); })()
            : aptStartMin + dur;

          // Overlap on [start, end)
          return (cs < aptEndMin) && (se > aptStartMin);
        });

        slots.push({
          date,
          startTime: format(currentSlot, 'HH:mm'),
          endTime: format(slotEndTime, 'HH:mm'),
          available: isAvailable,
          dentistId,
        });

        currentSlot = slotEndTime;
      }

      return slots;
    } catch (err: any) {
      console.error('Error fetching available slots:', err.message);
      setError(err.message);
      Alert.alert('Error', 'Failed to fetch available slots');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Set up real-time subscription
  useEffect(() => {
    if (!user) return;

    const subscription = supabase
      .channel('appointments_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'appointments',
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setAppointments(prev => [...prev, payload.new as Appointment]);
          } else if (payload.eventType === 'UPDATE') {
            setAppointments(prev =>
              prev.map(apt =>
                apt.id === payload.new.id ? (payload.new as Appointment) : apt
              )
            );
          } else if (payload.eventType === 'DELETE') {
            setAppointments(prev =>
              prev.filter(apt => apt.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user]);

  const value: AppointmentContextType = {
    appointments,
    loading,
    error,
    fetchAppointments,
    createAppointment,
    updateAppointment,
    cancelAppointment,
    getAvailableSlots,
  };

  return (
    <AppointmentContext.Provider value={value}>
      {children}
    </AppointmentContext.Provider>
  );
};
