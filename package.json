{"name": "drs<PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/drawer": "^7.5.8", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.57.4", "base-64": "^1.0.0", "date-fns": "^4.1.0", "expo": "^54.0.0", "expo-device": "~8.0.6", "expo-image-picker": "~17.0.7", "expo-notifications": "~0.32.10", "expo-status-bar": "~3.0.7", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-calendars": "^1.1313.0", "react-native-chart-kit": "^6.12.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.28.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "^18.2.66", "supabase": "^2.40.7", "typescript": "^5.4.5"}, "private": true}