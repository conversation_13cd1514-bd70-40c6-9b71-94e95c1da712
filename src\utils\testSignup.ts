import { supabase } from '../../lib/supabase';

export const testSignup = async () => {
  try {
    console.log('🔍 Testing Supabase signup...');
    
    // Test with a simple signup
    const testEmail = 'test' + Date.now() + '@example.com';
    const testPassword = 'TestPassword123!';
    
    console.log('📧 Test email:', testEmail);
    console.log('🔒 Test password:', testPassword);
    
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
    });
    
    console.log('📊 Signup result:', { data, error });
    
    if (error) {
      console.error('❌ Signup failed:', error);
      return { success: false, error: error.message };
    }
    
    if (data.user) {
      console.log('✅ User created:', data.user.id);
      return { success: true, user: data.user };
    }
    
    return { success: false, error: 'Unknown error' };
    
  } catch (err: any) {
    console.error('💥 Exception:', err);
    return { success: false, error: err.message };
  }
};
