import * as React from 'react';
import { useEffect } from 'react';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { AuthProvider } from './src/contexts/AuthContext';
import { AppointmentProvider } from './src/contexts/AppointmentContext';
import { AppNavigator } from './src/navigation/AppNavigator';
import NotificationService from './src/services/NotificationService';
import theme from './src/theme';

export default function App() {
  useEffect(() => {
    // Initialize notification service (non-blocking)
    NotificationService.initialize().catch(error => {
      // Silently handle notification initialization errors
      console.log('Notification service initialization skipped:', error?.message);
    });
    
    return () => {
      // Cleanup notification listeners
      NotificationService.cleanup();
    };
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <PaperProvider theme={theme}>
          <AuthProvider>
            <AppointmentProvider>
              <AppNavigator />
            </AppointmentProvider>
          </AuthProvider>
        </PaperProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
