-- Check for duplicate users
SELECT id, email, created_at, COUNT(*) 
FROM public.users 
GROUP BY id, email, created_at
ORDER BY created_at DESC;

-- See all users with the same email
SELECT * FROM public.users 
WHERE email = '<EMAIL>'
ORDER BY created_at DESC;

-- Delete duplicate entries, keeping only the most recent one for each user ID
DELETE FROM public.users a
USING public.users b
WHERE a.id = b.id
  AND a.created_at < b.created_at;

-- Alternative: Delete all entries for a specific email except the most recent
DELETE FROM public.users
WHERE email = '<EMAIL>'
  AND created_at < (
    SELECT MAX(created_at)
    FROM public.users
    WHERE email = '<EMAIL>'
  );

-- Verify no duplicates remain
SELECT id, email, COUNT(*) as count
FROM public.users
GROUP BY id, email
HAVING COUNT(*) > 1;
