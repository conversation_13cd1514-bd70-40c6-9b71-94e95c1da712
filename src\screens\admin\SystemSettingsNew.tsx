import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  TextInput,
  Switch,
  List,
  Divider,
  Portal,
  Dialog,
  RadioButton,
  Chip,
  SegmentedButtons,
  IconButton,
  Banner,
  Surface,
  FAB,
  Snackbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { supabase } from '../../../lib/supabase';
import { format } from 'date-fns';

interface SystemSettings {
  // Clinic Information
  clinicName: string;
  clinicAddress: string;
  clinicPhone: string;
  clinicEmail: string;
  clinicWebsite: string;
  clinicLogo?: string;
  
  // Operating Hours
  operatingHours: {
    [key: string]: {
      isOpen: boolean;
      openTime: string;
      closeTime: string;
      breakStart?: string;
      breakEnd?: string;
    };
  };
  
  // Appointment Settings
  appointmentDuration: number; // in minutes
  bufferTime: number; // in minutes between appointments
  maxAdvanceBooking: number; // days in advance
  maxDailyAppointments: number;
  allowOnlineBooking: boolean;
  requirePaymentConfirmation: boolean;
  cancellationPolicy: string;
  cancellationTimeLimit: number; // hours before appointment
  
  // Notification Settings
  enableEmailNotifications: boolean;
  enableSMSNotifications: boolean;
  enablePushNotifications: boolean;
  appointmentReminder: number; // hours before appointment
  followUpReminder: number; // days after appointment
  
  // Payment Settings
  acceptedPaymentMethods: string[];
  taxRate: number;
  currency: string;
  
  // System Settings
  maintenanceMode: boolean;
  autoBackup: boolean;
  backupFrequency: string;
  dataRetentionPeriod: number; // in days
  sessionTimeout: number; // in minutes
  
  // Features
  enableReviews: boolean;
  enableReferrals: boolean;
  enableLoyaltyProgram: boolean;
  enableTeleconsultation: boolean;
}

const defaultSettings: SystemSettings = {
  clinicName: "Dr. Shilpa's Dental Clinic",
  clinicAddress: '',
  clinicPhone: '',
  clinicEmail: '',
  clinicWebsite: '',
  operatingHours: {
    monday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breakStart: '13:00', breakEnd: '14:00' },
    tuesday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breakStart: '13:00', breakEnd: '14:00' },
    wednesday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breakStart: '13:00', breakEnd: '14:00' },
    thursday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breakStart: '13:00', breakEnd: '14:00' },
    friday: { isOpen: true, openTime: '09:00', closeTime: '18:00', breakStart: '13:00', breakEnd: '14:00' },
    saturday: { isOpen: true, openTime: '09:00', closeTime: '14:00' },
    sunday: { isOpen: false, openTime: '09:00', closeTime: '18:00' },
  },
  appointmentDuration: 30,
  bufferTime: 15,
  maxAdvanceBooking: 30,
  maxDailyAppointments: 20,
  allowOnlineBooking: true,
  requirePaymentConfirmation: false,
  cancellationPolicy: 'Appointments can be cancelled up to 24 hours in advance',
  cancellationTimeLimit: 24,
  enableEmailNotifications: true,
  enableSMSNotifications: false,
  enablePushNotifications: true,
  appointmentReminder: 24,
  followUpReminder: 7,
  acceptedPaymentMethods: ['cash', 'card', 'upi'],
  taxRate: 18,
  currency: 'INR',
  maintenanceMode: false,
  autoBackup: true,
  backupFrequency: 'daily',
  dataRetentionPeriod: 365,
  sessionTimeout: 30,
  enableReviews: true,
  enableReferrals: true,
  enableLoyaltyProgram: false,
  enableTeleconsultation: false,
};

export const SystemSettings = () => {
  const theme = useTheme();
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeSection, setActiveSection] = useState('clinic');
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [showTimePickerFor, setShowTimePickerFor] = useState<string | null>(null);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => void>(() => {});

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setSettings({ ...defaultSettings, ...data.settings });
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      showSnackbar('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const { error } = await supabase
        .from('system_settings')
        .upsert({
          id: 1, // Single row for system settings
          settings,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      // Log admin action
      await logAdminAction('settings_updated', 'System settings updated');

      showSnackbar('Settings saved successfully');
      setUnsavedChanges(false);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const logAdminAction = async (action: string, description: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase.from('admin_activity_logs').insert({
          admin_id: user.id,
          action_type: action,
          target_type: 'system',
          description,
        });
      }
    } catch (error) {
      console.error('Error logging admin action:', error);
    }
  };

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setUnsavedChanges(true);
  };

  const updateOperatingHours = (day: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      operatingHours: {
        ...prev.operatingHours,
        [day]: {
          ...prev.operatingHours[day],
          [field]: value,
        },
      },
    }));
    setUnsavedChanges(true);
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const handleSave = () => {
    if (unsavedChanges) {
      setConfirmAction(() => saveSettings);
      setConfirmDialogVisible(true);
    }
  };

  const resetToDefaults = () => {
    setConfirmAction(() => () => {
      setSettings(defaultSettings);
      setUnsavedChanges(true);
      showSnackbar('Settings reset to defaults');
    });
    setConfirmDialogVisible(true);
  };

  const renderClinicInfo = () => (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="hospital-building"
            size={24}
            color={theme.colors.primary}
          />
          <Title style={styles.sectionTitle}>Clinic Information</Title>
        </View>

        <TextInput
          label="Clinic Name"
          value={settings.clinicName}
          onChangeText={(text) => updateSetting('clinicName', text)}
          mode="outlined"
          style={styles.input}
          left={<TextInput.Icon icon="hospital" />}
        />

        <TextInput
          label="Address"
          value={settings.clinicAddress}
          onChangeText={(text) => updateSetting('clinicAddress', text)}
          mode="outlined"
          multiline
          numberOfLines={3}
          style={styles.input}
          left={<TextInput.Icon icon="map-marker" />}
        />

        <TextInput
          label="Phone"
          value={settings.clinicPhone}
          onChangeText={(text) => updateSetting('clinicPhone', text)}
          mode="outlined"
          keyboardType="phone-pad"
          style={styles.input}
          left={<TextInput.Icon icon="phone" />}
        />

        <TextInput
          label="Email"
          value={settings.clinicEmail}
          onChangeText={(text) => updateSetting('clinicEmail', text)}
          mode="outlined"
          keyboardType="email-address"
          autoCapitalize="none"
          style={styles.input}
          left={<TextInput.Icon icon="email" />}
        />

        <TextInput
          label="Website"
          value={settings.clinicWebsite}
          onChangeText={(text) => updateSetting('clinicWebsite', text)}
          mode="outlined"
          autoCapitalize="none"
          style={styles.input}
          left={<TextInput.Icon icon="web" />}
        />
      </Card.Content>
    </Card>
  );

  const renderOperatingHours = () => {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    
    return (
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons
              name="clock-outline"
              size={24}
              color={theme.colors.primary}
            />
            <Title style={styles.sectionTitle}>Operating Hours</Title>
          </View>

          {days.map(day => {
            const daySettings = settings.operatingHours[day];
            const dayName = day.charAt(0).toUpperCase() + day.slice(1);
            
            return (
              <Surface key={day} style={styles.dayCard} elevation={1}>
                <View style={styles.dayHeader}>
                  <Text style={styles.dayName}>{dayName}</Text>
                  <Switch
                    value={daySettings.isOpen}
                    onValueChange={(value) => updateOperatingHours(day, 'isOpen', value)}
                    color={theme.colors.primary}
                  />
                </View>
                
                {daySettings.isOpen && (
                  <View style={styles.timeContainer}>
                    <View style={styles.timeRow}>
                      <Chip
                        icon="clock-start"
                        style={styles.timeChip}
                        onPress={() => setShowTimePickerFor(`${day}-open`)}
                      >
                        Open: {daySettings.openTime}
                      </Chip>
                      <Chip
                        icon="clock-end"
                        style={styles.timeChip}
                        onPress={() => setShowTimePickerFor(`${day}-close`)}
                      >
                        Close: {daySettings.closeTime}
                      </Chip>
                    </View>
                    
                    {(day !== 'saturday' && day !== 'sunday') && (
                      <View style={styles.timeRow}>
                        <Chip
                          icon="food"
                          style={styles.timeChip}
                          onPress={() => setShowTimePickerFor(`${day}-break-start`)}
                        >
                          Break: {daySettings.breakStart || '--:--'}
                        </Chip>
                        <Chip
                          icon="food-off"
                          style={styles.timeChip}
                          onPress={() => setShowTimePickerFor(`${day}-break-end`)}
                        >
                          Resume: {daySettings.breakEnd || '--:--'}
                        </Chip>
                      </View>
                    )}
                  </View>
                )}
              </Surface>
            );
          })}
        </Card.Content>
      </Card>
    );
  };

  const renderAppointmentSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="calendar-clock"
            size={24}
            color={theme.colors.primary}
          />
          <Title style={styles.sectionTitle}>Appointment Settings</Title>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Default Duration (minutes)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('appointmentDuration', Math.max(15, settings.appointmentDuration - 15))}
            />
            <Text style={styles.numberValue}>{settings.appointmentDuration}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('appointmentDuration', Math.min(120, settings.appointmentDuration + 15))}
            />
          </View>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Buffer Time (minutes)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('bufferTime', Math.max(0, settings.bufferTime - 5))}
            />
            <Text style={styles.numberValue}>{settings.bufferTime}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('bufferTime', Math.min(60, settings.bufferTime + 5))}
            />
          </View>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Max Advance Booking (days)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('maxAdvanceBooking', Math.max(7, settings.maxAdvanceBooking - 7))}
            />
            <Text style={styles.numberValue}>{settings.maxAdvanceBooking}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('maxAdvanceBooking', Math.min(90, settings.maxAdvanceBooking + 7))}
            />
          </View>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Max Daily Appointments</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('maxDailyAppointments', Math.max(5, settings.maxDailyAppointments - 5))}
            />
            <Text style={styles.numberValue}>{settings.maxDailyAppointments}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('maxDailyAppointments', Math.min(50, settings.maxDailyAppointments + 5))}
            />
          </View>
        </View>

        <Divider style={styles.divider} />

        <List.Item
          title="Allow Online Booking"
          description="Patients can book appointments online"
          left={props => <List.Icon {...props} icon="web" />}
          right={() => (
            <Switch
              value={settings.allowOnlineBooking}
              onValueChange={(value) => updateSetting('allowOnlineBooking', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <List.Item
          title="Require Payment Confirmation"
          description="Appointments need payment before confirmation"
          left={props => <List.Icon {...props} icon="credit-card-check" />}
          right={() => (
            <Switch
              value={settings.requirePaymentConfirmation}
              onValueChange={(value) => updateSetting('requirePaymentConfirmation', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <TextInput
          label="Cancellation Policy"
          value={settings.cancellationPolicy}
          onChangeText={(text) => updateSetting('cancellationPolicy', text)}
          mode="outlined"
          multiline
          numberOfLines={3}
          style={styles.input}
        />

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Cancellation Time Limit (hours)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('cancellationTimeLimit', Math.max(1, settings.cancellationTimeLimit - 1))}
            />
            <Text style={styles.numberValue}>{settings.cancellationTimeLimit}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('cancellationTimeLimit', Math.min(72, settings.cancellationTimeLimit + 1))}
            />
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderNotificationSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="bell-outline"
            size={24}
            color={theme.colors.primary}
          />
          <Title style={styles.sectionTitle}>Notification Settings</Title>
        </View>

        <List.Item
          title="Email Notifications"
          description="Send appointment reminders via email"
          left={props => <List.Icon {...props} icon="email" />}
          right={() => (
            <Switch
              value={settings.enableEmailNotifications}
              onValueChange={(value) => updateSetting('enableEmailNotifications', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <List.Item
          title="SMS Notifications"
          description="Send appointment reminders via SMS"
          left={props => <List.Icon {...props} icon="message-text" />}
          right={() => (
            <Switch
              value={settings.enableSMSNotifications}
              onValueChange={(value) => updateSetting('enableSMSNotifications', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <List.Item
          title="Push Notifications"
          description="Send in-app push notifications"
          left={props => <List.Icon {...props} icon="cellphone" />}
          right={() => (
            <Switch
              value={settings.enablePushNotifications}
              onValueChange={(value) => updateSetting('enablePushNotifications', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <Divider style={styles.divider} />

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Appointment Reminder (hours before)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('appointmentReminder', Math.max(1, settings.appointmentReminder - 1))}
            />
            <Text style={styles.numberValue}>{settings.appointmentReminder}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('appointmentReminder', Math.min(48, settings.appointmentReminder + 1))}
            />
          </View>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Follow-up Reminder (days after)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('followUpReminder', Math.max(1, settings.followUpReminder - 1))}
            />
            <Text style={styles.numberValue}>{settings.followUpReminder}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('followUpReminder', Math.min(30, settings.followUpReminder + 1))}
            />
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderPaymentSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="cash-multiple"
            size={24}
            color={theme.colors.primary}
          />
          <Title style={styles.sectionTitle}>Payment Settings</Title>
        </View>

        <Text style={styles.fieldLabel}>Accepted Payment Methods</Text>
        <View style={styles.chipContainer}>
          {['cash', 'card', 'upi', 'netbanking', 'wallet'].map(method => (
            <Chip
              key={method}
              selected={settings.acceptedPaymentMethods.includes(method)}
              onPress={() => {
                const newMethods = settings.acceptedPaymentMethods.includes(method)
                  ? settings.acceptedPaymentMethods.filter(m => m !== method)
                  : [...settings.acceptedPaymentMethods, method];
                updateSetting('acceptedPaymentMethods', newMethods);
              }}
              style={styles.paymentChip}
            >
              {method.toUpperCase()}
            </Chip>
          ))}
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Tax Rate (%)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('taxRate', Math.max(0, settings.taxRate - 1))}
            />
            <Text style={styles.numberValue}>{settings.taxRate}%</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('taxRate', Math.min(30, settings.taxRate + 1))}
            />
          </View>
        </View>

        <Text style={styles.fieldLabel}>Currency</Text>
        <SegmentedButtons
          value={settings.currency}
          onValueChange={(value) => updateSetting('currency', value)}
          buttons={[
            { value: 'INR', label: 'INR ₹' },
            { value: 'USD', label: 'USD $' },
            { value: 'EUR', label: 'EUR €' },
          ]}
          style={styles.segmentedButtons}
        />
      </Card.Content>
    </Card>
  );

  const renderSystemSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="cog"
            size={24}
            color={theme.colors.primary}
          />
          <Title style={styles.sectionTitle}>System Configuration</Title>
        </View>

        <Banner
          visible={settings.maintenanceMode}
          icon="alert"
          style={styles.maintenanceBanner}
        >
          Maintenance mode is active. The system is not accessible to users.
        </Banner>

        <List.Item
          title="Maintenance Mode"
          description="Temporarily disable system access for users"
          left={props => <List.Icon {...props} icon="wrench" />}
          right={() => (
            <Switch
              value={settings.maintenanceMode}
              onValueChange={(value) => updateSetting('maintenanceMode', value)}
              color={theme.colors.error}
            />
          )}
        />

        <List.Item
          title="Auto Backup"
          description="Automatically backup system data"
          left={props => <List.Icon {...props} icon="backup-restore" />}
          right={() => (
            <Switch
              value={settings.autoBackup}
              onValueChange={(value) => updateSetting('autoBackup', value)}
              color={theme.colors.primary}
            />
          )}
        />

        {settings.autoBackup && (
          <>
            <Text style={styles.fieldLabel}>Backup Frequency</Text>
            <SegmentedButtons
              value={settings.backupFrequency}
              onValueChange={(value) => updateSetting('backupFrequency', value)}
              buttons={[
                { value: 'daily', label: 'Daily' },
                { value: 'weekly', label: 'Weekly' },
                { value: 'monthly', label: 'Monthly' },
              ]}
              style={styles.segmentedButtons}
            />
          </>
        )}

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Data Retention (days)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('dataRetentionPeriod', Math.max(30, settings.dataRetentionPeriod - 30))}
            />
            <Text style={styles.numberValue}>{settings.dataRetentionPeriod}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('dataRetentionPeriod', Math.min(1095, settings.dataRetentionPeriod + 30))}
            />
          </View>
        </View>

        <View style={styles.settingRow}>
          <Text style={styles.settingLabel}>Session Timeout (minutes)</Text>
          <View style={styles.numberInput}>
            <IconButton
              icon="minus"
              size={20}
              onPress={() => updateSetting('sessionTimeout', Math.max(5, settings.sessionTimeout - 5))}
            />
            <Text style={styles.numberValue}>{settings.sessionTimeout}</Text>
            <IconButton
              icon="plus"
              size={20}
              onPress={() => updateSetting('sessionTimeout', Math.min(120, settings.sessionTimeout + 5))}
            />
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderFeatureSettings = () => (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.sectionHeader}>
          <MaterialCommunityIcons
            name="star-outline"
            size={24}
            color={theme.colors.primary}
          />
          <Title style={styles.sectionTitle}>Feature Settings</Title>
        </View>

        <List.Item
          title="Patient Reviews"
          description="Allow patients to leave reviews"
          left={props => <List.Icon {...props} icon="star" />}
          right={() => (
            <Switch
              value={settings.enableReviews}
              onValueChange={(value) => updateSetting('enableReviews', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <List.Item
          title="Referral Program"
          description="Enable patient referral rewards"
          left={props => <List.Icon {...props} icon="account-multiple" />}
          right={() => (
            <Switch
              value={settings.enableReferrals}
              onValueChange={(value) => updateSetting('enableReferrals', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <List.Item
          title="Loyalty Program"
          description="Reward frequent patients"
          left={props => <List.Icon {...props} icon="gift" />}
          right={() => (
            <Switch
              value={settings.enableLoyaltyProgram}
              onValueChange={(value) => updateSetting('enableLoyaltyProgram', value)}
              color={theme.colors.primary}
            />
          )}
        />

        <List.Item
          title="Teleconsultation"
          description="Enable video consultations"
          left={props => <List.Icon {...props} icon="video" />}
          right={() => (
            <Switch
              value={settings.enableTeleconsultation}
              onValueChange={(value) => updateSetting('enableTeleconsultation', value)}
              color={theme.colors.primary}
            />
          )}
        />
      </Card.Content>
    </Card>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'clinic':
        return renderClinicInfo();
      case 'hours':
        return renderOperatingHours();
      case 'appointments':
        return renderAppointmentSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'payments':
        return renderPaymentSettings();
      case 'system':
        return renderSystemSettings();
      case 'features':
        return renderFeatureSettings();
      default:
        return renderClinicInfo();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <View style={styles.header}>
          <Title style={styles.title}>System Settings</Title>
          {unsavedChanges && (
            <Chip icon="alert" style={styles.unsavedChip}>
              Unsaved Changes
            </Chip>
          )}
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.tabBar}
        >
          {[
            { key: 'clinic', label: 'Clinic', icon: 'hospital-building' },
            { key: 'hours', label: 'Hours', icon: 'clock-outline' },
            { key: 'appointments', label: 'Appointments', icon: 'calendar-clock' },
            { key: 'notifications', label: 'Notifications', icon: 'bell-outline' },
            { key: 'payments', label: 'Payments', icon: 'cash-multiple' },
            { key: 'system', label: 'System', icon: 'cog' },
            { key: 'features', label: 'Features', icon: 'star-outline' },
          ].map(tab => (
            <Chip
              key={tab.key}
              selected={activeSection === tab.key}
              onPress={() => setActiveSection(tab.key)}
              style={styles.tabChip}
              icon={tab.icon}
            >
              {tab.label}
            </Chip>
          ))}
        </ScrollView>

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
        >
          {renderContent()}
          
          <View style={styles.actions}>
            <Button
              mode="outlined"
              onPress={resetToDefaults}
              style={styles.actionButton}
              icon="restore"
            >
              Reset to Defaults
            </Button>
            <Button
              mode="contained"
              onPress={handleSave}
              loading={saving}
              disabled={!unsavedChanges}
              style={styles.actionButton}
              icon="content-save"
            >
              Save Settings
            </Button>
          </View>

          <View style={{ height: 100 }} />
        </ScrollView>

        <Portal>
          <Dialog visible={confirmDialogVisible} onDismiss={() => setConfirmDialogVisible(false)}>
            <Dialog.Title>Confirm Action</Dialog.Title>
            <Dialog.Content>
              <Text>Are you sure you want to proceed with this action?</Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={() => setConfirmDialogVisible(false)}>Cancel</Button>
              <Button
                onPress={() => {
                  confirmAction();
                  setConfirmDialogVisible(false);
                }}
              >
                Confirm
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
        >
          {snackbarMessage}
        </Snackbar>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  unsavedChip: {
    backgroundColor: '#FF9800',
  },
  tabBar: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 60,
    elevation: 1,
  },
  tabChip: {
    marginRight: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  input: {
    marginBottom: 12,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
    marginTop: 8,
    color: '#666',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  settingLabel: {
    fontSize: 14,
    flex: 1,
    color: '#333',
  },
  numberInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  numberValue: {
    fontSize: 16,
    fontWeight: 'bold',
    paddingHorizontal: 12,
    minWidth: 50,
    textAlign: 'center',
  },
  dayCard: {
    marginBottom: 8,
    padding: 12,
    borderRadius: 8,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  timeContainer: {
    marginTop: 8,
  },
  timeRow: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  timeChip: {
    flex: 1,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginVertical: 8,
  },
  paymentChip: {
    marginBottom: 4,
  },
  segmentedButtons: {
    marginVertical: 8,
  },
  divider: {
    marginVertical: 12,
  },
  maintenanceBanner: {
    backgroundColor: '#FFECB3',
    marginBottom: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});
