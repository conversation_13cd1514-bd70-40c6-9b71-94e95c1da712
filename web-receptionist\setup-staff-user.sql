-- SQL Script to create a staff user for the receptionist portal
-- Run this in your Supabase SQL Editor

-- First, create the auth user (you'll need to do this through Supabase Auth UI or API)
-- Then run this script to create the corresponding user profile

-- Example: Create a staff user profile
-- Replace the UUID with the actual user ID from Supabase Auth
INSERT INTO users (
  id,
  email,
  name,
  role,
  status,
  created_at,
  updated_at
) VALUES (
  'your-auth-user-id-here', -- Replace with actual UUID from Supabase Auth
  '<EMAIL>',
  'Receptionist Staff',
  'staff',
  'active',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  role = EXCLUDED.role,
  status = EXCLUDED.status,
  updated_at = NOW();

-- Create an admin user profile (optional)
INSERT INTO users (
  id,
  email,
  name,
  role,
  status,
  created_at,
  updated_at
) VALUES (
  'your-admin-user-id-here', -- Replace with actual UUID from Supabase Auth
  '<EMAIL>',
  'System Administrator',
  'admin',
  'active',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  role = EXCLUDED.role,
  status = EXCLUDED.status,
  updated_at = NOW();

-- Verify the users were created
SELECT id, email, name, role, status FROM users WHERE role IN ('staff', 'admin');
