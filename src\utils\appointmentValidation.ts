import { 
  CLINIC_HOURS, 
  BOOKING_RULES, 
  isClinicWorkingDay,
  TIME_SLOTS,
  APPOINTMENT_ERRORS,
  APPOINTMENT_TYPES
} from '../constants/appointmentConfig';
import { parse, format, isAfter, isBefore, addDays, differenceInHours } from 'date-fns';

/**
 * Validates if a selected date is valid for booking
 */
export const validateBookingDate = (date: Date): { valid: boolean; error?: string } => {
  // Check if it's a working day
  if (!isClinicWorkingDay(date)) {
    return {
      valid: false,
      error: APPOINTMENT_ERRORS.CLINIC_CLOSED
    };
  }

  // Check if date is in the past
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (isBefore(date, today)) {
    return {
      valid: false,
      error: APPOINTMENT_ERRORS.PAST_TIME
    };
  }

  // Check if date exceeds maximum advance booking
  const maxDate = addDays(today, BOOKING_RULES.MAX_ADVANCE_DAYS);
  if (isAfter(date, maxDate)) {
    return {
      valid: false,
      error: APPOINTMENT_ERRORS.TOO_FAR
    };
  }

  return { valid: true };
};

/**
 * Validates if a selected time slot is within clinic hours
 */
export const validateTimeSlot = (time: string): { valid: boolean; error?: string } => {
  const [hours] = time.split(':').map(Number);

  if (hours < CLINIC_HOURS.START_HOUR || hours >= CLINIC_HOURS.END_HOUR) {
    return {
      valid: false,
      error: APPOINTMENT_ERRORS.CLINIC_CLOSED
    };
  }

  // Check if it's the last hour and ensure appointment won't exceed closing time
  if (hours === CLINIC_HOURS.END_HOUR - 1) {
    // Only allow appointments that will end by closing time
    // Since minimum slot duration is 60 minutes, last slot should start at least 1 hour before closing
    return {
      valid: false,
      error: APPOINTMENT_ERRORS.CLINIC_CLOSED
    };
  }

  return { valid: true };
};

/**
 * Validates if an appointment can be cancelled based on advance notice requirement
 */
export const validateCancellation = (appointmentDateTime: Date): { valid: boolean; error?: string } => {
  const now = new Date();
  const hoursUntilAppointment = differenceInHours(appointmentDateTime, now);

  if (hoursUntilAppointment < BOOKING_RULES.MIN_ADVANCE_HOURS) {
    return {
      valid: false,
      error: APPOINTMENT_ERRORS.TOO_SOON
    };
  }

  return { valid: true };
};

/**
 * Validates appointment type
 */
export const validateAppointmentType = (type: string): { valid: boolean; error?: string } => {
  const validType = APPOINTMENT_TYPES.find(t => t.value === type);
  
  if (!validType) {
    return { 
      valid: false, 
      error: 'Invalid appointment type selected' 
    };
  }

  return { valid: true };
};

/**
 * Checks if two time slots overlap
 */
export const doTimeSlotsOverlap = (
  start1: string, 
  end1: string, 
  start2: string, 
  end2: string
): boolean => {
  const slot1Start = parse(start1, 'HH:mm', new Date());
  const slot1End = parse(end1, 'HH:mm', new Date());
  const slot2Start = parse(start2, 'HH:mm', new Date());
  const slot2End = parse(end2, 'HH:mm', new Date());

  return (
    (slot1Start >= slot2Start && slot1Start < slot2End) ||
    (slot1End > slot2Start && slot1End <= slot2End) ||
    (slot1Start <= slot2Start && slot1End >= slot2End)
  );
};

/**
 * Formats appointment time range for display
 */
export const formatAppointmentTimeRange = (
  startTime: string, 
  endTime: string
): string => {
  return `${startTime} - ${endTime}`;
};

/**
 * Gets appointment type configuration
 */
export const getAppointmentTypeConfig = (type: string) => {
  return APPOINTMENT_TYPES.find(t => t.value === type);
};

/**
 * Validates complete appointment booking data
 */
export const validateAppointmentBooking = (
  date: Date,
  time: string,
  appointmentType: string
): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validate date
  const dateValidation = validateBookingDate(date);
  if (!dateValidation.valid && dateValidation.error) {
    errors.push(dateValidation.error);
  }

  // Validate time
  const timeValidation = validateTimeSlot(time);
  if (!timeValidation.valid && timeValidation.error) {
    errors.push(timeValidation.error);
  }

  // Validate appointment type
  const typeValidation = validateAppointmentType(appointmentType);
  if (!typeValidation.valid && typeValidation.error) {
    errors.push(typeValidation.error);
  }

  return {
    valid: errors.length === 0,
    errors
  };
};
