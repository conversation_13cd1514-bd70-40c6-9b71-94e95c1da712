-- 007_fn_book_appointment.sql
-- Purpose: Provide atomic RPC to book an appointment with server-side conflict enforcement
-- Returns the created appointment row or raises a friendly error if slot is unavailable

CREATE OR REPLACE FUNCTION public.book_appointment(
  p_patient_id UUID,
  p_dentist_id UUID,
  p_date DATE,
  p_start_time TIME,
  p_duration_minutes INTEGER DEFAULT 60,
  p_appointment_type TEXT DEFAULT 'checkup',
  p_notes TEXT DEFAULT NULL
)
RETURNS public.appointments
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public
AS $$
DECLARE
  v_end_time TIME;
  v_row public.appointments;
BEGIN
  IF p_duration_minutes IS NULL OR p_duration_minutes <= 0 THEN
    p_duration_minutes := 60;
  END IF;

  v_end_time := (p_start_time + make_interval(mins => p_duration_minutes))::time;

  INSERT INTO public.appointments (
    patient_id,
    dentist_id,
    date,
    start_time,
    end_time,
    appointment_type,
    duration_minutes,
    notes,
    status
  ) VALUES (
    p_patient_id,
    p_dentist_id,
    p_date,
    p_start_time,
    v_end_time,
    COALESCE(p_appointment_type, 'checkup'),
    COALESCE(p_duration_minutes, 60),
    p_notes,
    'scheduled'
  )
  RETURNING * INTO v_row;

  RETURN v_row;

EXCEPTION
  WHEN exclusion_violation THEN
    RAISE EXCEPTION 'Time slot is not available for this dentist' USING ERRCODE = '23P01';
  WHEN unique_violation THEN
    RAISE EXCEPTION 'Time slot is not available for this dentist' USING ERRCODE = '23505';
END;
$$;

-- Allow authenticated users to call this RPC (adjust as desired)
GRANT EXECUTE ON FUNCTION public.book_appointment(UUID, UUID, DATE, TIME, INTEGER, TEXT, TEXT) TO authenticated;

