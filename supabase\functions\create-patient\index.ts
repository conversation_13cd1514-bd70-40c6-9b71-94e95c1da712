// @ts-nocheck

// Supabase Edge Function: create-patient
// Creates an auth user (role = patient) with optional invite or temp password,
// then inserts a matching row into public.users. Returns the created user's id/name/email.
// NOTE: Requires SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY to be set as function secrets.

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: { ...corsHeaders } });
  }

  try {
    console.log("Create patient function called");
    const body = await req.json();
    console.log("Request body:", body);
    const { name, email, phone, date_of_birth, gender, address, sendInvite, defaultPassword } = body;
    console.log("Destructured values:", { name, email, phone, date_of_birth, gender, address, sendInvite, defaultPassword });
    if (!name) throw new Error("name is required");
    if (!email && !phone) throw new Error("email or phone is required");
    console.log("Validation passed");

    const supabaseUrl = Deno.env.get("URL");
    const serviceKey = Deno.env.get("SERVICE_ROLE_KEY");
    console.log("Env vars present:", { supabaseUrl: !!supabaseUrl, serviceKey: !!serviceKey });
    if (!supabaseUrl || !serviceKey) throw new Error("Missing service configuration");
    console.log("Configuration check passed");

    const admin = createClient(supabaseUrl, serviceKey, { auth: { autoRefreshToken: false, persistSession: false } });

    // Create auth user without affecting current client session
    let tempPassword = defaultPassword as string | undefined;
    if (!tempPassword && !sendInvite) {
      // generate a strong temporary password if not inviting
      tempPassword = crypto.randomUUID() + "Aa1!";
    }

    // Normalize identifiers for auth user creation
    // - Indian clinics enter 10-digit local numbers; convert to E.164 +91XXXXXXXXXX for auth
    // - If already E.164 with +, use as-is
    const e164 = /^\+[1-9]\d{6,14}$/;
    const digits = (phone || '').replace(/\D/g, '');
    let phoneForAuth: string | undefined = undefined;
    if (phone && e164.test(phone)) {
      phoneForAuth = phone;
    } else if (digits) {
      if (digits.length === 10) {
        phoneForAuth = `+91${digits}`; // assume India for 10-digit local numbers
      } else if (digits.length === 12 && digits.startsWith('91')) {
        phoneForAuth = `+${digits}`; // already has country code without plus
      }
    }
    const emailForAuth = (email && email.trim())
      ? email.trim()
      : (digits ? `${digits}@noemail.local` : `${crypto.randomUUID()}@noemail.local`);

    const { data: created, error: createErr } = await admin.auth.admin.createUser({
      email: emailForAuth,
      phone: phoneForAuth,
      password: tempPassword,
      user_metadata: { name, role: "patient" },
    });
    if (createErr) throw createErr;
    const user = created.user;
    if (!user) throw new Error("Failed to create auth user");

    // Optional invite email to set password
    if (sendInvite && email) {
      const { error: inviteErr } = await admin.auth.admin.inviteUserByEmail(email);
      if (inviteErr) {
        // Not fatal, continue but include note
        console.warn("invite error", inviteErr);
      }
    }

    // Insert profile row (ensure email is non-null for NOT NULL constraint)
    const profileEmail = email || (phone ? `${phone}@noemail.local` : `${user.id}@noemail.local`);
    console.log("Inserting user:", { id: user.id, name, email: profileEmail, phone, role: "patient", date_of_birth, gender, address });
    const { error: insertErr } = await admin.from("users").insert([
      { id: user.id, name, email: profileEmail, phone, role: "patient", date_of_birth, gender, address },
    ]);
    console.log("Insert result:", { data: null, error: insertErr });
    if (insertErr) throw insertErr;
    console.log("Insert successful");

    return new Response(
      JSON.stringify({ id: user.id, name, email, phone, tempPassword }),
      { headers: { "Content-Type": "application/json", ...corsHeaders } },
    );
  } catch (e) {
    return new Response(JSON.stringify({ error: e.message || String(e) }), {
      status: 400,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }
});

// end

