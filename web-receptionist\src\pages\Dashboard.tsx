import React, { useEffect, useState } from 'react'
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Button,
  CircularProgress,
} from '@mui/material'
import {
  Event as EventIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import { useAppointments } from '../contexts/AppointmentContext'
import { usePatients } from '../contexts/PatientContext'


const Dashboard: React.FC = () => {
  const { appointments, loading: appointmentsLoading } = useAppointments()
  const { patients, loading: patientsLoading } = usePatients()
  const [todayAppointments, setTodayAppointments] = useState<any[]>([])
  const [stats, setStats] = useState({
    totalAppointments: 0,
    todayAppointments: 0,
    upcomingAppointments: 0,
    totalPatients: 0,
  })

  useEffect(() => {
    if (appointments.length > 0) {
      const today = new Date().toISOString().split('T')[0]
      const todayApts = appointments.filter(apt => apt.date === today)
      const upcomingApts = appointments.filter(apt =>
        apt.date > today &&
        ['scheduled', 'confirmed'].includes(apt.status)
      )

      setTodayAppointments(todayApts.slice(0, 5)) // Show only first 5

      setStats({
        totalAppointments: appointments.length,
        todayAppointments: todayApts.length,
        upcomingAppointments: upcomingApts.length,
        totalPatients: patients.length,
      })
    } else {
      // Set default stats when no appointments
      setStats({
        totalAppointments: 0,
        todayAppointments: 0,
        upcomingAppointments: 0,
        totalPatients: patients.length,
      })
    }
  }, [appointments, patients])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'info'
      case 'confirmed': return 'success'
      case 'completed': return 'default'
      case 'cancelled': return 'error'
      default: return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <ScheduleIcon />
      case 'confirmed': return <EventIcon />
      case 'completed': return <EventIcon />
      case 'cancelled': return <WarningIcon />
      default: return <EventIcon />
    }
  }

  if (appointmentsLoading || patientsLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="60vh" flexDirection="column">
        <CircularProgress size={60} />
        <Typography sx={{ mt: 2 }}>Loading dashboard data...</Typography>
      </Box>
    )
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <EventIcon color="primary" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Today's Appointments
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.todayAppointments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ScheduleIcon color="secondary" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Upcoming Appointments
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.upcomingAppointments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="success" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Total Patients
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.totalPatients}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <EventIcon color="info" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Total Appointments
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.totalAppointments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Today's Appointments */}
      <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Today's Appointments
        </Typography>
        {todayAppointments.length > 0 ? (
          <List>
            {todayAppointments.map((appointment) => (
              <ListItem key={appointment.id} divider>
                <ListItemAvatar>
                  <Avatar>
                    {getStatusIcon(appointment.status)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={`${appointment.start_time} - ${appointment.patient?.name || 'Unknown Patient'}`}
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {appointment.appointment_type || 'General Appointment'} with Dr. {appointment.dentist?.name || 'Unknown'}
                      </Typography>
                      <Chip
                        label={appointment.status}
                        size="small"
                        color={getStatusColor(appointment.status) as any}
                        sx={{ mt: 1 }}
                      />
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        ) : (
          <Typography color="text.secondary">
            No appointments scheduled for today.
          </Typography>
        )}
      </Paper>

      {/* Quick Actions */}
      <Paper elevation={1} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<EventIcon />}
            href="/appointments"
          >
            New Appointment
          </Button>
          <Button
            variant="outlined"
            startIcon={<PeopleIcon />}
            href="/patients"
          >
            Add Patient
          </Button>
          <Button
            variant="outlined"
            startIcon={<ScheduleIcon />}
            href="/calendar"
          >
            View Calendar
          </Button>
        </Box>
      </Paper>
    </Box>
  )
}

export default Dashboard