import React, { useEffect, useState } from 'react'
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
} from '@mui/material'
import {
  Event as EventIcon,
  People as PeopleIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material'
import DebugInfo from '../components/common/DebugInfo'

const Dashboard: React.FC = () => {
  // Temporarily disable context loading to test basic functionality
  const [stats, setStats] = useState({
    totalAppointments: 0,
    todayAppointments: 0,
    upcomingAppointments: 0,
    totalPatients: 0,
  })

  // Mock data for testing
  useEffect(() => {
    setStats({
      totalAppointments: 12,
      todayAppointments: 3,
      upcomingAppointments: 8,
      totalPatients: 45,
    })
  }, [])

  // Removed status functions for now

  // Removed loading check for now

  return (
    <Box>
      <DebugInfo />
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <EventIcon color="primary" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Today's Appointments
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.todayAppointments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <ScheduleIcon color="secondary" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Upcoming Appointments
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.upcomingAppointments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PeopleIcon color="success" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Total Patients
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.totalPatients}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <EventIcon color="info" sx={{ mr: 2 }} />
                <Typography color="textSecondary" gutterBottom>
                  Total Appointments
                </Typography>
              </Box>
              <Typography variant="h4">
                {stats.totalAppointments}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Today's Appointments */}
      <Paper elevation={1} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Today's Appointments
        </Typography>
        <Typography color="text.secondary">
          Dashboard is working! Appointment data will be loaded once contexts are enabled.
        </Typography>
      </Paper>

      {/* Quick Actions */}
      <Paper elevation={1} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<EventIcon />}
            href="/appointments"
          >
            New Appointment
          </Button>
          <Button
            variant="outlined"
            startIcon={<PeopleIcon />}
            href="/patients"
          >
            Add Patient
          </Button>
          <Button
            variant="outlined"
            startIcon={<ScheduleIcon />}
            href="/calendar"
          >
            View Calendar
          </Button>
        </Box>
      </Paper>
    </Box>
  )
}

export default Dashboard