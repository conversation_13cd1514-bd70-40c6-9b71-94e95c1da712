-- 003b_rls_users_add_dentist.sql
-- Purpose: Update users SELECT RLS to include 'dentist' (both as a caller role and as visible roles).
-- Idempotent: Drops old policy if present and recreates with expanded role set.

BEGIN;

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM pg_policies WHERE schemaname='public' AND tablename='users' AND policyname='users_select_for_receptionist') THEN
    DROP POLICY users_select_for_receptionist ON public.users;
  END IF;
END $$;

CREATE POLICY users_select_for_receptionist
ON public.users
FOR SELECT
TO authenticated
USING (
  -- Caller must be in these roles
  EXISTS (
    SELECT 1 FROM public.users me
    WHERE me.id = auth.uid()
      AND me.role IN ('admin','staff','doctor','dentist','receptionist')
  )
  AND (
    -- Receptionist can see doctors and patients generally (include dentist)
    role IN ('doctor','dentist','patient')
    -- Everyone can always see their own row
    OR id = auth.uid()
  )
);

COMMIT;
