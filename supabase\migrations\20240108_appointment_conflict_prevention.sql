-- Add unique constraint to prevent double-booking
-- This ensures no dentist can have overlapping appointments

-- First, let's add end_time column if it doesn't exist
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS end_time TIME;

-- Update existing appointments to have end_time (1 hour after start_time)
UPDATE appointments 
SET end_time = (appointment_time + INTERVAL '1 hour')::TIME
WHERE end_time IS NULL;

-- Make end_time NOT NULL
ALTER TABLE appointments 
ALTER COLUMN end_time SET NOT NULL;

-- Add a constraint to prevent overlapping appointments for the same dentist
CREATE OR REPLACE FUNCTION check_appointment_overlap()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if there's any overlap with existing appointments
  IF EXISTS (
    SELECT 1 FROM appointments
    WHERE dentist_id = NEW.dentist_id
      AND appointment_date = NEW.appointment_date
      AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000')
      AND status NOT IN ('cancelled', 'no-show')
      AND (
        -- Check for time overlap
        (NEW.appointment_time >= appointment_time AND NEW.appointment_time < end_time)
        OR (NEW.end_time > appointment_time AND NEW.end_time <= end_time)
        OR (NEW.appointment_time <= appointment_time AND NEW.end_time >= end_time)
      )
  ) THEN
    RAISE EXCEPTION 'This time slot conflicts with an existing appointment';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for insert
CREATE TRIGGER check_appointment_overlap_insert
BEFORE INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION check_appointment_overlap();

-- Create trigger for update
CREATE TRIGGER check_appointment_overlap_update
BEFORE UPDATE ON appointments
FOR EACH ROW
WHEN (
  OLD.dentist_id IS DISTINCT FROM NEW.dentist_id 
  OR OLD.appointment_date IS DISTINCT FROM NEW.appointment_date
  OR OLD.appointment_time IS DISTINCT FROM NEW.appointment_time
  OR OLD.end_time IS DISTINCT FROM NEW.end_time
  OR OLD.status IS DISTINCT FROM NEW.status
)
EXECUTE FUNCTION check_appointment_overlap();

-- Add index for better performance on conflict checks
CREATE INDEX IF NOT EXISTS idx_appointments_dentist_date_time 
ON appointments(dentist_id, appointment_date, appointment_time, end_time)
WHERE status NOT IN ('cancelled', 'no-show');

-- Add constraint to ensure appointments are within clinic hours (10am-6pm)
ALTER TABLE appointments
ADD CONSTRAINT check_clinic_hours
CHECK (
  appointment_time >= '10:00:00'::TIME 
  AND appointment_time < '18:00:00'::TIME
  AND end_time <= '18:00:00'::TIME
);

-- Add constraint to ensure appointments are not on Sundays
ALTER TABLE appointments
ADD CONSTRAINT check_clinic_days
CHECK (
  EXTRACT(DOW FROM appointment_date) != 0 -- 0 is Sunday
);

-- Add column for follow-up appointments
ALTER TABLE appointments
ADD COLUMN IF NOT EXISTS followup_id UUID REFERENCES appointments(id);

-- Add index for follow-up lookups
CREATE INDEX IF NOT EXISTS idx_appointments_followup 
ON appointments(followup_id)
WHERE followup_id IS NOT NULL;

-- Comment on the new columns and constraints
COMMENT ON COLUMN appointments.end_time IS 'End time of the appointment (typically 1 hour after start time)';
COMMENT ON COLUMN appointments.followup_id IS 'Reference to the original appointment if this is a follow-up';
COMMENT ON CONSTRAINT check_clinic_hours ON appointments IS 'Ensures appointments are within clinic hours (10am-6pm)';
COMMENT ON CONSTRAINT check_clinic_days ON appointments IS 'Ensures appointments are not scheduled on Sundays';
