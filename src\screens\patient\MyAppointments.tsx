import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  Chip,
  FAB,
  Searchbar,
  SegmentedButtons,
  List,
  Avatar,
  IconButton,
  Badge,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { format, isAfter, isBefore, isToday, isTomorrow } from 'date-fns';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAuth } from '../../contexts/AuthContext';
import { useAppointments } from '../../contexts/AppointmentContext';
import { useNavigation } from '@react-navigation/native';
import { Appointment, AppointmentStatus } from '../../types';
import { APPOINTMENT_TYPES } from '../../constants/appointmentConfig';

export const MyAppointments = () => {
  const theme = useTheme();
  const navigation = useNavigation<any>();
  const { user } = useAuth();
  const { appointments, fetchAppointments, cancelAppointment, loading } = useAppointments();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('upcoming');
  const [appointmentTypeFilter, setAppointmentTypeFilter] = useState('all');
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);

  useEffect(() => {
    loadAppointments();
  }, []);

  useEffect(() => {
    filterAppointments();
  }, [appointments, filter, searchQuery, appointmentTypeFilter]);

  const loadAppointments = async () => {
    await fetchAppointments();
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadAppointments();
    setRefreshing(false);
  }, []);

  const filterAppointments = () => {
    let filtered = [...appointments];

    // Apply status filter
    const now = new Date();
    if (filter === 'upcoming') {
      filtered = filtered.filter(
        apt => apt.status === AppointmentStatus.SCHEDULED && 
        isAfter(new Date(apt.date), now)
      );
    } else if (filter === 'past') {
      filtered = filtered.filter(
        apt => apt.status === AppointmentStatus.COMPLETED ||
        isBefore(new Date(apt.date), now)
      );
    } else if (filter === 'cancelled') {
      filtered = filtered.filter(
        apt => apt.status === AppointmentStatus.CANCELLED
      );
    }

    // Apply appointment type filter
    if (appointmentTypeFilter !== 'all') {
      filtered = filtered.filter(
        apt => apt.appointment_type === appointmentTypeFilter
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        apt => 
          apt.dentist?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          apt.notes?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          apt.appointment_type?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort by date and time
    filtered.sort((a, b) => {
      const dateCompare = new Date(a.date).getTime() - new Date(b.date).getTime();
      if (dateCompare !== 0) return dateCompare;
      return a.start_time.localeCompare(b.start_time);
    });

    setFilteredAppointments(filtered);
  };

  const handleCancelAppointment = (appointment: Appointment) => {
    Alert.alert(
      'Cancel Appointment',
      'Are you sure you want to cancel this appointment?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            try {
              await cancelAppointment(appointment.id);
              Alert.alert('Success', 'Appointment cancelled successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel appointment');
            }
          },
        },
      ]
    );
  };

  const handleReschedule = (appointment: Appointment) => {
    navigation.navigate('BookAppointment', { reschedule: appointment });
  };

  const getAppointmentIcon = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return 'calendar-clock';
      case AppointmentStatus.CONFIRMED:
        return 'calendar-check';
      case AppointmentStatus.COMPLETED:
        return 'check-circle';
      case AppointmentStatus.CANCELLED:
        return 'calendar-remove';
      default:
        return 'calendar';
    }
  };

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return '#2196F3';
      case AppointmentStatus.CONFIRMED:
        return '#4CAF50';
      case AppointmentStatus.COMPLETED:
        return '#8BC34A';
      case AppointmentStatus.CANCELLED:
        return '#F44336';
      default:
        return '#757575';
    }
  };

  const getDateLabel = (date: string) => {
    const appointmentDate = new Date(date);
    if (isToday(appointmentDate)) return 'Today';
    if (isTomorrow(appointmentDate)) return 'Tomorrow';
    return format(appointmentDate, 'EEEE, MMM d');
  };

  const getAppointmentTypeConfig = (type?: string) => {
    if (!type) return { label: 'General', color: '#757575', icon: 'tooth' };
    const config = APPOINTMENT_TYPES.find(t => t.value === type);
    return config || { label: type, color: '#757575', icon: 'tooth' };
  };

  const renderAppointmentCard = (appointment: Appointment) => {
    const appointmentDate = new Date(appointment.date);
    const isPast = isBefore(appointmentDate, new Date());
    const canCancel = appointment.status === AppointmentStatus.SCHEDULED && !isPast;

    return (
      <Card key={appointment.id} style={styles.appointmentCard}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.dateTimeContainer}>
              <View style={styles.dateBox}>
                <Text style={styles.dayText}>
                  {format(appointmentDate, 'dd')}
                </Text>
                <Text style={styles.monthText}>
                  {format(appointmentDate, 'MMM')}
                </Text>
              </View>
              <View style={styles.appointmentInfo}>
                <Text style={styles.dateLabel}>
                  {getDateLabel(appointment.date)}
                </Text>
                <Text style={styles.timeText}>
                  {appointment.start_time} - {appointment.end_time}
                </Text>
                <Text style={styles.dentistText}>
                  Dr. {appointment.dentist?.name}
                </Text>
              </View>
            </View>
            <View style={styles.statusContainer}>
              {appointment.appointment_type && (
                <Chip
                  icon={getAppointmentTypeConfig(appointment.appointment_type).icon}
                  style={[
                    styles.typeChip,
                    { backgroundColor: getAppointmentTypeConfig(appointment.appointment_type).color + '20' }
                  ]}
                  textStyle={{ 
                    color: getAppointmentTypeConfig(appointment.appointment_type).color,
                    fontSize: 11
                  }}
                  compact
                >
                  {getAppointmentTypeConfig(appointment.appointment_type).label}
                </Chip>
              )}
              <Chip
                icon={getAppointmentIcon(appointment.status)}
                style={[
                  styles.statusChip,
                  { backgroundColor: getStatusColor(appointment.status) }
                ]}
                textStyle={{
                  color: '#fff',
                  fontSize: 11
                }}
                compact
              >
                {appointment.status}
              </Chip>
            </View>
          </View>

          {appointment.notes && (
            <View style={styles.notesContainer}>
              <MaterialCommunityIcons name="note-text" size={16} color="#666" />
              <Text style={styles.notesText}>{appointment.notes}</Text>
            </View>
          )}

          <Divider style={styles.divider} />

          <View style={styles.cardActions}>
            <Button
              mode="text"
              onPress={() => navigation.navigate('AppointmentDetails', { appointment })}
              icon="eye"
              compact
            >
              View Details
            </Button>
            {canCancel && (
              <>
                <Button
                  mode="text"
                  onPress={() => handleReschedule(appointment)}
                  icon="calendar-edit"
                  compact
                >
                  Reschedule
                </Button>
                <Button
                  mode="text"
                  onPress={() => handleCancelAppointment(appointment)}
                  icon="cancel"
                  textColor={theme.colors.error}
                  compact
                >
                  Cancel
                </Button>
              </>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialCommunityIcons
        name="calendar-blank"
        size={80}
        color="#ccc"
      />
      <Text style={styles.emptyTitle}>No Appointments</Text>
      <Text style={styles.emptyText}>
        {filter === 'upcoming' 
          ? "You don't have any upcoming appointments"
          : filter === 'past'
          ? "You don't have any past appointments"
          : "You don't have any cancelled appointments"}
      </Text>
      {filter === 'upcoming' && (
        <Button
          mode="contained"
          onPress={() => navigation.navigate('BookAppointment')}
          style={styles.bookButton}
        >
          Book Appointment
        </Button>
      )}
    </View>
  );

  const renderStatistics = () => {
    const upcoming = appointments.filter(
      apt => apt.status === AppointmentStatus.SCHEDULED
    ).length;
    const completed = appointments.filter(
      apt => apt.status === AppointmentStatus.COMPLETED
    ).length;
    const cancelled = appointments.filter(
      apt => apt.status === AppointmentStatus.CANCELLED
    ).length;

    return (
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: '#2196F3' }]}>{upcoming}</Text>
          <Text style={styles.statLabel}>Upcoming</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: '#4CAF50' }]}>{completed}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: '#F44336' }]}>{cancelled}</Text>
          <Text style={styles.statLabel}>Cancelled</Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Title style={styles.title}>My Appointments</Title>
          {renderStatistics()}
        </View>

        <Searchbar
          placeholder="Search by dentist, type, or notes..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          icon="magnify"
        />

        <SegmentedButtons
          value={filter}
          onValueChange={setFilter}
          buttons={[
            { value: 'upcoming', label: 'Upcoming' },
            { value: 'past', label: 'Past' },
            { value: 'cancelled', label: 'Cancelled' },
          ]}
          style={styles.filterButtons}
        />

        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.typeFilterContainer}
        >
          <Chip
            selected={appointmentTypeFilter === 'all'}
            onPress={() => setAppointmentTypeFilter('all')}
            style={styles.typeFilterChip}
            mode="outlined"
          >
            All Types
          </Chip>
          {APPOINTMENT_TYPES.map(type => (
            <Chip
              key={type.value}
              selected={appointmentTypeFilter === type.value}
              onPress={() => setAppointmentTypeFilter(type.value)}
              style={styles.typeFilterChip}
              mode="outlined"
              icon={type.icon}
            >
              {type.label}
            </Chip>
          ))}
        </ScrollView>

        {filteredAppointments.length > 0 ? (
          <View style={styles.appointmentsList}>
            {filteredAppointments.map(renderAppointmentCard)}
          </View>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>


    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: '#e0e0e0',
  },
  searchBar: {
    marginHorizontal: 16,
    marginBottom: 8,
    elevation: 0,
    backgroundColor: '#fff',
  },
  filterButtons: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  appointmentsList: {
    padding: 16,
  },
  appointmentCard: {
    marginBottom: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateBox: {
    width: 50,
    height: 50,
    backgroundColor: '#2874ba',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dayText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  monthText: {
    color: '#fff',
    fontSize: 12,
  },
  appointmentInfo: {
    flex: 1,
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  timeText: {
    fontSize: 14,
    color: '#333',
    marginTop: 2,
  },
  dentistText: {
    fontSize: 14,
    color: '#2874ba',
    marginTop: 2,
  },
  statusContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 4,
  },
  typeChip: {
    height: 24,
    marginBottom: 4,
  },
  statusChip: {
    height: 24,
  },
  typeFilterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  typeFilterChip: {
    marginRight: 8,
  },
  notesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 8,
  },
  notesText: {
    fontSize: 12,
    color: '#555',
    marginLeft: 8,
    flex: 1,
  },
  divider: {
    marginVertical: 12,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  bookButton: {
    marginTop: 24,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});
