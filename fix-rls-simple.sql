-- Drop all existing policies on users table
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Staff and admin can view all users" ON public.users;
DROP POLICY IF EXISTS "Admin can manage all users" ON public.users;
DROP POLICY IF EXISTS "Allow user creation" ON public.users;

-- Create very simple policies that avoid recursion
-- Policy 1: Anyone authenticated can SELECT (we'll filter in the app based on role)
CREATE POLICY "Authenticated users can view users" ON public.users
    FOR SELECT USING (auth.uid() IS NOT NULL);

-- Policy 2: Users can update their own profile  
CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Policy 3: Allow INSERT for new user creation
CREATE POLICY "Allow user creation" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Policy 4: Users can delete their own profile (if needed)
CREATE POLICY "Users can delete their own profile" ON public.users
    FOR DELETE USING (auth.uid() = id);
