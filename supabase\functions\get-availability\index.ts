// Supabase Edge Function: get-availability
// Returns booked time ranges for a given date and dentist, using service role to bypass R<PERSON> safely.
// Input JSON: { date: 'YYYY-MM-DD', dentist_id: 'uuid' }
// Output JSON: { slots: Array<{ start: string; end: string }> }
// NOTE: Requires SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY to be set as function secrets.

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

type Body = { date?: string; dentist_id?: string };

type ApptRow = {
  start_time?: string | null;
  end_time?: string | null;
  appointment_time?: string | null;
  appointment_date?: string | null;
  duration_minutes?: number | null;
  status?: string | null;
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: { ...corsHeaders } });
  }

  try {
    const { date, dentist_id }: Body = await req.json();
    if (!date) throw new Error("date (YYYY-MM-DD) is required");
    if (!dentist_id) throw new Error("dentist_id is required");

    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const serviceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    if (!supabaseUrl || !serviceKey) throw new Error("Missing service configuration");

    const admin = createClient(supabaseUrl, serviceKey, {
      auth: { autoRefreshToken: false, persistSession: false },
    });

    // Fetch all non-cancelled appointments for the given date/dentist
    const { data, error } = await admin
      .from("appointments")
      .select("start_time, end_time, appointment_time, appointment_date, duration_minutes, status")
      .or(`date.eq.${date},appointment_date.eq.${date}`)
      .eq("dentist_id", dentist_id)
      .not("status", "in", "(cancelled,no_show)");

    if (error) throw error;

    const slots = (data as ApptRow[]).map((apt) => {
      const start = (apt.start_time || apt.appointment_time || "").slice(0, 5);
      const end = (
        apt.end_time ||
        ((): string => {
          const dur = apt.duration_minutes ?? 60;
          // compute end = start + dur
          const [h, m] = (start || "00:00").split(":" ).map((n) => parseInt(n, 10));
          const startMin = h * 60 + m;
          const endMin = startMin + dur;
          const eh = Math.floor(endMin / 60) % 24;
          const em = endMin % 60;
          const pad = (n: number) => n.toString().padStart(2, "0");
          return `${pad(eh)}:${pad(em)}`;
        })()
      ).slice(0, 5);
      return { start, end };
    }).filter(s => s.start);

    return new Response(JSON.stringify({ slots }), {
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  } catch (e) {
    return new Response(JSON.stringify({ error: e.message || String(e) }), {
      status: 400,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }
});

