import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  useTheme,
  Chip,
  IconButton,
  List,
  Divider,
  Portal,
  Modal,

  SegmentedButtons,
  Surface,
  Avatar,
  Badge,
  Switch,
  TextInput,
  RadioButton,
  ActivityIndicator,
  ProgressBar,
  Searchbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { Calendar, CalendarList, Agenda } from 'react-native-calendars';
import { supabase } from '../../../lib/supabase';
import { LoadingOverlay } from '../../components/common/LoadingOverlay';
import { CalendarSkeleton, AppointmentCardSkeleton } from '../../components/common/SkeletonLoaders';
import { useAuth } from '../../contexts/AuthContext';
import {
  format,
  parseISO,
  addDays,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isToday,
  isTomorrow,
  isPast,
  isFuture,
  addMinutes,
  differenceInMinutes,
  startOfDay,
  endOfDay,
  setHours,
  setMinutes,
} from 'date-fns';

interface Appointment {
  id: string;
  patient_id: string;
  patient_name?: string;
  patient_phone?: string;
  patient_email?: string;
  appointment_date: string;
  appointment_time: string;
  appointment_type: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  notes?: string;
  duration?: number; // in minutes
}

interface TimeSlot {
  time: string;
  available: boolean;
  appointment?: Appointment;
  reason?: 'break' | 'off-hours';
}

interface Patient {
  id: string;
  name: string;
  email: string;
  phone?: string;
}

interface DaySchedule {
  date: string;
  appointments: Appointment[];
  totalAppointments: number;
  completedAppointments: number;
  workingHours: {
    start: string;
    end: string;
    breakStart?: string;
    breakEnd?: string;
  };
}

interface ScheduleStats {
  todayTotal: number;
  todayCompleted: number;
  weekTotal: number;
  monthTotal: number;
  completionRate: number;
  averageDailyAppointments: number;
}

interface Availability {
  dayOfWeek: string;
  isAvailable: boolean;
  startTime: string;
  endTime: string;
  breakStartTime?: string;
  breakEndTime?: string;
}

const APPOINTMENT_TYPES = [
  { value: 'checkup', label: 'Regular Checkup', duration: 30, color: '#4CAF50' },
  { value: 'cleaning', label: 'Teeth Cleaning', duration: 45, color: '#2196F3' },
  { value: 'filling', label: 'Filling', duration: 60, color: '#FF9800' },
  { value: 'extraction', label: 'Tooth Extraction', duration: 45, color: '#F44336' },
  { value: 'root_canal', label: 'Root Canal', duration: 90, color: '#9C27B0' },
  { value: 'crown', label: 'Crown', duration: 60, color: '#E91E63' },
  { value: 'orthodontics', label: 'Orthodontics', duration: 30, color: '#00BCD4' },
  { value: 'consultation', label: 'Consultation', duration: 20, color: '#795548' },
  { value: 'emergency', label: 'Emergency', duration: 60, color: '#FF5722' },
];

const TIME_SLOTS = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30',
];

const DAYS_OF_WEEK = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const DentistSchedule = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month'>('day');
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [availabilityModalVisible, setAvailabilityModalVisible] = useState(false);
  const [blockTimeModalVisible, setBlockTimeModalVisible] = useState(false);
  const [notesModalVisible, setNotesModalVisible] = useState(false);

  const [daySchedule, setDaySchedule] = useState<DaySchedule | null>(null);
  const [weekSchedule, setWeekSchedule] = useState<DaySchedule[]>([]);
  const [availability, setAvailability] = useState<Availability[]>([]);
  const [stats, setStats] = useState<ScheduleStats>({
    todayTotal: 0,
    todayCompleted: 0,
    weekTotal: 0,
    monthTotal: 0,
    completionRate: 0,
    averageDailyAppointments: 0,
  });

  const [blockTimeData, setBlockTimeData] = useState({
    date: '',
    startTime: '',
    endTime: '',
    reason: '',
    recurring: false,
  });

  const [appointmentNotes, setAppointmentNotes] = useState('');

  // Quick booking state
  const [bookingModalVisible, setBookingModalVisible] = useState(false);
  const [bookingSlot, setBookingSlot] = useState<{ date: string; time: string } | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [patientSearch, setPatientSearch] = useState('');
  const [selectedPatientId, setSelectedPatientId] = useState<string>('');


  useEffect(() => {
    if (user) {
      fetchAppointments();
      fetchAvailability();
      fetchPatients();
    }
  }, [user, selectedDate, viewMode]);

  useEffect(() => {
    calculateStats();
    if (viewMode === 'day') {
      generateDaySchedule();
    } else if (viewMode === 'week') {
      generateWeekSchedule();
    }
  }, [appointments, selectedDate, viewMode]);

  const fetchAppointments = async () => {
    if (!user) return;

    setLoading(true);
    try {
      let startDate = selectedDate;
      let endDate = selectedDate;

      if (viewMode === 'week') {
        startDate = format(startOfWeek(parseISO(selectedDate)), 'yyyy-MM-dd');
        endDate = format(endOfWeek(parseISO(selectedDate)), 'yyyy-MM-dd');
      } else if (viewMode === 'month') {
        const date = parseISO(selectedDate);
        startDate = format(new Date(date.getFullYear(), date.getMonth(), 1), 'yyyy-MM-dd');
        endDate = format(new Date(date.getFullYear(), date.getMonth() + 1, 0), 'yyyy-MM-dd');
      }

      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          patient:patient_id (
            id,
            name,
            email,
            phone
          )
        `)
        .eq('dentist_id', user.id)
        .gte('appointment_date', startDate)
        .lte('appointment_date', endDate)
        .order('appointment_date', { ascending: true })
        .order('appointment_time', { ascending: true });

      if (error) throw error;

      const mappedData = data?.map(apt => ({
        ...apt,
        patient_name: apt.patient?.name,
        patient_phone: apt.patient?.phone,
        patient_email: apt.patient?.email,
        duration: APPOINTMENT_TYPES.find(t => t.value === apt.appointment_type)?.duration || 30,
      })) || [];

      setAppointments(mappedData);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      Alert.alert('Error', 'Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, name, email, phone')
        .eq('role', 'patient')
        .order('name');
      if (error) throw error;
      setPatients(data || []);
    } catch (e) {
      console.error('Error fetching patients:', e);
    }
  };

  const fetchAvailability = async () => {
    if (!user) return;

    try {
      // In a real app, this would fetch from a dentist_availability table
      // For now, we'll use default working hours
      const defaultAvailability: Availability[] = [
        { dayOfWeek: 'Monday', isAvailable: true, startTime: '09:00', endTime: '18:00', breakStartTime: '13:00', breakEndTime: '14:00' },
        { dayOfWeek: 'Tuesday', isAvailable: true, startTime: '09:00', endTime: '18:00', breakStartTime: '13:00', breakEndTime: '14:00' },
        { dayOfWeek: 'Wednesday', isAvailable: true, startTime: '09:00', endTime: '18:00', breakStartTime: '13:00', breakEndTime: '14:00' },
        { dayOfWeek: 'Thursday', isAvailable: true, startTime: '09:00', endTime: '18:00', breakStartTime: '13:00', breakEndTime: '14:00' },
        { dayOfWeek: 'Friday', isAvailable: true, startTime: '09:00', endTime: '14:00' },
        { dayOfWeek: 'Saturday', isAvailable: true, startTime: '09:00', endTime: '14:00' },
        { dayOfWeek: 'Sunday', isAvailable: false, startTime: '09:00', endTime: '18:00' },
      ];

      setAvailability(defaultAvailability);
    } catch (error) {
      console.error('Error fetching availability:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchAppointments();
    setRefreshing(false);
  };

  const generateDaySchedule = () => {
    const dayAppointments = appointments.filter(apt => apt.appointment_date === selectedDate);
    const completed = dayAppointments.filter(apt => apt.status === 'completed').length;

    const dayOfWeek = format(parseISO(selectedDate), 'EEEE');
    const dayAvailability = availability.find(a => a.dayOfWeek === dayOfWeek);

    setDaySchedule({
      date: selectedDate,
      appointments: dayAppointments,
      totalAppointments: dayAppointments.length,
      completedAppointments: completed,
      workingHours: {
        start: dayAvailability?.startTime || '09:00',
        end: dayAvailability?.endTime || '18:00',
        breakStart: dayAvailability?.breakStartTime,
        breakEnd: dayAvailability?.breakEndTime,
      },
    });
  };

  const generateWeekSchedule = () => {
    const weekStart = startOfWeek(parseISO(selectedDate));
    const weekEnd = endOfWeek(parseISO(selectedDate));
    const days = eachDayOfInterval({ start: weekStart, end: weekEnd });

    const weekData = days.map(day => {
      const dateStr = format(day, 'yyyy-MM-dd');
      const dayAppointments = appointments.filter(apt => apt.appointment_date === dateStr);
      const completed = dayAppointments.filter(apt => apt.status === 'completed').length;
      const dayOfWeek = format(day, 'EEEE');
      const dayAvailability = availability.find(a => a.dayOfWeek === dayOfWeek);

      return {
        date: dateStr,
        appointments: dayAppointments,
        totalAppointments: dayAppointments.length,
        completedAppointments: completed,
        workingHours: {
          start: dayAvailability?.startTime || '09:00',
          end: dayAvailability?.endTime || '18:00',
          breakStart: dayAvailability?.breakStartTime,
          breakEnd: dayAvailability?.breakEndTime,
        },
      };
    });

    setWeekSchedule(weekData);
  };

  const calculateStats = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const todayAppointments = appointments.filter(apt => apt.appointment_date === today);
    const todayCompleted = todayAppointments.filter(apt => apt.status === 'completed').length;

    const weekStart = startOfWeek(new Date());
    const weekEnd = endOfWeek(new Date());
    const weekAppointments = appointments.filter(apt => {
      const date = parseISO(apt.appointment_date);
      return date >= weekStart && date <= weekEnd;
    });

    const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
    const monthEnd = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0);
    const monthAppointments = appointments.filter(apt => {
      const date = parseISO(apt.appointment_date);
      return date >= monthStart && date <= monthEnd;
    });

    const totalCompleted = appointments.filter(apt => apt.status === 'completed').length;
    const completionRate = appointments.length > 0 ? (totalCompleted / appointments.length) * 100 : 0;

    const uniqueDays = new Set(appointments.map(apt => apt.appointment_date)).size;
    const avgDaily = uniqueDays > 0 ? appointments.length / uniqueDays : 0;

    setStats({
      todayTotal: todayAppointments.length,
      todayCompleted,
      weekTotal: weekAppointments.length,
      monthTotal: monthAppointments.length,
      completionRate,
      averageDailyAppointments: Math.round(avgDaily * 10) / 10,
    });
  };

  const generateTimeSlots = (): TimeSlot[] => {
    if (!daySchedule) return [];

    const slots: TimeSlot[] = [];
    const dayAppointments = daySchedule.appointments;

    const toMinutes = (t: string) => {
      const [h, m] = t.split(':').map(Number);
      return h * 60 + m;
    };

    TIME_SLOTS.forEach(time => {
      const tMin = toMinutes(time);
      // Match appointments that span across multiple slots by duration
      const appointment = dayAppointments.find(apt => {
        const start = toMinutes(apt.appointment_time);
        const duration = apt.duration || 30; // fallback to 30 minutes
        const end = start + duration;
        return tMin >= start && tMin < end;
      });

      const isBreakTime =
        daySchedule.workingHours.breakStart &&
        daySchedule.workingHours.breakEnd &&
        time >= daySchedule.workingHours.breakStart &&
        time < daySchedule.workingHours.breakEnd;

      const isOutsideWorkingHours =
        time < daySchedule.workingHours.start ||
        time >= daySchedule.workingHours.end;

      slots.push({
        time,
        available: !appointment && !isBreakTime && !isOutsideWorkingHours,
        appointment,
        reason: appointment ? undefined : (isBreakTime ? 'break' : (isOutsideWorkingHours ? 'off-hours' : undefined)),
      });
    });

    return slots;
  };

  const handleStatusChange = async (appointment: Appointment, newStatus: Appointment['status']) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', appointment.id);

      if (error) throw error;

      Alert.alert('Success', 'Appointment status updated');
      fetchAppointments();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to update status');
    }
  };

  const handleSaveNotes = async () => {
    if (!selectedAppointment) return;

    try {
      const { error } = await supabase
        .from('appointments')
        .update({
          notes: appointmentNotes,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedAppointment.id);

      if (error) throw error;

      Alert.alert('Success', 'Notes saved successfully');
      setNotesModalVisible(false);
      fetchAppointments();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save notes');
    }
  };

  const handleBlockTime = async () => {
    // In a real app, this would create a blocked time slot in the database
    Alert.alert('Success', 'Time blocked successfully');
    setBlockTimeModalVisible(false);
    setBlockTimeData({
      date: '',
      startTime: '',
      endTime: '',
      reason: '',
      recurring: false,
    });
  };

  const updateAvailability = async (day: string, updates: Partial<Availability>) => {
    // In a real app, this would update the dentist_availability table
    const updatedAvailability = availability.map(a =>
      a.dayOfWeek === day ? { ...a, ...updates } : a
    );
    setAvailability(updatedAvailability);
    Alert.alert('Success', 'Availability updated');
  };

  const getAppointmentTypeInfo = (type: string) => {
    return APPOINTMENT_TYPES.find(t => t.value === type) || APPOINTMENT_TYPES[0];
  };

  const getMarkedDates = () => {
    const marked: any = {};

    appointments.forEach(apt => {
      const date = apt.appointment_date;
      if (!marked[date]) {
        marked[date] = { dots: [] };
      }

      const typeInfo = getAppointmentTypeInfo(apt.appointment_type);
      marked[date].dots.push({
        color: apt.status === 'completed' ? '#4CAF50' :
               apt.status === 'cancelled' ? '#F44336' :
               typeInfo.color,
      });
    });

    // Mark selected date
    if (selectedDate) {
      marked[selectedDate] = {
        ...marked[selectedDate],
        selected: true,
        selectedColor: theme.colors.primary,
      };
    }

    return marked;
  };


  const handleCreateAppointmentFromSlot = async () => {
    if (!bookingSlot) return;
    if (!selectedPatientId) {
      Alert.alert('Select patient', 'Please choose a patient to book this slot.');
      return;
    }
    try {
      const { error } = await supabase
        .from('appointments')
        .insert([
          {
            patient_id: selectedPatientId,
            dentist_id: user?.id,
            appointment_date: bookingSlot.date,
            appointment_time: bookingSlot.time,
            appointment_type: 'consultation',
            status: 'scheduled',
          },
        ]);
      if (error) throw error;
      Alert.alert('Booked', 'Appointment created successfully');
      setBookingModalVisible(false);
      setSelectedPatientId('');
      setPatientSearch('');
      setBookingSlot(null);
      fetchAppointments();
    } catch (err: any) {
      Alert.alert('Error', err.message || 'Failed to create appointment');
    }
  };

  const renderBookingModal = () => {
    const q = patientSearch.trim().toLowerCase();
    const filtered = patients.filter(p =>
      (p.name || '').toLowerCase().includes(q) ||
      (p.email || '').toLowerCase().includes(q) ||
      (p.phone || '').toLowerCase().includes(q)
    );
    return (
      <Portal>
        <Modal
          visible={bookingModalVisible}
          onDismiss={() => setBookingModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Book Appointment</Title>
            {bookingSlot && (
              <Text style={{ marginBottom: 8 }}>
                {format(parseISO(bookingSlot.date), 'MMMM d, yyyy')} at {bookingSlot.time}
              </Text>
            )}
            <Searchbar
              placeholder="Search patient by name, email, or phone"
              value={patientSearch}
              onChangeText={setPatientSearch}
              style={{ marginVertical: 12 }}
            />
            {filtered.slice(0, 20).map(p => (
              <List.Item
                key={p.id}
                title={p.name}
                description={[p.email, p.phone].filter(Boolean).join(' • ')}
                onPress={() => setSelectedPatientId(p.id)}
                right={(props) => selectedPatientId === p.id ? <List.Icon {...props} icon="check" /> : null}
              />
            ))}
            <View style={styles.modalActions}>
              <Button mode="text" onPress={() => setBookingModalVisible(false)}>Cancel</Button>
              <Button mode="contained" onPress={handleCreateAppointmentFromSlot} disabled={!selectedPatientId || !bookingSlot}>Book</Button>
            </View>
          </ScrollView>
        </Modal>
      </Portal>
    );
  };

  const renderDayView = () => {
    const timeSlots = generateTimeSlots();

    return (
      <ScrollView style={styles.dayView}>

        <View style={styles.timeSlotContainer}>
          {timeSlots.map((slot, index) => (
            <Surface
              key={index}
              style={[
                styles.timeSlot,
                !slot.available && styles.unavailableSlot,
                slot.appointment && styles.bookedSlot,
              ]}
              elevation={slot.appointment ? 2 : 0}
            >
              <Text style={styles.slotTime}>{slot.time}</Text>

              {slot.appointment ? (
                <TouchableOpacity
                  style={styles.appointmentInfo}
                  onPress={() => {
                    setSelectedAppointment(slot.appointment!);
                    setDetailsModalVisible(true);
                  }}
                >
                  <View style={styles.appointmentHeader}>
                    <Text style={styles.patientName}>{slot.appointment.patient_name}</Text>
                    <Chip
                      style={[
                        styles.statusChip,
                        { backgroundColor: getAppointmentTypeInfo(slot.appointment.appointment_type).color + '20' }
                      ]}
                      textStyle={{
                        color: getAppointmentTypeInfo(slot.appointment.appointment_type).color,
                        fontSize: 10
                      }}
                    >
                      {getAppointmentTypeInfo(slot.appointment.appointment_type).label}
                    </Chip>
                  </View>

                  {slot.appointment.patient_phone && (
                    <Text style={styles.patientPhone}>📞 {slot.appointment.patient_phone}</Text>
                  )}

                  <View style={styles.appointmentActions}>
                    {slot.appointment.status === 'scheduled' && (
                      <>
                        <Button
                          mode="text"
                          compact
                          onPress={() => handleStatusChange(slot.appointment!, 'confirmed')}
                          textColor="#4CAF50"
                        >
                          Confirm
                        </Button>
                        <Button
                          mode="text"
                          compact
                          onPress={() => handleStatusChange(slot.appointment!, 'completed')}
                          textColor="#2196F3"
                        >
                          Complete
                        </Button>
                      </>
                    )}
                    <IconButton
                      icon="note-text"
                      size={16}
                      onPress={() => {
                        setSelectedAppointment(slot.appointment!);
                        setAppointmentNotes(slot.appointment?.notes || '');
                        setNotesModalVisible(true);
                      }}
                    />
                  </View>
                </TouchableOpacity>
              ) : slot.available ? (
                <TouchableOpacity
                  style={styles.availableSlot}
                  onPress={() => {
                    setBookingSlot({ date: selectedDate, time: slot.time });
                    setBookingModalVisible(true);
                  }}
                >
                  <Text style={styles.availableText}>Available</Text>
                  <Text style={{ fontSize: 11, color: '#999' }}>Tap to book</Text>
                </TouchableOpacity>
              ) : (
                <View style={styles.blockedSlot}>
                  <Text style={styles.blockedText}>{slot.reason === 'break' ? 'Break' : 'Off-hours'}</Text>
                </View>
              )}
            </Surface>
          ))}
        </View>
      </ScrollView>
    );
  };

  const renderWeekView = () => {
    return (
      <ScrollView style={styles.weekView}>
        {weekSchedule.map((day, index) => {
          const date = parseISO(day.date);
          const isToday = format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');

          return (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setSelectedDate(day.date);
                setViewMode('day');
              }}
            >
              <Surface
                style={[
                  styles.weekDayCard,
                  isToday && styles.todayCard,
                ]}
                elevation={1}
              >
                <View style={styles.weekDayHeader}>
                  <Text style={[styles.weekDayName, isToday && styles.todayText]}>
                    {format(date, 'EEEE')}
                  </Text>
                  <Text style={[styles.weekDayDate, isToday && styles.todayText]}>
                    {format(date, 'MMM d')}
                  </Text>
                </View>

                <View style={styles.weekDayStats}>
                  <Badge style={styles.weekBadge}>{day.totalAppointments}</Badge>
                  {day.completedAppointments > 0 && (
                    <Badge style={[styles.weekBadge, { backgroundColor: '#4CAF50' }]}>
                      {`✓ ${day.completedAppointments}`}
                    </Badge>
                  )}
                </View>

                <View style={styles.weekAppointments}>
                  {day.appointments.slice(0, 3).map((apt, aptIndex) => (
                    <View key={aptIndex} style={styles.weekAppointmentItem}>
                      <Text style={styles.weekAppointmentTime}>{apt.appointment_time}</Text>
                      <Text style={styles.weekAppointmentName} numberOfLines={1}>
                        {apt.patient_name}
                      </Text>
                    </View>
                  ))}
                  {day.appointments.length > 3 && (
                    <Text style={styles.moreAppointments}>
                      +{day.appointments.length - 3} more
                    </Text>
                  )}
                </View>
              </Surface>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    );
  };

  const renderMonthView = () => {
    return (
      <View style={styles.monthView}>
        <Calendar
          current={selectedDate}
          onDayPress={(day) => {
            setSelectedDate(day.dateString);
            setViewMode('day');
          }}
          markedDates={getMarkedDates()}
          markingType="multi-dot"
          theme={{
            selectedDayBackgroundColor: theme.colors.primary,
            todayTextColor: theme.colors.primary,
            arrowColor: theme.colors.primary,
          }}
        />

        <View style={styles.monthStats}>
          <Surface style={styles.monthStatCard} elevation={1}>
            <Text style={styles.monthStatValue}>{stats.monthTotal}</Text>
            <Text style={styles.monthStatLabel}>Total Appointments</Text>
          </Surface>
          <Surface style={styles.monthStatCard} elevation={1}>
            <Text style={styles.monthStatValue}>{stats.averageDailyAppointments}</Text>
            <Text style={styles.monthStatLabel}>Daily Average</Text>
          </Surface>
        </View>
      </View>
    );
  };

  const renderAvailabilityModal = () => {
    return (
      <Portal>
        <Modal
          visible={availabilityModalVisible}
          onDismiss={() => setAvailabilityModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <ScrollView>
            <Title style={styles.modalTitle}>Manage Availability</Title>

            {DAYS_OF_WEEK.map((day, index) => {
              const dayAvailability = availability.find(a => a.dayOfWeek === day);

              return (
                <Surface key={index} style={styles.availabilityDay} elevation={1}>
                  <View style={styles.availabilityHeader}>
                    <Text style={styles.availabilityDayName}>{day}</Text>
                    <Switch
                      value={dayAvailability?.isAvailable || false}
                      onValueChange={(value) =>
                        updateAvailability(day, { isAvailable: value })
                      }
                      color={theme.colors.primary}
                    />
                  </View>

                  {dayAvailability?.isAvailable && (
                    <View style={styles.availabilityTimes}>
                      <View style={styles.timeRow}>
                        <TextInput
                          label="Start Time"
                          value={dayAvailability.startTime}
                          onChangeText={(text) =>
                            updateAvailability(day, { startTime: text })
                          }
                          mode="outlined"
                          style={styles.timeInput}
                        />
                        <TextInput
                          label="End Time"
                          value={dayAvailability.endTime}
                          onChangeText={(text) =>
                            updateAvailability(day, { endTime: text })
                          }
                          mode="outlined"
                          style={styles.timeInput}
                        />
                      </View>

                      <View style={styles.timeRow}>
                        <TextInput
                          label="Break Start"
                          value={dayAvailability.breakStartTime || ''}
                          onChangeText={(text) =>
                            updateAvailability(day, { breakStartTime: text })
                          }
                          mode="outlined"
                          style={styles.timeInput}
                          placeholder="Optional"
                        />
                        <TextInput
                          label="Break End"
                          value={dayAvailability.breakEndTime || ''}
                          onChangeText={(text) =>
                            updateAvailability(day, { breakEndTime: text })
                          }
                          mode="outlined"
                          style={styles.timeInput}
                          placeholder="Optional"
                        />
                      </View>
                    </View>
                  )}
                </Surface>
              );
            })}

            <Button
              mode="contained"
              onPress={() => setAvailabilityModalVisible(false)}
              style={styles.modalButton}
            >
              Save Changes
            </Button>
          </ScrollView>
        </Modal>
      </Portal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingOverlay message="Loading schedule..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Title style={styles.title}>My Schedule</Title>
        <IconButton
          icon="cog"
          onPress={() => setAvailabilityModalVisible(true)}
        />
      </View>

      {/* Stats Bar */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.statsContainer}
      >
        <Surface style={[styles.statCard, { backgroundColor: '#E3F2FD' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.todayTotal}</Text>
          <Text style={styles.statLabel}>Today</Text>
          <ProgressBar
            progress={stats.todayTotal > 0 ? stats.todayCompleted / stats.todayTotal : 0}
            color="#4CAF50"
            style={styles.progressBar}
          />
        </Surface>

        <Surface style={[styles.statCard, { backgroundColor: '#E8F5E9' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.weekTotal}</Text>
          <Text style={styles.statLabel}>This Week</Text>
        </Surface>

        <Surface style={[styles.statCard, { backgroundColor: '#FFF3E0' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.completionRate.toFixed(0)}%</Text>
          <Text style={styles.statLabel}>Completion</Text>
        </Surface>

        <Surface style={[styles.statCard, { backgroundColor: '#F3E5F5' }]} elevation={1}>
          <Text style={styles.statValue}>{stats.averageDailyAppointments}</Text>
          <Text style={styles.statLabel}>Daily Avg</Text>
        </Surface>
      </ScrollView>



      {/* Content based on view mode */}
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        style={styles.content}
      >
        {viewMode === 'day' && renderDayView()}
        {viewMode === 'week' && renderWeekView()}
        {viewMode === 'month' && renderMonthView()}
      </ScrollView>



      {/* Modals */}
      {renderAvailabilityModal()}
      {renderBookingModal()}


      {/* Appointment Details Modal */}
      <Portal>
        <Modal
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          {selectedAppointment && (
            <ScrollView>
              <Title style={styles.modalTitle}>Appointment Details</Title>

              <List.Section>
                <List.Item
                  title="Patient"
                  description={selectedAppointment.patient_name}
                  left={props => <List.Icon {...props} icon="account" />}
                />
                <List.Item
                  title="Date & Time"
                  description={`${format(parseISO(selectedAppointment.appointment_date), 'MMMM d, yyyy')} at ${selectedAppointment.appointment_time}`}
                  left={props => <List.Icon {...props} icon="calendar-clock" />}
                />
                <List.Item
                  title="Type"
                  description={getAppointmentTypeInfo(selectedAppointment.appointment_type).label}
                  left={props => <List.Icon {...props} icon="tooth" />}
                />
                <List.Item
                  title="Duration"
                  description={`${selectedAppointment.duration} minutes`}
                  left={props => <List.Icon {...props} icon="timer" />}
                />
                {selectedAppointment.patient_phone && (
                  <List.Item
                    title="Phone"
                    description={selectedAppointment.patient_phone}
                    left={props => <List.Icon {...props} icon="phone" />}
                  />
                )}
                {selectedAppointment.notes && (
                  <List.Item
                    title="Notes"
                    description={selectedAppointment.notes}
                    left={props => <List.Icon {...props} icon="note-text" />}
                  />
                )}
              </List.Section>

              <View style={styles.modalActions}>
                {selectedAppointment.status !== 'completed' && (
                  <Button
                    mode="contained"
                    onPress={() => {
                      handleStatusChange(selectedAppointment, 'completed');
                      setDetailsModalVisible(false);
                    }}
                    style={styles.actionButton}
                  >
                    Mark Complete
                  </Button>
                )}
                <Button
                  mode="outlined"
                  onPress={() => {
                    setAppointmentNotes(selectedAppointment.notes || '');
                    setDetailsModalVisible(false);
                    setNotesModalVisible(true);
                  }}
                  style={styles.actionButton}
                >
                  Add Notes
                </Button>
              </View>
            </ScrollView>
          )}
        </Modal>
      </Portal>

      {/* Notes Modal */}
      <Portal>
        <Modal
          visible={notesModalVisible}
          onDismiss={() => setNotesModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <Title style={styles.modalTitle}>Appointment Notes</Title>
          <TextInput
            label="Notes"
            value={appointmentNotes}
            onChangeText={setAppointmentNotes}
            mode="outlined"
            multiline
            numberOfLines={6}
            style={styles.notesInput}
          />
          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setNotesModalVisible(false)}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveNotes}
            >
              Save Notes
            </Button>
          </View>
        </Modal>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    elevation: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statsContainer: {
    padding: 16,
    maxHeight: 120,
  },
  statCard: {
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    minWidth: 100,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  progressBar: {
    width: '100%',
    marginTop: 8,
    height: 4,
    borderRadius: 2,
  },
  viewSelector: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  navigationControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff',
    elevation: 1,
  },
  navigationDate: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  dayView: {
    padding: 16,
  },
  dayHeader: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  dayDate: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  dayStats: {
    flexDirection: 'row',
    gap: 8,
  },
  statChip: {
    backgroundColor: '#2196F3',
  },
  timeSlotContainer: {
    gap: 8,
  },
  timeSlot: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  unavailableSlot: {
    backgroundColor: '#f5f5f5',
  },
  bookedSlot: {
    backgroundColor: '#fff',
  },
  slotTime: {
    width: 60,
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  appointmentInfo: {
    flex: 1,
    marginLeft: 12,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  patientName: {
    fontSize: 14,
    fontWeight: 'bold',
    flex: 1,
  },
  statusChip: {
    height: 20,
  },
  patientPhone: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  appointmentActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  availableSlot: {
    flex: 1,
    justifyContent: 'center',
    marginLeft: 12,
  },
  availableText: {
    color: '#4CAF50',
    fontSize: 12,
  },
  blockedSlot: {
    flex: 1,
    justifyContent: 'center',
    marginLeft: 12,
  },
  blockedText: {
    color: '#999',
    fontSize: 12,
  },
  weekView: {
    padding: 16,
  },
  weekDayCard: {
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  todayCard: {
    borderColor: '#2196F3',
    borderWidth: 2,
  },
  weekDayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  weekDayName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  weekDayDate: {
    fontSize: 14,
    color: '#666',
  },
  todayText: {
    color: '#2196F3',
  },
  weekDayStats: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  weekBadge: {
    backgroundColor: '#2196F3',
  },
  weekAppointments: {
    marginTop: 8,
  },
  weekAppointmentItem: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  weekAppointmentTime: {
    width: 50,
    fontSize: 12,
    color: '#666',
  },
  weekAppointmentName: {
    flex: 1,
    fontSize: 12,
  },
  moreAppointments: {
    fontSize: 11,
    color: '#999',
    fontStyle: 'italic',
    marginTop: 4,
  },
  monthView: {
    padding: 16,
  },
  monthStats: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  monthStatCard: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  monthStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  monthStatLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  modal: {
    backgroundColor: '#fff',
    padding: 20,
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  availabilityDay: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
  },
  availabilityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availabilityDayName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  availabilityTimes: {
    marginTop: 12,
  },
  timeRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 8,
  },
  timeInput: {
    flex: 1,
  },
  modalButton: {
    marginTop: 16,
  },
  notesInput: {
    marginVertical: 16,
  },
});
