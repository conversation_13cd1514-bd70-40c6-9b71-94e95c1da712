-- Drop all existing policies on users table
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Staff and admin can view all users" ON public.users;
DROP POLICY IF EXISTS "Admin can manage all users" ON public.users;

-- Disable <PERSON><PERSON> temporarily to allow user creation
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create simpler, non-recursive policies
-- Policy 1: Users can view their own profile
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Policy 2: Users can update their own profile  
CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Policy 3: Allow INSERT for new user creation (this is the key fix)
CREATE POLICY "Allow user creation" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Policy 4: Staff and admin can view all users (using auth.jwt() instead of users table lookup)
CREATE POLICY "Staff and admin can view all users" ON public.users
    FOR SELECT USING (
        (auth.jwt() ->> 'user_metadata' ->> 'role') IN ('staff', 'admin')
        OR auth.uid() = id
    );

-- Policy 5: Admin can manage all users (using auth.jwt() instead of users table lookup)  
CREATE POLICY "Admin can manage all users" ON public.users
    FOR ALL USING (
        (auth.jwt() ->> 'user_metadata' ->> 'role') = 'admin'
        OR auth.uid() = id
    );
