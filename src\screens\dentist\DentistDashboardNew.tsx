import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../../lib/supabase';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, isToday, isTomorrow, parseISO } from 'date-fns';
import { NavigationProp } from '@react-navigation/native';
import { Appointment } from '../../types';

interface DentistDashboardProps {
  navigation: NavigationProp<any>;
}

interface DashboardStats {
  todayCount: number;
  weekCount: number;
  pendingCount: number;
  completedToday: number;
}

const DentistDashboardNew: React.FC<DentistDashboardProps> = ({ navigation }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [todayAppointments, setTodayAppointments] = useState<Appointment[]>([]);
  const [upcomingAppointments, setUpcomingAppointments] = useState<Appointment[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    todayCount: 0,
    weekCount: 0,
    pendingCount: 0,
    completedToday: 0,
  });

  const fetchDashboardData = useCallback(async () => {
    if (!user?.id) return;

    try {
      const now = new Date();
      const todayStart = startOfDay(now);
      const todayEnd = endOfDay(now);
      const weekStart = startOfWeek(now);
      const weekEnd = endOfWeek(now);

      // Fetch today's appointments
      const { data: todayData, error: todayError } = await supabase
        .from('appointments')
        .select(`
          *,
          patient:users!appointments_patient_id_fkey(id, email, phone, name)
        `)
        .eq('dentist_id', user.id)
        .gte('date', todayStart.toISOString())
        .lte('date', todayEnd.toISOString())
        .order('start_time', { ascending: true });

      if (todayError) throw todayError;

      // Fetch upcoming appointments (next 7 days)
      const { data: upcomingData, error: upcomingError } = await supabase
        .from('appointments')
        .select(`
          *,
          patient:users!appointments_patient_id_fkey(id, email, phone, name)
        `)
        .eq('dentist_id', user.id)
        .gt('date', todayEnd.toISOString())
        .lte('date', new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString())
        .eq('status', 'scheduled')
        .order('date', { ascending: true })
        .order('start_time', { ascending: true })
        .limit(10);

      if (upcomingError) throw upcomingError;

      // Fetch stats
      const { data: weekData, error: weekError } = await supabase
        .from('appointments')
        .select('status')
        .eq('dentist_id', user.id)
        .gte('date', weekStart.toISOString())
        .lte('date', weekEnd.toISOString());

      if (weekError) throw weekError;

      const todayCompleted = todayData?.filter(apt => apt.status === 'completed').length || 0;
      const todayPending = todayData?.filter(apt => apt.status === 'scheduled').length || 0;

      setTodayAppointments(todayData || []);
      setUpcomingAppointments(upcomingData || []);
      setStats({
        todayCount: todayData?.length || 0,
        weekCount: weekData?.length || 0,
        pendingCount: todayPending,
        completedToday: todayCompleted,
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user?.id]);

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const onRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
  };

  const updateAppointmentStatus = async (appointmentId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .update({ status: newStatus, updated_at: new Date().toISOString() })
        .eq('id', appointmentId);

      if (error) throw error;

      Alert.alert('Success', `Appointment ${newStatus}`);
      fetchDashboardData();
    } catch (error) {
      console.error('Error updating appointment:', error);
      Alert.alert('Error', 'Failed to update appointment status');
    }
  };

  const handleAppointmentAction = (appointment: Appointment) => {
    Alert.alert(
      'Update Appointment',
      `Patient: ${appointment.patient?.name || 'Unknown'}`,
      [
        {
          text: 'Mark as Completed',
          onPress: () => updateAppointmentStatus(appointment.id, 'completed'),
        },
        {
          text: 'Mark as No-Show',
          onPress: () => updateAppointmentStatus(appointment.id, 'no_show'),
        },
        {
          text: 'View Details',
          onPress: () => navigation.navigate('AppointmentDetails', { appointmentId: appointment.id }),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const renderStatCard = (icon: string, label: string, value: number, color: string) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <MaterialIcons name={icon as any} size={24} color={color} />
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );

  const renderAppointmentItem = ({ item }: { item: Appointment }) => {
    const statusColors: { [key: string]: string } = {
      scheduled: '#4CAF50',
      completed: '#2196F3',
      cancelled: '#F44336',
      no_show: '#FF9800',
    };

    return (
      <TouchableOpacity
        style={styles.appointmentCard}
        onPress={() => handleAppointmentAction(item)}
      >
        <View style={styles.appointmentHeader}>
          <View style={styles.timeContainer}>
            <MaterialIcons name="access-time" size={16} color="#666" />
            <Text style={styles.appointmentTime}>
              {format(parseISO(`2000-01-01T${item.start_time}`), 'h:mm a')}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: statusColors[item.status] }]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>
        <Text style={styles.patientName}>{item.patient?.name || 'Unknown Patient'}</Text>
        <Text style={styles.appointmentType}>{item.appointment_type || 'General Checkup'}</Text>
        {item.patient?.phone && (
          <View style={styles.contactInfo}>
            <FontAwesome5 name="phone" size={12} color="#666" />
            <Text style={styles.phoneText}>{item.patient.phone}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderUpcomingItem = ({ item }: { item: Appointment }) => {
    const appointmentDate = parseISO(item.date);
    let dateLabel = format(appointmentDate, 'MMM dd');
    if (isTomorrow(appointmentDate)) {
      dateLabel = 'Tomorrow';
    }

    return (
      <TouchableOpacity
        style={styles.upcomingCard}
        onPress={() => navigation.navigate('AppointmentDetails', { appointmentId: item.id })}
      >
        <View style={styles.upcomingDate}>
          <Text style={styles.upcomingDateText}>{dateLabel}</Text>
          <Text style={styles.upcomingTimeText}>
            {format(parseISO(`2000-01-01T${item.start_time}`), 'h:mm a')}
          </Text>
        </View>
        <View style={styles.upcomingDetails}>
          <Text style={styles.upcomingPatient}>{item.patient?.name || 'Unknown'}</Text>
          <Text style={styles.upcomingType}>{item.appointment_type || 'General'}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading Dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.greeting}>Welcome, Dr. {user?.name || 'Doctor'}</Text>
          <Text style={styles.dateText}>{format(new Date(), 'EEEE, MMMM d, yyyy')}</Text>
        </View>

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          {renderStatCard('today', 'Today', stats.todayCount, '#007AFF')}
          {renderStatCard('event', 'This Week', stats.weekCount, '#4CAF50')}
          {renderStatCard('pending', 'Pending', stats.pendingCount, '#FF9800')}
          {renderStatCard('check-circle', 'Completed', stats.completedToday, '#2196F3')}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('DentistSchedule')}
          >
            <MaterialIcons name="calendar-today" size={24} color="#007AFF" />
            <Text style={styles.actionText}>My Schedule</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('PatientRecords')}
          >
            <MaterialIcons name="people" size={24} color="#007AFF" />
            <Text style={styles.actionText}>Patients</Text>
          </TouchableOpacity>
        </View>

        {/* Today's Appointments */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today's Appointments</Text>
            <Text style={styles.sectionCount}>{todayAppointments.length}</Text>
          </View>
          {todayAppointments.length > 0 ? (
            <FlatList
              data={todayAppointments}
              renderItem={renderAppointmentItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyState}>
              <MaterialIcons name="event-available" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No appointments scheduled for today</Text>
            </View>
          )}
        </View>

        {/* Upcoming Appointments */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming Appointments</Text>
            <TouchableOpacity onPress={() => navigation.navigate('DentistSchedule')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          {upcomingAppointments.length > 0 ? (
            <FlatList
              data={upcomingAppointments}
              renderItem={renderUpcomingItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
            />
          ) : (
            <View style={styles.emptyState}>
              <MaterialIcons name="event" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No upcoming appointments</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  dateText: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 10,
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    width: '48%',
    marginVertical: 5,
    alignItems: 'center',
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginVertical: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 15,
    backgroundColor: '#fff',
    marginVertical: 10,
  },
  actionButton: {
    alignItems: 'center',
    padding: 10,
  },
  actionText: {
    fontSize: 14,
    color: '#007AFF',
    marginTop: 5,
  },
  section: {
    marginVertical: 10,
    backgroundColor: '#fff',
    paddingVertical: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  sectionCount: {
    backgroundColor: '#007AFF',
    color: '#fff',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: 'bold',
    overflow: 'hidden',
  },
  viewAllText: {
    color: '#007AFF',
    fontSize: 14,
  },
  appointmentCard: {
    backgroundColor: '#f9f9f9',
    marginHorizontal: 20,
    marginVertical: 5,
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  appointmentTime: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginLeft: 5,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  patientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  appointmentType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  phoneText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 5,
  },
  upcomingCard: {
    flexDirection: 'row',
    backgroundColor: '#f9f9f9',
    marginHorizontal: 20,
    marginVertical: 5,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  upcomingDate: {
    marginRight: 15,
    alignItems: 'center',
    minWidth: 60,
  },
  upcomingDateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  upcomingTimeText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  upcomingDetails: {
    flex: 1,
  },
  upcomingPatient: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  upcomingType: {
    fontSize: 13,
    color: '#666',
    marginTop: 2,
  },
  emptyState: {
    alignItems: 'center',
    padding: 30,
  },
  emptyText: {
    fontSize: 14,
    color: '#999',
    marginTop: 10,
  },
});

export default DentistDashboardNew;
