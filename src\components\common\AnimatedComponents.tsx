import React, { useEffect, useRef, ReactNode } from 'react';
import {
  Animated,
  ViewStyle,
  Pressable,
  PressableProps,
  View,
} from 'react-native';
import { lightTheme } from '../../theme';

interface FadeInViewProps {
  children: ReactNode;
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

// Fade In Animation
export const FadeInView: React.FC<FadeInViewProps> = ({
  children,
  duration = 300,
  delay = 0,
  style,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration,
      delay,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim, duration, delay]);

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface SlideInViewProps {
  children: ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

// Slide In Animation
export const SlideInView: React.FC<SlideInViewProps> = ({
  children,
  direction = 'right',
  duration = 300,
  delay = 0,
  style,
}) => {
  const slideAnim = useRef(new Animated.Value(getInitialPosition(direction))).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  function getInitialPosition(dir: string): number {
    switch (dir) {
      case 'left':
        return -100;
      case 'right':
        return 100;
      case 'up':
        return -100;
      case 'down':
        return 100;
      default:
        return 100;
    }
  }

  useEffect(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration,
        delay,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration,
        delay,
        useNativeDriver: true,
      }),
    ]).start();
  }, [slideAnim, fadeAnim, duration, delay]);

  const getTransform = () => {
    if (direction === 'left' || direction === 'right') {
      return [{ translateX: slideAnim }];
    }
    return [{ translateY: slideAnim }];
  };

  return (
    <Animated.View
      style={[
        style,
        {
          opacity: fadeAnim,
          transform: getTransform(),
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface ScaleButtonProps extends PressableProps {
  children: ReactNode;
  scaleTo?: number;
  duration?: number;
  style?: ViewStyle;
}

// Scale Button with Press Animation
export const ScaleButton: React.FC<ScaleButtonProps> = ({
  children,
  scaleTo = 0.95,
  duration = 100,
  style,
  onPressIn,
  onPressOut,
  ...props
}) => {
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = (event: any) => {
    Animated.timing(scaleAnim, {
      toValue: scaleTo,
      duration,
      useNativeDriver: true,
    }).start();
    onPressIn?.(event);
  };

  const handlePressOut = (event: any) => {
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration,
      useNativeDriver: true,
    }).start();
    onPressOut?.(event);
  };

  return (
    <Pressable onPressIn={handlePressIn} onPressOut={handlePressOut} {...props}>
      <Animated.View
        style={[
          style,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {children}
      </Animated.View>
    </Pressable>
  );
};

interface PulseViewProps {
  children: ReactNode;
  duration?: number;
  style?: ViewStyle;
  minScale?: number;
  maxScale?: number;
}

// Pulse Animation
export const PulseView: React.FC<PulseViewProps> = ({
  children,
  duration = 1500,
  style,
  minScale = 0.95,
  maxScale = 1.05,
}) => {
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: maxScale,
          duration: duration / 2,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: minScale,
          duration: duration / 2,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [pulseAnim, duration, minScale, maxScale]);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ scale: pulseAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface RotateViewProps {
  children: ReactNode;
  duration?: number;
  style?: ViewStyle;
}

// Rotation Animation
export const RotateView: React.FC<RotateViewProps> = ({
  children,
  duration = 1000,
  style,
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      })
    ).start();
  }, [rotateAnim, duration]);

  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ rotate }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface ShakeViewProps {
  children: ReactNode;
  trigger?: boolean;
  duration?: number;
  style?: ViewStyle;
}

// Shake Animation (for errors)
export const ShakeView: React.FC<ShakeViewProps> = ({
  children,
  trigger = false,
  duration = 500,
  style,
}) => {
  const shakeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (trigger) {
      Animated.sequence([
        Animated.timing(shakeAnim, {
          toValue: 10,
          duration: duration / 6,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnim, {
          toValue: -10,
          duration: duration / 6,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnim, {
          toValue: 10,
          duration: duration / 6,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnim, {
          toValue: -10,
          duration: duration / 6,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnim, {
          toValue: 5,
          duration: duration / 6,
          useNativeDriver: true,
        }),
        Animated.timing(shakeAnim, {
          toValue: 0,
          duration: duration / 6,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [trigger, shakeAnim, duration]);

  return (
    <Animated.View
      style={[
        style,
        {
          transform: [{ translateX: shakeAnim }],
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface StaggeredListProps {
  children: ReactNode[];
  staggerDelay?: number;
  itemDuration?: number;
  style?: ViewStyle;
}

// Staggered List Animation
export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 100,
  itemDuration = 300,
  style,
}) => {
  return (
    <View style={style}>
      {React.Children.map(children, (child, index) => (
        <FadeInView
          key={index}
          delay={index * staggerDelay}
          duration={itemDuration}
        >
          <SlideInView
            direction="right"
            delay={index * staggerDelay}
            duration={itemDuration}
          >
            {child}
          </SlideInView>
        </FadeInView>
      ))}
    </View>
  );
};

interface CollapsibleViewProps {
  children: ReactNode;
  collapsed: boolean;
  duration?: number;
  style?: ViewStyle;
  maxHeight?: number;
}

// Collapsible View
export const CollapsibleView: React.FC<CollapsibleViewProps> = ({
  children,
  collapsed,
  duration = 300,
  style,
  maxHeight = 500,
}) => {
  const heightAnim = useRef(new Animated.Value(collapsed ? 0 : maxHeight)).current;
  const opacityAnim = useRef(new Animated.Value(collapsed ? 0 : 1)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(heightAnim, {
        toValue: collapsed ? 0 : maxHeight,
        duration,
        useNativeDriver: false,
      }),
      Animated.timing(opacityAnim, {
        toValue: collapsed ? 0 : 1,
        duration: duration / 2,
        useNativeDriver: true,
      }),
    ]).start();
  }, [collapsed, heightAnim, opacityAnim, duration, maxHeight]);

  return (
    <Animated.View
      style={[
        style,
        {
          maxHeight: heightAnim,
          opacity: opacityAnim,
          overflow: 'hidden',
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

interface ParallaxScrollViewProps {
  children: ReactNode;
  headerComponent: ReactNode;
  headerHeight?: number;
  style?: ViewStyle;
}

// Parallax Scroll Header
export const ParallaxScrollView: React.FC<ParallaxScrollViewProps> = ({
  children,
  headerComponent,
  headerHeight = 200,
  style,
}) => {
  const scrollY = useRef(new Animated.Value(0)).current;

  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, headerHeight],
    outputRange: [0, -headerHeight / 2],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, headerHeight / 2, headerHeight],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  return (
    <View style={[{ flex: 1 }, style]}>
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: headerHeight,
          transform: [{ translateY: headerTranslateY }],
          opacity: headerOpacity,
          zIndex: 1,
        }}
      >
        {headerComponent}
      </Animated.View>
      <Animated.ScrollView
        contentContainerStyle={{ paddingTop: headerHeight }}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
      >
        {children}
      </Animated.ScrollView>
    </View>
  );
};

interface ProgressBarAnimatedProps {
  progress: number;
  duration?: number;
  height?: number;
  color?: string;
  backgroundColor?: string;
  style?: ViewStyle;
}

// Animated Progress Bar
export const ProgressBarAnimated: React.FC<ProgressBarAnimatedProps> = ({
  progress,
  duration = 500,
  height = 4,
  color = lightTheme.colors.primary,
  backgroundColor = '#E0E0E0',
  style,
}) => {
  const widthAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(widthAnim, {
      toValue: progress,
      duration,
      useNativeDriver: false,
    }).start();
  }, [progress, widthAnim, duration]);

  const width = widthAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View
      style={[
        {
          height,
          backgroundColor,
          borderRadius: height / 2,
          overflow: 'hidden',
        },
        style,
      ]}
    >
      <Animated.View
        style={{
          height: '100%',
          width,
          backgroundColor: color,
          borderRadius: height / 2,
        }}
      />
    </View>
  );
};

interface FlipCardProps {
  frontComponent: ReactNode;
  backComponent: ReactNode;
  isFlipped: boolean;
  duration?: number;
  style?: ViewStyle;
}

// Flip Card Animation
export const FlipCard: React.FC<FlipCardProps> = ({
  frontComponent,
  backComponent,
  isFlipped,
  duration = 500,
  style,
}) => {
  const flipAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(flipAnim, {
      toValue: isFlipped ? 1 : 0,
      duration,
      useNativeDriver: true,
    }).start();
  }, [isFlipped, flipAnim, duration]);

  const frontRotateY = flipAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const backRotateY = flipAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['180deg', '360deg'],
  });

  const frontOpacity = flipAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 0, 0],
  });

  const backOpacity = flipAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0, 0, 1],
  });

  return (
    <View style={style}>
      <Animated.View
        style={[
          {
            position: 'absolute',
            width: '100%',
            height: '100%',
            backfaceVisibility: 'hidden',
            transform: [{ rotateY: frontRotateY }],
            opacity: frontOpacity,
          },
        ]}
      >
        {frontComponent}
      </Animated.View>
      <Animated.View
        style={[
          {
            width: '100%',
            height: '100%',
            backfaceVisibility: 'hidden',
            transform: [{ rotateY: backRotateY }],
            opacity: backOpacity,
          },
        ]}
      >
        {backComponent}
      </Animated.View>
    </View>
  );
};
