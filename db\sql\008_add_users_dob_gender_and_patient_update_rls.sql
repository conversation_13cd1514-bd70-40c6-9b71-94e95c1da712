-- 008_add_users_dob_gender_and_patient_update_rls.sql
-- Purpose:
-- 1) Add missing columns used by web admin Patients page
--    - date_of_birth (DATE)
--    - gender (VARCHAR)
-- 2) Ensure RLS allows Admin/Staff/Receptionist to UPDATE patient rows
--    (so reception can edit patient profiles).
--
-- Idempotent: Uses IF NOT EXISTS guards.

BEGIN;

-- Add columns if they don't exist
ALTER TABLE public.users
  ADD COLUMN IF NOT EXISTS date_of_birth DATE;

ALTER TABLE public.users
  ADD COLUMN IF NOT EXISTS gender VARCHAR(20);

-- Optional: avatar_url exists in many templates; leaving as-is since harmless
-- If you truly want to remove it later: ALTER TABLE public.users DROP COLUMN avatar_url;

-- Ensure RLS is enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Allow Admin/Staff/Receptionist to UPDATE patient profiles
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename  = 'users'
      AND policyname = 'users_update_patients_by_web_roles'
  ) THEN
    CREATE POLICY users_update_patients_by_web_roles
    ON public.users
    FOR UPDATE
    TO authenticated
    USING (
      -- Target row must be a patient
      role = 'patient'
      -- Caller must be one of these roles
      AND EXISTS (
        SELECT 1 FROM public.users me
        WHERE me.id = auth.uid()
          AND me.role IN ('admin','staff','receptionist')
      )
    )
    WITH CHECK (
      role = 'patient'
      AND EXISTS (
        SELECT 1 FROM public.users me
        WHERE me.id = auth.uid()
          AND me.role IN ('admin','staff','receptionist')
      )
    );
  END IF;
END $$;

COMMIT;

